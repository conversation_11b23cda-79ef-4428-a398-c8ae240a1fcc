<!-- Drop Shadow Controls -->
<div class="shadow-controls">
    <div class="control-group">
        <label for="shadowColor">Shadow Color:</label>
        <div class="simplified-color-picker">
            <input type="color" id="shadowColor" value="#727272">
        </div>
    </div>
    
    <div class="control-group">
        <label for="shadowOpacity">Opacity:</label>
        <div class="slider-container">
            <input type="range" id="shadowOpacity" class="slider" min="0" max="100" value="100" step="1">
            <span class="slider-value" id="shadowOpacityValue">100%</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="shadowDistance">Distance:</label>
        <div class="slider-container">
            <input type="range" id="shadowDistance" class="slider" min="0" max="100" value="100" step="1">
            <span class="slider-value" id="shadowDistanceValue">100</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="shadowAngle">Angle:</label>
        <div class="slider-container">
            <input type="range" id="shadowAngle" class="slider" min="-180" max="180" value="-58" step="1">
            <span class="slider-value" id="shadowAngleValue">-58°</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="shadowBlur">Blur:</label>
        <div class="slider-container">
            <input type="range" id="shadowBlur" class="slider" min="0" max="50" value="5" step="1">
            <span class="slider-value" id="shadowBlurValue">5</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="shadowOutlineWidth">Outline Width:</label>
        <div class="slider-container">
            <input type="range" id="shadowOutlineWidth" class="slider" min="0" max="20" value="0" step="1">
            <span class="slider-value" id="shadowOutlineWidthValue">0</span>
        </div>
    </div>
</div>
