<!-- Line Shadow Controls -->
<div class="line-shadow-controls">
    <div class="control-group">
        <label for="lineShadowColor">Shadow Color:</label>
        <div class="simplified-color-picker">
            <input type="color" id="lineShadowColor" value="#000000">
        </div>
    </div>
    
    <div class="control-group">
        <label for="lineShadowOpacity">Opacity:</label>
        <div class="slider-container">
            <input type="range" id="lineShadowOpacity" class="slider" min="0" max="100" value="100" step="1">
            <span class="slider-value" id="lineShadowOpacityValue">100%</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="layerDistance">Distance:</label>
        <div class="slider-container">
            <input type="range" id="layerDistance" class="slider" min="1" max="20" value="5" step="1">
            <span class="slider-value" id="layerDistanceValue">5</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="lineShadowAngle">Angle:</label>
        <div class="slider-container">
            <input type="range" id="lineShadowAngle" class="slider" min="0" max="360" value="45" step="1">
            <span class="slider-value" id="lineShadowAngleValue">45°</span>
        </div>
    </div>
</div>
