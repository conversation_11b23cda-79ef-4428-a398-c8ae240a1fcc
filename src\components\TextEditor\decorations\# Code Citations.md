# Code Citations

## License: unknown
https://github.com/jtwd/react-ui-library/tree/344524a42e9de11dc0a4b1a005eb2e60af7a1b18/src/components/_theme/utils/utils.colors.js

```
hex, alpha = 1) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7),
```


## License: unknown
https://github.com/loveCamel/LightZoom/tree/553f67f58f0354199f224fe2beaed1ba1f889241/src/ts/LightZoom.ts

```
const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(
```


## License: unknown
https://github.com/joshwcomeau/plot/tree/1cf3e0e1a143469c72d104dba07b807b7f590bbc/src/utils.js

```
parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})
```

