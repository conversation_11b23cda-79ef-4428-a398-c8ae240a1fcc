/* Text Editor CSS Styles */

/* Control Groups */
.editor-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 20px;
    display: flex;
    gap: 2rem;
}

.canvas-container {
    flex: 1;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
    min-height: 600px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: auto; /* Changed from 'hidden' to 'auto' to allow scrolling */
    max-height: 80vh; /* Limit height to 80% of viewport height */
    max-width: 100%; /* Limit width to 100% of parent */
}

.canvas-wrapper {
    position: relative;
    overflow: hidden;
    transform-origin: center center;
    transition: transform 0.2s ease-out;
    cursor: default; /* Changed from 'grab' to 'default' to prevent dragging */
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.canvas-wrapper.hand-mode {
    cursor: grab;
}

.canvas-wrapper.hand-mode:active {
    cursor: grabbing;
}

.canvas-wrapper.arrow-mode {
    cursor: default;
}

.canvas-wrapper:active {
    cursor: default; /* Changed from 'grabbing' to 'default' */
}

#editor-canvas {
    max-width: 100%;
    max-height: 100%;
    background: #2a2a2a;
    border: 2px solid #333;
    display: block;
    transform-origin: center center; /* Ensure zoom happens from center */
}

.zoom-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    border-radius: 25px;
    padding: 8px 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
}

.zoom-button {
    background: none;
    border: none;
    color: #555;
    font-size: 18px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s, background-color 0.2s;
    border-radius: 4px;
}

.zoom-button:hover {
    color: #000;
    background-color: rgba(0, 0, 0, 0.05);
}

.zoom-button.active {
    color: #fff;
    background-color: #1E90FF;
}

#handModeBtn, #arrowModeBtn {
    margin-left: 2px;
    margin-right: 2px;
}

.zoom-level {
    margin: 0 15px;
    font-size: 14px;
    font-weight: 600;
    width: 50px;
    text-align: center;
}

.zoom-separator {
    height: 20px;
    width: 1px;
    background: #ddd;
    margin: 0 5px;
}

.tools-panel {
    width: 300px;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
}

.tool-section {
    margin-bottom: 20px;
}

.tool-section h3 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: #fff;
}

.color-picker {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.color-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
}

.color-option.active {
    border-color: #fff;
}

.button-group {
    display: flex; 
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.control-button {
    background: #4CAF50;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-button:hover {
    background: #333;
}

.control-button.primary {
    background: #0066ff;
}

.control-button.primary:hover {
    background: #0052cc;
}

.control-button.small {
    padding: 8px;
    width: 36px;
    height: 36px;
    justify-content: center;
}

.control-button.small.active {
    background: #0066ff;
}

.control-button.danger {
    background: #dc3545;
}

.control-button.danger:hover {
    background: #bd2130;
}

.font-select {
    width: 100%;
    padding: 8px;
    background: #2a2a2a;
    border: 1px solid #333;
    color: white;
    border-radius: 4px;
    margin-bottom: 10px;
}

.font-select option {
    padding: 8px;
    font-size: 16px;
    background: #2a2a2a;
    color: white;
}

.font-preview {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.font-preview:hover {
    background-color: #3a3a3a;
}

.font-name {
    font-size: 14px;
    color: #888;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.slider {
    flex: 1;
    -webkit-appearance: none;
    appearance: none;
    height: 4px;
    background: #2a2a2a;
    border-radius: 2px;
    outline: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: #0066ff;
    border-radius: 50%;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #0066ff;
    border-radius: 50%;
    cursor: pointer;
}

.slider-value {
    min-width: 40px;
    color: #fff;
    font-size: 14px;
}

.font-controls {
    display: flex;
    gap: 8px;
    margin: 10px 0;
}

.font-size-input {
    width: 60px;
    padding: 8px;
    background: #2a2a2a;
    border: 1px solid #333;
    color: white;
    border-radius: 4px;
}

.font-menu-wrapper {
    position: relative;
    width: 100%;
    margin: 10px 0;
}

.font-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
}

.font-menu-header .selected-font {
    font-size: 20px;
    line-height: 1.2;
}

.font-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 300px;
    overflow-y: auto;
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-radius: 4px;
    margin-top: 4px;
    display: none;
    z-index: 1000;
}

.font-menu.show {
    display: block;
}

.font-option {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #3a3a3a;
}

.font-option:last-child {
    border-bottom: none;
}

.font-option:hover {
    background: #3a3a3a;
}

.font-option .preview-text {
    font-size: 20px;
    line-height: 1.2;
    margin-bottom: 4px;
}

.font-option .font-name {
    font-size: 12px;
    color: #999;
}

.warp-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.warp-controls label {
    color: #fff;
    font-size: 14px;
}

.stroke-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stroke-controls label {
    color: #fff;
    font-size: 14px;
    margin-bottom: 4px;
}

.canvas-menu {
    position: absolute;
    right: 20px;
    top: 20px;
    background: rgba(88, 88, 88, 0.95);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.canvas-menu button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #000000;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
    width: 100%;
    text-align: left;
}

.canvas-menu button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.canvas-menu button img {
    width: 20px;
    height: 20px;
    opacity: 0.8;
}

.canvas-menu button:hover img {
    opacity: 1;
}



.brush-size-control {
    margin: 10px 0;
}

.brush-size-control label {
    color: white;
    display: block;
    margin-bottom: 5px;
}

.brush-size-control input {
    width: 100%;
}

.model-selector-container {
    margin-bottom: 15px;
}

.model-selector-container label {
    display: block;
    margin-bottom: 5px;
    color: #fff;
    font-size: 14px;
}

#modelSelector {
    width: 100%;
    padding: 8px;
    background: #2a2a2a;
    border: 1px solid #333;
    color: white;
    border-radius: 4px;
    cursor: pointer;
}

#modelSelector option {
    padding: 8px;
    background: #2a2a2a;
}

#modelSelector:hover {
    border-color: #444;
}

/* Toast notification styles */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 1000;
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.toast.show {
    opacity: 1;
}

.toast.error {
    background: rgba(220, 53, 69, 0.9);
}

.toast.success {
    background: rgba(40, 167, 69, 0.9);
}

.toast.info {
    background: rgba(0, 123, 255, 0.9);
}

/* Scrollbar styles for the font menu */
.font-menu::-webkit-scrollbar {
    width: 8px;
}

.font-menu::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.font-menu::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
}

.font-menu::-webkit-scrollbar-thumb:hover {
    background: #444;
}

.slider-controls {
    margin-top: 20px;
}

.slider-group {
    margin-bottom: 20px;
}

.color-controls {
    margin-top: 20px;
}

.color-group {
    margin-bottom: 20px;
}

#imageInput {
    display: none;
}

.upload-area {
    border: 2px dashed #333;
    padding: 20px;
    text-align: center;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 15px;
}

.upload-area:hover {
    border-color: #0066ff;
}

.brush-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.color-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.current-color {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #333;
    background: #ffffff;
}

.brush-controls .control-button {
    flex: 1;
}

.brush-controls .slider-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.brush-controls label {
    color: #fff;
    font-size: 0.9rem;
}

.brush-controls input[type="range"] {
    width: 100%;
    margin: 5px 0;
}

.mode-controls {
    width: 100%;
}

.blend-mode-select {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    background-color: #333;
    color: white;
    border: 1px solid #555;
    margin-top: 8px;
}

.text-action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    justify-content: flex-start;
}

.text-action-buttons .control-button {
    flex: 0 0 auto;
}

.advanced-color-picker {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 0;
}

.color-preview-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #333;
    background: #ff0000;
}

.eyedropper-btn {
    background: #4CAF50;
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.eyedropper-btn:hover {
    background: #45a049;
}

.color-input-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

input[type="color"] {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    padding: 0;
}

.rgb-inputs {
    display: flex;
    gap: 10px;
    justify-content: space-between;
}

.rgb-input {
    display: flex;
    flex-direction: column;
    gap: 5px;
    flex: 1;
}

.rgb-input input[type="number"] {
    width: 100%;
    padding: 8px;
    background: #2a2a2a;
    border: 1px solid #333;
    color: white;
    border-radius: 4px;
}

.rgb-input label {
    color: #fff;
    font-size: 14px;
    text-align: center;
}

.simplified-color-picker {
    display: flex;
    align-items: center;
    padding: 10px 0;
    width: 100%;
}

.simplified-color-picker input[type="color"] {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 0;
    cursor: pointer;
    padding: 0;
    outline: none;
    background: none;
}

/* Remove default white border in some browsers */
.simplified-color-picker input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
}

.simplified-color-picker input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: 0;
}

/* For Firefox */
.simplified-color-picker input[type="color"]::-moz-color-swatch {
    border: none;
    border-radius: 0;
}

.model-selector-container {
    margin-bottom: 15px;
}

.model-selector-container label {
    display: block;
    margin-bottom: 5px;
    color: #fff;
    font-size: 14px;
}

#modelSelector {
    width: 100%;
    padding: 8px;
    background: #2a2a2a;
    border: 1px solid #333;
    color: white;
    border-radius: 4px;
    cursor: pointer;
}

#modelSelector option {
    padding: 8px;
    background: #2a2a2a;
}

#modelSelector:hover {
    border-color: #444;
}

/* Toast notification styles */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 1000;
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.toast.show {
    opacity: 1;
}

.toast.error {
    background: rgba(220, 53, 69, 0.9);
}

.toast.success {
    background: rgba(40, 167, 69, 0.9);
}

.toast.info {
    background: rgba(0, 123, 255, 0.9);
}

/* Scrollbar styles for the font menu */
.font-menu::-webkit-scrollbar {
    width: 8px;
}

.font-menu::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.font-menu::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
}

.font-menu::-webkit-scrollbar-thumb:hover {
    background: #444;
}

.slider-controls {
    margin-top: 20px;
}

.slider-group {
    margin-bottom: 20px;
}

.color-controls {
    margin-top: 20px;
}

.color-group {
    margin-bottom: 20px;
}

#imageInput {
    display: none;
}

.upload-area {
    border: 2px dashed #333;
    padding: 20px;
    text-align: center;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 15px;
}

.upload-area:hover {
    border-color: #0066ff;
}

.brush-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.color-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.current-color {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #333;
    background: #ffffff;
}

.brush-controls .control-button {
    flex: 1;
}

.brush-controls .slider-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.brush-controls label {
    color: #fff;
    font-size: 0.9rem;
}

.brush-controls input[type="range"] {
    width: 100%;
    margin: 5px 0;
}

.mode-controls {
    width: 100%;
}

.blend-mode-select {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    background-color: #333;
    color: white;
    border: 1px solid #555;
    margin-top: 8px;
}

.text-action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    justify-content: flex-start;
}

.text-action-buttons .control-button {
    flex: 0 0 auto;
}

.advanced-color-picker {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 0;
}

.color-preview-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #333;
    background: #ff0000;
}

.eyedropper-btn {
    background: #4CAF50;
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.eyedropper-btn:hover {
    background: #45a049;
}

.color-input-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.rgb-inputs {
    display: flex;
    gap: 10px;
    justify-content: space-between;
}

.rgb-input {
    display: flex;
    flex-direction: column;
    gap: 5px;
    flex: 1;
}

.rgb-input input[type="number"] {
    width: 100%;
    padding: 8px;
    background: #2a2a2a;
    border: 1px solid #333;
    color: white;
    border-radius: 4px;
}

.rgb-input label {
    color: #fff;
    font-size: 14px;
    text-align: center;
}
