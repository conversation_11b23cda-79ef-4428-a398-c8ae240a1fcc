import express from 'express';
const router = express.Router();
// Assuming DesignTemplate model uses module.exports, adjust if needed
// If DesignTemplate also uses ES modules, change the import accordingly.
// We might need to check models/DesignTemplate.js if this causes issues.
import DesignTemplate from '../models/DesignTemplate.js';
import { auth } from '../middleware/auth.js'; // Use the correct exported function 'auth'
import { storage } from '../utils/storage.js';

// @desc    Save a new design template
// @route   POST /api/design-templates
// @access  Private (requires login)
router.post('/', auth, async (req, res) => { // Use 'auth' middleware
    try {
        const {
            name,
            inspirationId,
            previewImageUrl,
            artboard,
            canvasObjects,
            adminData,
            editorState
        } = req.body;

        // Basic validation
        if (!previewImageUrl || !artboard || !canvasObjects) {
            return res.status(400).json({ message: 'Missing required template data (previewImageUrl, artboard, canvasObjects).' });
        }
        // Check specific artboard properties for validity
        if (typeof artboard.x !== 'number' || typeof artboard.y !== 'number' || typeof artboard.width !== 'number' || typeof artboard.height !== 'number') {
             return res.status(400).json({ message: 'Invalid artboard data properties.' });
        }


        const newTemplate = new DesignTemplate({
            name: name || 'Untitled Template', // Default name if not provided
            inspirationId: inspirationId || null,
            previewImageUrl,
            artboard,
            canvasObjects,
            adminData,
            editorState: editorState || {}, // Include editor state
            userId: req.user.id // Associate with the logged-in user
        });

        const savedTemplate = await newTemplate.save();

        console.log('Template saved successfully:', savedTemplate._id);
        res.status(201).json(savedTemplate);

    } catch (err) {
        console.error('Error saving template:', err);
        res.status(500).json({ message: 'Server error saving template', error: err.message });
    }
});

// @desc    Get all design templates (potentially for the logged-in user)
// @route   GET /api/design-templates
// @access  Private
router.get('/', auth, async (req, res) => { // Use 'auth' middleware
    try {
        // Fetch templates for the current user, sorted by creation date
        const templates = await DesignTemplate.find({ userId: req.user.id })
                                            .sort({ createdAt: -1 })
                                            .lean(); // Use .lean() for faster read-only operations

        res.status(200).json(templates);
    } catch (err) {
        console.error('Error fetching templates:', err);
        res.status(500).json({ message: 'Server error fetching templates', error: err.message });
    }
});

// @desc    Get a single design template by ID
// @route   GET /api/design-templates/:id
// @access  Private
router.get('/:id', auth, async (req, res) => { // Use 'auth' middleware
    try {
        const template = await DesignTemplate.findById(req.params.id).lean();

        if (!template) {
            return res.status(404).json({ message: 'Template not found' });
        }

        // Optional: Check if the template belongs to the requesting user
        if (template.userId && template.userId.toString() !== req.user.id.toString()) {
             return res.status(403).json({ message: 'User not authorized to access this template' });
        }

        res.status(200).json(template);
    } catch (err) {
        console.error('Error fetching template by ID:', err);
        // Handle potential CastError if ID format is invalid
        if (err.name === 'CastError') {
             return res.status(400).json({ message: 'Invalid template ID format' });
        }
        res.status(500).json({ message: 'Server error fetching template', error: err.message });
    }
});

// @desc    Delete a design template by ID
// @route   DELETE /api/design-templates/:id
// @access  Private
router.delete('/:id', auth, async (req, res) => { // Use 'auth' middleware
    try {
        const template = await DesignTemplate.findById(req.params.id);

        if (!template) {
            return res.status(404).json({ message: 'Template not found' });
        }

        // Ensure the user owns the template before deleting
        if (template.userId && template.userId.toString() !== req.user.id.toString()) {
            return res.status(403).json({ message: 'User not authorized to delete this template' });
        }

        await DesignTemplate.deleteOne({ _id: req.params.id }); // Use deleteOne

        console.log('Template deleted successfully:', req.params.id);
        res.status(200).json({ message: 'Template deleted successfully' });

    } catch (err) {
        console.error('Error deleting template:', err);
         if (err.name === 'CastError') {
             return res.status(400).json({ message: 'Invalid template ID format' });
        }
        res.status(500).json({ message: 'Server error deleting template', error: err.message });
    }
});

// @desc    Update a design template by ID
// @route   PUT /api/design-templates/:id
// @access  Private
router.put('/:id', auth, async (req, res) => {
    try {
        const template = await DesignTemplate.findById(req.params.id);

        if (!template) {
            return res.status(404).json({ message: 'Template not found' });
        }

        // Ensure the user owns the template before updating
        if (template.userId && template.userId.toString() !== req.user.id.toString()) {
            return res.status(403).json({ message: 'User not authorized to update this template' });
        }

        // Extract fields that can be updated
        const {
            name,
            previewImageUrl,
            artboard,
            adminData,
            canvasObjects,
            editorState,
            originalPalette
        } = req.body;

        // Log what we're about to update
        console.log('About to update template with originalPalette:', originalPalette);

        console.log('===== TEMPLATE UPDATE DEBUG =====');
        console.log('Template ID:', req.params.id);
        console.log('Request body keys:', Object.keys(req.body));
        console.log('Request body previewImageUrl:', req.body.previewImageUrl);
        console.log('Request body originalPalette:', req.body.originalPalette);
        console.log('Extracted previewImageUrl:', previewImageUrl);
        console.log('Extracted originalPalette:', originalPalette);
        console.log('Update data being applied:', {
            id: req.params.id,
            name: name,
            previewImageUrl: previewImageUrl,
            originalPalette: originalPalette,
            hasArtboard: !!artboard,
            hasCanvasObjects: !!canvasObjects,
            hasAdminData: !!adminData
        });
        console.log('Old template previewImageUrl:', template.previewImageUrl);
        console.log('Old template originalPalette:', template.originalPalette);

        // If updating preview image, try to delete the old one from storage
        if (previewImageUrl && template.previewImageUrl && template.previewImageUrl !== previewImageUrl) {
            try {
                console.log('Attempting to delete old preview image:', template.previewImageUrl);

                // Extract filename from the old URL
                // URL format: https://f005.backblazeb2.com/file/stickers-replicate-app/675f365a9f4fa90f4b5fcea9/1749669323111-jqas6.png
                const urlParts = template.previewImageUrl.split('/');
                const fileName = urlParts.slice(-2).join('/'); // Get "userId/filename.png"

                console.log('Extracted filename for deletion:', fileName);
                await storage.deleteFile(fileName);
                console.log('Old preview image deleted successfully');
            } catch (deleteError) {
                console.warn('Failed to delete old preview image (continuing with update):', deleteError.message);
                // Don't fail the update if image deletion fails
            }
        }

        // Use direct MongoDB update with $set operator
        const updateFields = {};
        if (name !== undefined) updateFields.name = name;
        if (previewImageUrl !== undefined) updateFields.previewImageUrl = previewImageUrl;
        if (artboard !== undefined) updateFields.artboard = artboard;
        if (adminData !== undefined) updateFields.adminData = adminData;
        if (canvasObjects !== undefined) updateFields.canvasObjects = canvasObjects;
        if (editorState !== undefined) updateFields.editorState = editorState;

        // Always set originalPalette, even if empty
        updateFields.originalPalette = originalPalette || '';

        console.log('Updating with fields:', updateFields);

        const updatedTemplate = await DesignTemplate.findByIdAndUpdate(
            req.params.id,
            { $set: updateFields },
            { new: true, runValidators: false }
        );

        console.log('After update - updatedTemplate.originalPalette:', updatedTemplate.originalPalette);

        console.log('===== TEMPLATE UPDATE RESULT =====');
        console.log('Template updated successfully:', req.params.id);
        console.log('Updated template name:', updatedTemplate.name);
        console.log('Updated template previewImageUrl:', updatedTemplate.previewImageUrl);
        console.log('Updated template originalPalette:', updatedTemplate.originalPalette);
        console.log('Preview URL changed:', template.previewImageUrl !== updatedTemplate.previewImageUrl);
        console.log('Original palette changed:', template.originalPalette !== updatedTemplate.originalPalette);

        res.status(200).json(updatedTemplate);

    } catch (err) {
        console.error('Error updating template:', err);
        if (err.name === 'CastError') {
            return res.status(400).json({ message: 'Invalid template ID format' });
        }
        res.status(500).json({ message: 'Server error updating template', error: err.message });
    }
});

// @desc    Toggle published status of a design template
// @route   PATCH /api/design-templates/:id/publish
// @access  Private
router.patch('/:id/publish', auth, async (req, res) => {
    try {
        const template = await DesignTemplate.findById(req.params.id);

        if (!template) {
            return res.status(404).json({ message: 'Template not found' });
        }

        // Ensure the user owns the template before updating
        if (template.userId && template.userId.toString() !== req.user.id.toString()) {
            return res.status(403).json({ message: 'User not authorized to update this template' });
        }

        // Toggle the published status
        template.published = !template.published;
        await template.save();

        console.log(`Template ${req.params.id} published status changed to: ${template.published}`);
        res.status(200).json({
            message: `Template ${template.published ? 'published' : 'unpublished'} successfully`,
            published: template.published
        });

    } catch (err) {
        console.error('Error toggling template published status:', err);
        if (err.name === 'CastError') {
            return res.status(400).json({ message: 'Invalid template ID format' });
        }
        res.status(500).json({ message: 'Server error toggling published status', error: err.message });
    }
});

// DELETE /api/design-templates/:id - Delete a template
router.delete('/:id', auth, async (req, res) => {
    try {
        console.log('===== DELETE TEMPLATE REQUEST =====');
        console.log('Template ID to delete:', req.params.id);
        console.log('User ID:', req.user.id);

        // Find the template first to check ownership and get image URL
        const template = await DesignTemplate.findById(req.params.id);

        if (!template) {
            console.log('Template not found:', req.params.id);
            return res.status(404).json({ error: 'Template not found' });
        }

        // Check if user owns this template
        if (template.userId.toString() !== req.user.id.toString()) {
            console.log('User does not own this template');
            return res.status(403).json({ error: 'Not authorized to delete this template' });
        }

        console.log('Deleting template:', template.name);
        console.log('Template preview image:', template.previewImageUrl);

        // Delete the template from database
        await DesignTemplate.findByIdAndDelete(req.params.id);

        // Optionally delete the preview image from storage
        if (template.previewImageUrl) {
            try {
                // Extract filename from URL for deletion
                const urlParts = template.previewImageUrl.split('/');
                const fileName = urlParts.slice(-2).join('/'); // Get "userId/filename.png"
                console.log('Attempting to delete preview image:', fileName);
                await storage.deleteFile(fileName);
                console.log('Preview image deleted successfully');
            } catch (deleteError) {
                console.warn('Failed to delete preview image (template still deleted):', deleteError.message);
            }
        }

        console.log('Template deleted successfully:', req.params.id);
        res.status(200).json({
            message: 'Template deleted successfully',
            deletedId: req.params.id
        });

    } catch (error) {
        console.error('Error deleting template:', error);
        res.status(500).json({ error: 'Failed to delete template' });
    }
});

export default router; // Use ES module export
