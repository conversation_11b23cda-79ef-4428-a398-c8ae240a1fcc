<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Variant Test - Triple Outline Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .font-demo {
            font-size: 48px;
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border: 1px solid #ccc;
            text-align: center;
        }
        .controls {
            margin: 10px 0;
        }
        .controls select, .controls input {
            margin: 5px;
            padding: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .comparison {
            display: flex;
            gap: 20px;
        }
        .comparison > div {
            flex: 1;
            text-align: center;
        }
        .old-method {
            border: 2px solid #dc3545;
            padding: 10px;
            border-radius: 5px;
        }
        .new-method {
            border: 2px solid #28a745;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Font Variant Detection System - Triple Outline Fix</h1>
        <p>This test demonstrates the difference between using CSS bold/italic (which causes triple outlines) vs. actual font files (which fixes the issue).</p>
        
        <div class="test-section">
            <h2>System Status</h2>
            <div id="systemStatus" class="status">Checking system...</div>
        </div>
        
        <div class="test-section">
            <h2>Font Selection</h2>
            <div class="controls">
                <label>Font: 
                    <select id="fontSelect">
                        <option value="Arial">Arial</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Roboto">Roboto</option>
                    </select>
                </label>
                <label><input type="checkbox" id="boldCheck"> Bold</label>
                <label><input type="checkbox" id="italicCheck"> Italic</label>
            </div>
            <div id="availabilityStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>Comparison: CSS vs. Font Files</h2>
            <div class="comparison">
                <div class="old-method">
                    <h3>❌ Old Method (CSS Bold/Italic)</h3>
                    <p>Causes triple outlines with some fonts</p>
                    <div id="cssDemo" class="font-demo">DESIGN</div>
                    <small>Uses font-weight: bold; font-style: italic;</small>
                </div>
                <div class="new-method">
                    <h3>✅ New Method (Font Files)</h3>
                    <p>Uses actual font variant files</p>
                    <div id="fontFileDemo" class="font-demo">DESIGN</div>
                    <small>Uses actual Bold.ttf, Italic.ttf files</small>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Available Font Variants</h2>
            <div id="fontList" class="status"></div>
        </div>
    </div>

    <script src="/js/font-variant-detector.js"></script>
    <script>
        let detector = null;
        
        async function initTest() {
            const statusEl = document.getElementById('systemStatus');
            
            try {
                statusEl.textContent = 'Initializing font variant detector...';
                statusEl.className = 'status warning';
                
                detector = window.fontVariantDetector;
                if (!detector) {
                    throw new Error('Font variant detector not found');
                }
                
                await detector.initialize();
                
                statusEl.textContent = '✅ Font variant detection system active!';
                statusEl.className = 'status success';
                
                updateFontList();
                setupControls();
                updateDemo();
                
            } catch (error) {
                statusEl.textContent = `❌ Error: ${error.message}`;
                statusEl.className = 'status error';
                console.error('Test initialization failed:', error);
            }
        }
        
        function updateFontList() {
            const fontListEl = document.getElementById('fontList');
            
            if (!detector || !detector.availableFonts) {
                fontListEl.textContent = 'No font data available';
                return;
            }
            
            let html = '<h4>Detected Font Families:</h4>';
            for (const [family, variants] of detector.availableFonts.entries()) {
                const available = [];
                if (variants.regular) available.push('Regular');
                if (variants.bold) available.push('Bold');
                if (variants.italic) available.push('Italic');
                if (variants.boldItalic) available.push('Bold Italic');
                
                html += `<div><strong>${family}:</strong> ${available.join(', ')}</div>`;
            }
            
            fontListEl.innerHTML = html;
        }
        
        function setupControls() {
            const fontSelect = document.getElementById('fontSelect');
            const boldCheck = document.getElementById('boldCheck');
            const italicCheck = document.getElementById('italicCheck');
            
            fontSelect.addEventListener('change', updateDemo);
            boldCheck.addEventListener('change', updateDemo);
            italicCheck.addEventListener('change', updateDemo);
        }
        
        function updateDemo() {
            const fontSelect = document.getElementById('fontSelect');
            const boldCheck = document.getElementById('boldCheck');
            const italicCheck = document.getElementById('italicCheck');
            const cssDemo = document.getElementById('cssDemo');
            const fontFileDemo = document.getElementById('fontFileDemo');
            const availabilityStatus = document.getElementById('availabilityStatus');
            
            const fontFamily = fontSelect.value;
            const bold = boldCheck.checked;
            const italic = italicCheck.checked;
            
            // Update CSS demo (old method)
            cssDemo.style.fontFamily = `"${fontFamily}", sans-serif`;
            cssDemo.style.fontWeight = bold ? 'bold' : 'normal';
            cssDemo.style.fontStyle = italic ? 'italic' : 'normal';
            
            // Update font file demo (new method)
            fontFileDemo.style.fontFamily = `"${fontFamily}", sans-serif`;
            fontFileDemo.style.fontWeight = 'normal'; // Always normal - use actual font files
            fontFileDemo.style.fontStyle = 'normal';   // Always normal - use actual font files
            
            // Check availability and update status
            if (detector && detector.initialized) {
                let variant = 'regular';
                if (bold && italic) variant = 'boldItalic';
                else if (bold) variant = 'bold';
                else if (italic) variant = 'italic';
                
                const available = detector.isVariantAvailable(fontFamily, variant);
                
                // Update checkbox states
                const boldAvailable = detector.isVariantAvailable(fontFamily, 'bold') || 
                                    detector.isVariantAvailable(fontFamily, 'boldItalic');
                const italicAvailable = detector.isVariantAvailable(fontFamily, 'italic') || 
                                      detector.isVariantAvailable(fontFamily, 'boldItalic');
                
                boldCheck.disabled = !boldAvailable;
                italicCheck.disabled = !italicAvailable;
                
                if (available) {
                    availabilityStatus.textContent = `✅ ${fontFamily} ${variant} variant is available as font file`;
                    availabilityStatus.className = 'status success';
                    
                    // Load the actual font variant
                    detector.loadFontVariant(fontFamily, bold, italic);
                } else {
                    availabilityStatus.textContent = `⚠️ ${fontFamily} ${variant} variant not available - using fallback`;
                    availabilityStatus.className = 'status warning';
                }
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
