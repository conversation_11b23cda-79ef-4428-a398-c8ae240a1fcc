<!-- Detailed 3D Shadow Controls -->
<div class="detailed-3d-controls">
    <div class="control-group">
        <label for="detailed3DPrimaryColor">Primary Shadow Color:</label>
        <div class="simplified-color-picker">
            <input type="color" id="detailed3DPrimaryColor" value="#000000">
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DPrimaryOpacity">Primary Opacity:</label>
        <div class="slider-container">
            <input type="range" id="detailed3DPrimaryOpacity" class="slider" min="0" max="100" value="100" step="1">
            <span class="slider-value" id="detailed3DPrimaryOpacityValue">100%</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DOffset">Extrude Distance:</label>
        <div class="slider-container">
            <input type="range" id="detailed3DOffset" class="slider" min="0" max="100" value="36" step="1">
            <span class="slider-value" id="detailed3DOffsetValue">36</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DAngle">Angle:</label>
        <div class="slider-container">
            <input type="range" id="detailed3DAngle" class="slider" min="-180" max="180" value="-63" step="1">
            <span class="slider-value" id="detailed3DAngleValue">-63°</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DBlur">Blur:</label>
        <div class="slider-container">
            <input type="range" id="detailed3DBlur" class="slider" min="0" max="50" value="5" step="1">
            <span class="slider-value" id="detailed3DBlurValue">5</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DOutlineWidth">Primary Outline Width:</label>
        <div class="slider-container">
            <input type="range" id="detailed3DOutlineWidth" class="slider" min="0" max="50" value="16" step="1">
            <span class="slider-value" id="detailed3DOutlineWidthValue">16</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DSecondaryColor">Secondary Outline Color:</label>
        <div class="simplified-color-picker">
            <input type="color" id="detailed3DSecondaryColor" value="#00FF66">
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DSecondaryOpacity">Secondary Opacity:</label>
        <div class="slider-container">
            <input type="range" id="detailed3DSecondaryOpacity" class="slider" min="0" max="100" value="100" step="1">
            <span class="slider-value" id="detailed3DSecondaryOpacityValue">100%</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DSecondaryWidth">Secondary Outline Width:</label>
        <div class="slider-container">
            <input type="range" id="detailed3DSecondaryWidth" class="slider" min="1" max="20" value="4" step="1">
            <span class="slider-value" id="detailed3DSecondaryWidthValue">4</span>
        </div>
    </div>
    
    <div class="control-group">
        <label for="detailed3DSecondaryOffset">Secondary Outline Offset:</label>
        <div class="slider-container">
            <input type="range" id="detailed3DSecondaryOffset" class="slider" min="0" max="50" value="10" step="1">
            <span class="slider-value" id="detailed3DSecondaryOffsetValue">10</span>
        </div>
    </div>
</div>
