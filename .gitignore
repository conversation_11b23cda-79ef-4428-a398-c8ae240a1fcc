# Dependencies
node_modules/

# Environment variables
.env
.env.*
!.env.example

# Logs
logs/
*.log
npm-debug.log*
public/logs*.txt

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/

# Temporary files
tmp/
temp/
temp_*.js
temp_*.html

# Uploads and generated files
uploads/
public/uploads/
public/storage/

# Test files
test-*.js
test-*.cjs
simple-mongo-test.js
check-mongodb.js
checkCurrentDB.js
cloneDatabase.js
cloneTestDb*.js
compareDBs.js

# Backup files
*-original.js
*-fix.js
*.bak
*.backup

# IDE - VSCode
.vscode/
*.code-workspace
.windsurfrules

# System Files
.DS_Store
Thumbs.db

# Development files
vacio - Copy/
