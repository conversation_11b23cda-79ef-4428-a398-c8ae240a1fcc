<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Template Image Generator</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="/styles.css">
    <style>
        /* Copy of styles from face-sticker.html (adjust as necessary) */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', sans-serif;
            background: #111;
            color: #fff;
        }

        .container {
            max-width: 800px;
            margin: 1rem auto 2rem;
            padding: 0 1rem;
        }

        .model-selector-section {
            margin-bottom: 1rem;
        }

        .model-selector-section label {
            font-size: 1rem;
            margin-right: 0.5rem;
        }

        .model-selector-section select {
            padding: 0.5rem;
            border-radius: 4px;
            border: 1px solid #333;
            background: #1a1a1a;
            color: #fff;
        }

        .prompt-section {
            margin-bottom: 1.5rem;
        }

        textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #333;
            border-radius: 8px;
            min-height: 80px;
            font-family: inherit;
            background: #1a1a1a;
            color: #fff;
            resize: vertical;
            margin-bottom: 1rem;
        }

        .upload-section {
            margin-bottom: 1rem;
        }

        .upload-bar {
            background-color: #1a1a1a;
            padding: 0.5rem;
            border: 1px solid #333;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .upload-icon {
            margin-right: 0.5rem;
        }

        .thumbnail {
            margin-left: 1rem;
        }

        .filename {
            font-size: 0.875rem;
            color: #666;
        }

        .style-section {
            margin-bottom: 1rem;
        }

        .style-section .section-title {
            margin-bottom: 0.5rem;
        }

        .generate-button {
            background-color: #333;
            color: #fff;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
        }

        .generate-button i {
            margin-right: 0.5rem;
        }

        .result {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div id="topbar"></div>

    <div class="container">
        <h3 class="section-title">Enter the Keyword for your Sticker</h3>
        
        <!-- Model dropdown section -->
        <div class="model-selector-section">
            <label for="modelSelect">Choose a Model:</label>
            <select id="modelSelect">
                <option>Loading models...</option>
            </select>
        </div>

        <div class="prompt-section">
            <textarea id="prompt" placeholder="Enter your prompt..."></textarea>
        </div>

        <!-- Added face-sticker.html components -->
        <div class="upload-section">
            <div class="upload-bar" id="uploadArea">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <span class="upload-text">Upload Image</span>
                <div class="thumbnail" id="thumbnailContainer">
                    <img id="imagePreview" alt="Preview">
                </div>
            </div>
            <div class="filename" id="filename"></div>
        </div>

        <div class="style-section">
            <h4 class="section-title">Select Style</h4>
            <div id="styleCarousel"></div>
        </div>

        <button class="generate-button" id="generateButton">
            <i class="fas fa-magic"></i> Generate Now
        </button>

        <div class="result" id="generationResults"></div>
    </div>

    <script type="module">
        import { createTopbar } from '/js/components/Topbar.js';
        import '/js/components/StyleSelector.js';
        import '/js/components/GenerationCard.js';
        import '/js/components/CollectionModal.js';

        // Initialize topbar
        const topbarContainer = document.getElementById('topbar');
        createTopbar(topbarContainer);

        // Initialize style carousel by rendering the style-selector component
        const styleCarousel = document.getElementById('styleCarousel');
        styleCarousel.innerHTML = '<style-selector></style-selector>';

        // Image upload functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        uploadArea.addEventListener('click', () => fileInput.click());

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    document.getElementById('imagePreview').src = event.target.result;
                    document.getElementById('thumbnailContainer').classList.add('visible');
                    document.getElementById('filename').textContent = file.name;
                    document.getElementById('filename').classList.add('visible');
                };
                reader.readAsDataURL(file);
            }
        });

        // Generate button click handler
        document.getElementById('generateButton').addEventListener('click', async () => {
            console.log('Generate button clicked');
            // TODO: Add image generation logic here
        });
    </script>
</body>
</html>
