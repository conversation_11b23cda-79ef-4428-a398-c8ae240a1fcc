<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sticker Generator</title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        .auth-container {
            max-width: 400px;
            margin: 100px auto;
            padding: 2rem;
            background: #2a2a2a;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .auth-header h1 {
            color: #fff;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .auth-nav {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .auth-nav a {
            color: #666;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .auth-nav a.active {
            color: #fff;
            border-bottom: 2px solid #4CAF50;
        }

        .auth-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            color: #fff;
            font-size: 0.9rem;
        }

        .form-group input {
            padding: 0.75rem;
            border: 1px solid #494949;
            border-radius: 4px;
            background: #1a1a1a;
            color: #fff;
            font-size: 1rem;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .auth-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .auth-button:hover {
            background: #45a049;
        }

        .auth-links {
            margin-top: 1rem;
            text-align: center;
            color: #666;
        }

        .auth-links a {
            color: #4CAF50;
            text-decoration: none;
        }

        .auth-links a:hover {
            text-decoration: underline;
        }

        .error-message {
            color: #ff4444;
            background: rgba(255, 68, 68, 0.1);
            padding: 0.75rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            display: none;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1>Welcome Back</h1>
            <p>Log in to your account</p>
        </div>

        <nav class="auth-nav">
            <a href="/login" class="active">Login</a>
            <a href="/register">Register</a>
        </nav>

        <div class="error-message" id="errorMessage"></div>

        <form class="auth-form" id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="auth-button">Log In</button>
        </form>

        <div class="auth-links">
            <p>Don't have an account? <a href="/register">Sign up</a></p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'Login failed');
                }
                
                console.log('Login successful, redirecting to:', data.redirect);
                
                // Force a hard redirect to the specified URL
                window.location.replace(data.redirect || '/');
                
            } catch (error) {
                console.error('Login error:', error);
                errorMessage.textContent = error.message;
                errorMessage.style.display = 'block';
            }
        });
    </script>
</body>
</html>
