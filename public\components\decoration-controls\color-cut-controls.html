<div class="tool-section">
    <h3>Color Cut Settings</h3>
    <div class="effect-control">
        <div class="slider-container">
            <label for="ccDistance">Distance:</label>
            <input type="range" id="ccDistance" min="1" max="100" value="21">
            <span class="slider-value" id="ccDistanceValue">21%</span>
        </div>
        <div class="slider-container">
            <label for="ccCoverage">Coverage:</label>
            <input type="range" id="ccCoverage" min="100" max="300" value="100">
            <span class="slider-value" id="ccCoverageValue">100%</span>
        </div>
        <div class="fill-direction">
            <label>Fill Direction:</label>
            <div class="radio-group">
                <label class="radio-container">
                    <input type="radio" name="ccFillDirection" id="ccFillTop" value="top" checked>
                    <span class="radio-label">Top to Bottom</span>
                </label>
                <label class="radio-container">
                    <input type="radio" name="ccFillDirection" id="ccFillBottom" value="bottom">
                    <span class="radio-label">Bottom to Top</span>
                </label>
            </div>
        </div>
        <div class="simplified-color-picker">
            <label for="ccColor">Fill Color:</label>
            <input type="color" id="ccColor" value="#0000FF">
        </div>
    </div>
</div>
