<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Template Manager</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="/styles.css">
    <script type="module" src="/js/components/Toast.js"></script>
    <script type="module" src="/js/components/PromptTemplateEditor.js"></script>
    <script type="module" src="/js/components/ColorPaletteSelector.js"></script>
    <style>
        /* General styles */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #111;
            color: #fff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #1a1a1a;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            padding: 20px;
            margin-top: 20px;
        }
        
        h1, h2, h3 {
            color: #fff;
        }
        
        /* Tabs */
        .tabs {
            display: flex;
            border-bottom: 1px solid #333;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            color: #999;
        }
        
        .tab:hover {
            color: #fff;
        }
        
        .tab.active {
            border-bottom-color: #3498db;
            color: #fff;
        }
        
        /* Tab content */
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Form styles */
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        input[type="text"],
        select,
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 4px;
            background-color: #222;
            color: #fff;
        }
        
        button {
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        button.secondary {
            background-color: #7f8c8d;
        }
        
        button.secondary:hover {
            background-color: #95a5a6;
        }
        
        button.danger {
            background-color: #e74c3c;
        }
        
        button.danger:hover {
            background-color: #c0392b;
        }
        
        /* Template list */
        .template-card {
            background-color: #222;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }
        
        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .template-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .template-category {
            font-size: 14px;
            color: #999;
            margin-top: 5px;
        }
        
        .template-actions {
            display: flex;
            gap: 10px;
        }
        
        .template-content {
            font-family: monospace;
            background-color: #333;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            margin-bottom: 10px;
            max-height: 150px;
            overflow-y: auto;
        }
        
        .template-footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
        }
        
        /* Thumbnail */
        .thumbnail-container {
            margin-top: 20px;
        }
        
        .thumbnail-preview {
            width: 100%;
            max-width: 300px;
            height: 150px;
            background-color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .thumbnail-preview img {
            max-width: 100%;
            max-height: 100%;
        }
        
        /* Template thumbnail in list */
        .template-thumbnail {
            width: 100%;
            height: 150px;
            background-color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .template-thumbnail img {
            max-width: 100%;
            max-height: 100%;
        }
        
        /* Action buttons */
        .action-btn {
            background: none;
            border: none;
            color: #3498db;
            cursor: pointer;
            padding: 5px;
        }
        
        .action-btn:hover {
            color: #2980b9;
        }
        
        .action-btn.edit {
            color: #f39c12;
        }
        
        .action-btn.edit:hover {
            color: #d35400;
        }
        
        .action-btn.delete {
            color: #e74c3c;
        }
        
        .action-btn.delete:hover {
            color: #c0392b;
        }
        
        /* Utility */
        .hidden {
            display: none;
        }
        
        /* Clean up storage button */
        #cleanupStorageBtn {
            margin-bottom: 20px;
            background-color: #7f8c8d;
        }
        
        /* Form note */
        .form-note {
            margin-top: 10px;
            color: #666;
            font-style: italic;
        }

        /* Topbar spacing */
        .topbar-wrapper {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="topbar-wrapper">
        <div id="topbar"></div>
    </div>
    
    <div class="container">
        <h1 class="page-title">Prompt Template Manager</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="createTab">Create New Template</div>
            <div class="tab" data-tab="listTab">Template List</div>
        </div>
        
        <div class="tab-content active" id="createTab">
            <h2>Create New Template</h2>
            
            <div class="form-group">
                <label for="templateName">Template Name</label>
                <input type="text" id="templateName" placeholder="Enter a name for your template">
                <input type="hidden" id="templateId">
            </div>
            
            <div class="form-group">
                <label for="templateCategory">Category</label>
                <select id="templateCategory">
                    <option value="general">General</option>
                    <option value="t-shirt">T-Shirt</option>
                    <option value="poster">Poster</option>
                    <option value="logo">Logo</option>
                    <option value="illustration">Illustration</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="templateThumbnail">Carousel Thumbnail</label>
                <div class="thumbnail-upload">
                    <div class="thumbnail-preview" id="thumbnailPreview">
                        <span>No image</span>
                    </div>
                    <input type="file" id="templateThumbnail" accept="image/*">
                    <input type="hidden" id="thumbnailUrlInput" value="">
                </div>
            </div>
            
            <div class="form-group">
                <label>Template Content</label>
                <div class="template-info">
                    <p>Create a template with variables in [brackets]. Each template can have its own set of random variable options.</p>
                    <p>For example, a beach template might have different text options than a rock and roll template.</p>
                </div>
                <prompt-template-editor id="templateEditor"></prompt-template-editor>
            </div>

            <div class="form-group">
                <label>
                    Test Color Palette (Optional)
                    <span style="color: #999; font-size: 12px;">- Preview how [image-palette] variable works</span>
                </label>
                <color-palette-selector id="template-palette-selector"></color-palette-selector>
            </div>

            <div class="form-group">
                <label for="template-original-palette-input">
                    Original Palette Description (Optional)
                    <span style="color: #999; font-size: 12px;">- Used when [image-palette] variable is in template</span>
                </label>
                <input
                    type="text"
                    id="template-original-palette-input"
                    placeholder="e.g., red, blue, and yellow"
                    style="width: 100%; padding: 8px; background: #222; border: 1px solid #333; color: #fff; border-radius: 4px;"
                />
                <div style="font-size: 12px; color: #999; margin-top: 5px;">
                    This description will be used when users select "Original Palette" for templates containing [image-palette].
                </div>
            </div>

            <div class="form-group">
                <label for="template-recommended-model">
                    Recommended AI Model (Optional)
                    <span style="color: #999; font-size: 12px;">- Best model for this template style</span>
                </label>
                <select
                    id="template-recommended-model"
                    style="width: 100%; padding: 8px; background: #222; border: 1px solid #333; color: #fff; border-radius: 4px;"
                >
                    <option value="">Select a model...</option>
                </select>
                <div style="font-size: 12px; color: #999; margin-top: 5px;">
                    This model will be automatically selected when users choose this template in the full generator.
                </div>
            </div>
            
            <div class="form-group">
                <button id="saveTemplateBtn">Save Template</button>
                <button id="updateTemplateBtn" class="hidden">Update Template</button>
                <button id="cancelEditBtn" class="hidden">Cancel Edit</button>
                <div class="form-note">
                    <small>Use "Save Template" for new templates. Use "Update Template" only when editing existing templates.</small>
                </div>
            </div>
        </div>
        
        <div class="tab-content" id="listTab">
            <h2>Template List</h2>
            <button id="cleanupStorageBtn">Clean up Storage</button>
            <div id="templatesList"></div>
        </div>
    </div>
    
    <script type="module">
        import { PromptTemplateEditor } from './js/components/PromptTemplateEditor.js';
        import { processTemplate, validateTemplate } from './js/promptTemplates.js';
        import { showToast } from './js/components/Toast.js';

        import { ColorPaletteSelector } from './js/components/ColorPaletteSelector.js';
        import { createTopbar } from './js/components/Topbar.js';
        
        // Initialize topbar
        createTopbar();
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            // Get elements
            const templateNameInput = document.getElementById('templateName');
            const templateCategorySelect = document.getElementById('templateCategory');
            const saveTemplateBtn = document.getElementById('saveTemplateBtn');
            const updateTemplateBtn = document.getElementById('updateTemplateBtn');
            const cancelEditBtn = document.getElementById('cancelEditBtn');
            const templateThumbnail = document.getElementById('templateThumbnail');
            const thumbnailPreview = document.getElementById('thumbnailPreview');
            const thumbnailUrlInput = document.getElementById('thumbnailUrlInput');
            const cleanupStorageBtn = document.getElementById('cleanupStorageBtn');
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            // Tab switching
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabId = tab.dataset.tab;
                    
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab and corresponding content
                    tab.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // Save template
            saveTemplateBtn.addEventListener('click', saveTemplate);
            
            // Update template
            updateTemplateBtn.addEventListener('click', updateTemplate);
            
            // Cancel edit
            cancelEditBtn.addEventListener('click', cancelEdit);
            
            // Handle thumbnail upload
            if (templateThumbnail) {
                templateThumbnail.addEventListener('change', async (e) => {
                    const file = e.target.files[0];
                    if (!file) return;
                    
                    try {
                        // Create a FormData object
                        const formData = new FormData();
                        formData.append('image', file);
                        
                        // Show loading state
                        thumbnailPreview.innerHTML = '<div class="loading">Uploading...</div>';
                        
                        // Upload the image
                        const response = await fetch('/api/upload', {
                            method: 'POST',
                            body: formData
                        });
                        
                        if (!response.ok) {
                            throw new Error(`Failed to upload image: ${response.status} ${response.statusText}`);
                        }
                        
                        const data = await response.json();
                        console.log('Image uploaded:', data);
                        
                        // Update the thumbnail preview and hidden input
                        if (data.url) {
                            thumbnailPreview.innerHTML = `<img src="${data.url}" alt="Template thumbnail">`;
                            document.getElementById('thumbnailUrlInput').value = data.url;
                        }
                    } catch (error) {
                        console.error('Error uploading thumbnail:', error);
                        thumbnailPreview.innerHTML = `<div class="error">Upload failed: ${error.message}</div>`;
                        showToast('Error uploading thumbnail: ' + error.message, 'error');
                    }
                });
            }
            
            // Load models first, then templates
            loadModels().then(() => {
                console.log('Models loaded, now loading templates');
                loadTemplates();
            }).catch(error => {
                console.error('Error loading models:', error);
                loadTemplates(); // Load templates anyway
            });

            // Initialize UI state
            updateUIState();

            // Clean up storage
            cleanupStorageBtn.addEventListener('click', cleanupStorage);

            // Initialize palette selector
            const templatePaletteSelector = document.getElementById('template-palette-selector');
            const templateOriginalPaletteInput = document.getElementById('template-original-palette-input');

            if (templatePaletteSelector && templateOriginalPaletteInput) {
                // Handle original palette input
                templateOriginalPaletteInput.addEventListener('input', function(e) {
                    const description = e.target.value.trim();
                    templatePaletteSelector.setOriginalPaletteDescription(description);
                    console.log('Template original palette description updated:', description);
                });
            }
        });
        
        // Create or update template
        let editingTemplateId = null;

        // Update UI state based on edit mode
        function updateUIState() {
            const saveBtn = document.getElementById('saveTemplateBtn');
            const updateBtn = document.getElementById('updateTemplateBtn');
            const cancelBtn = document.getElementById('cancelEditBtn');
            const formNote = document.querySelector('.form-note small');

            if (editingTemplateId) {
                // Edit mode
                if (saveBtn) saveBtn.style.display = 'none';
                if (updateBtn) updateBtn.style.display = 'inline-block';
                if (cancelBtn) cancelBtn.style.display = 'inline-block';
                if (formNote) formNote.textContent = 'Editing existing template. Click "Update Template" to save changes.';
            } else {
                // Create mode
                if (saveBtn) saveBtn.style.display = 'inline-block';
                if (updateBtn) updateBtn.style.display = 'none';
                if (cancelBtn) cancelBtn.style.display = 'none';
                if (formNote) formNote.textContent = 'Use "Save Template" for new templates. Use "Update Template" only when editing existing templates.';
            }
        }
        
        // Save template
        async function saveTemplate() {
            // Get form elements with null checks
            const templateNameElement = document.getElementById('templateName');
            const templateCategoryElement = document.getElementById('templateCategory');
            const templateEditor = document.getElementById('templateEditor');
            
            if (!templateNameElement || !templateCategoryElement || !templateEditor) {
                showToast('Form elements not found', 'error');
                return;
            }
            
            const templateName = templateNameElement.value.trim();
            const templateCategory = templateCategoryElement.value;
            
            // Get content from editor with null checks
            let templateContent = '';
            if (templateEditor && templateEditor.shadowRoot) {
                const textarea = templateEditor.shadowRoot.querySelector('textarea');
                if (textarea) {
                    templateContent = textarea.value.trim();
                }
            }
            
            // Validate required fields
            if (!templateName) {
                showToast('Template name is required', 'error');
                return;
            }
            
            if (!templateContent) {
                showToast('Template content is required', 'error');
                return;
            }
            
            // Get random options directly from the editor component
            let randomOptions = {};
            if (templateEditor && typeof templateEditor.getRandomOptions === 'function') {
                randomOptions = templateEditor.getRandomOptions();
                console.log('Random options from editor:', randomOptions);
            }
            
            // Get thumbnail URL
            const thumbnailUrlInput = document.getElementById('thumbnailUrlInput');
            const thumbnailUrl = thumbnailUrlInput ? thumbnailUrlInput.value : '';

            // Get original palette description
            const originalPaletteInput = document.getElementById('template-original-palette-input');
            const originalPalette = originalPaletteInput ? originalPaletteInput.value.trim() : '';

            // Get recommended model
            const recommendedModelSelect = document.getElementById('template-recommended-model');
            const recommendedModel = recommendedModelSelect ? recommendedModelSelect.value : '';

            console.log('DEBUG - Recommended model select element:', recommendedModelSelect);
            console.log('DEBUG - Recommended model value:', recommendedModel);

            // Create template object for database
            const template = {
                name: templateName,
                category: templateCategory,
                template: templateContent,
                thumbnailUrl: thumbnailUrl,
                randomOptions: randomOptions,
                originalPalette: originalPalette,
                recommendedModel: recommendedModel
            };

            console.log('Saving template with data:', template);
            
            try {
                // Get auth token from cookies
                const getCookie = (name) => {
                    const value = `; ${document.cookie}`;
                    const parts = value.split(`; ${name}=`);
                    if (parts.length === 2) return parts.pop().split(';').shift();
                    return null;
                };
                
                const token = getCookie('token');
                
                // Save to database
                const response = await fetch('/api/templates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token ? `Bearer ${token}` : ''
                    },
                    body: JSON.stringify(template),
                    credentials: 'include' // Include credentials for cookies
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to save template to database: ${response.status} ${response.statusText}`);
                }
                
                const savedTemplate = await response.json();
                console.log('Template saved to database:', savedTemplate);
                
                // Show success message
                showToast('Template saved successfully to database', 'success');
                
                // Reset form
                resetForm();
                
                // Refresh templates list
                loadTemplates();
            } catch (error) {
                console.error('Error saving template:', error);
                showToast('Error saving template: ' + error.message, 'error');
            }
        }

        // Load models for the recommended model dropdown
        async function loadModels() {
            try {
                const response = await fetch('/api/models');
                if (!response.ok) {
                    throw new Error('Failed to load models');
                }
                const models = await response.json();

                const modelSelect = document.getElementById('template-recommended-model');
                if (!modelSelect) {
                    console.error('Recommended model select not found');
                    return;
                }

                // Clear existing options except the first one
                modelSelect.innerHTML = '<option value="">Select a model...</option>';

                // Add each model as an option
                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = model.displayName;
                    modelSelect.appendChild(option);
                });

                console.log('Models loaded for recommended model selector:', models.length);
            } catch (error) {
                console.error('Error loading models:', error);
                const modelSelect = document.getElementById('template-recommended-model');
                if (modelSelect) {
                    modelSelect.innerHTML = '<option value="">Error loading models</option>';
                }
            }
        }

        // Load templates
        async function loadTemplates() {
            try {
                // Clear templates container
                const templatesContainer = document.getElementById('templatesList');
                if (templatesContainer) {
                    templatesContainer.innerHTML = '<div class="loading">Loading templates...</div>';
                }
                
                // Load from database
                const response = await fetch('/api/templates');
                
                if (!response.ok) {
                    throw new Error(`Failed to load templates from database: ${response.status} ${response.statusText}`);
                }
                
                const templates = await response.json();
                console.log('Templates loaded from database:', templates);
                
                // Clear templates container
                if (templatesContainer) {
                    templatesContainer.innerHTML = '';
                }
                
                // Display templates
                if (templates && templates.length > 0) {
                    const templatesGrid = document.createElement('div');
                    templatesGrid.className = 'templates-grid';
                    
                    templates.forEach(template => {
                        const templateCard = createTemplateCard(template);
                        templatesGrid.appendChild(templateCard);
                    });
                    
                    templatesContainer.appendChild(templatesGrid);
                } else {
                    // No templates found
                    if (templatesContainer) {
                        templatesContainer.innerHTML = '<div class="no-templates">No templates found. Create your first template!</div>';
                    }
                }
            } catch (error) {
                console.error('Error loading templates:', error);
                showToast('Error loading templates: ' + error.message, 'error');
                
                // Show error message in UI
                const templatesContainer = document.getElementById('templatesList');
                if (templatesContainer) {
                    templatesContainer.innerHTML = `<div class="error-message">Error loading templates: ${error.message}</div>`;
                }
            }
        }
        
        // Create template card element
        function createTemplateCard(template) {
            const templateCard = document.createElement('div');
            templateCard.className = 'template-card';
            templateCard.dataset.id = template._id;
            
            templateCard.innerHTML = `
                <div class="template-thumbnail">
                    ${template.thumbnailUrl 
                        ? `<img src="${template.thumbnailUrl}" alt="${template.name}">`
                        : `<div style="color: #999; text-align: center;">No thumbnail</div>`
                    }
                </div>
                <div class="template-info">
                    <h3 class="template-title">${template.name}</h3>
                    <div class="template-category">${template.category}</div>
                </div>
                <div class="template-content">${template.template.substring(0, 100)}${template.template.length > 100 ? '...' : ''}</div>
                <div class="template-actions">
                    <button class="edit-template-btn" data-id="${template._id}">Edit</button>
                    <button class="delete-template-btn" data-id="${template._id}">Delete</button>
                </div>
            `;
            
            // Add event listeners for edit and delete buttons
            const editBtn = templateCard.querySelector('.edit-template-btn');
            if (editBtn) {
                editBtn.addEventListener('click', () => editTemplate(template._id));
            }
            
            const deleteBtn = templateCard.querySelector('.delete-template-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => deleteTemplate(template._id));
            }
            
            return templateCard;
        }
        
        // Fill template form with template data
        function fillTemplateForm(template) {
            console.log('Filling template form with data:', template);
            
            // Set template ID with null check
            const templateIdElement = document.getElementById('templateId');
            if (templateIdElement) {
                templateIdElement.value = template._id;
            }
            
            // Fill form with template data with null checks
            const templateNameElement = document.getElementById('templateName');
            if (templateNameElement) {
                templateNameElement.value = template.name || '';
            }
            
            const templateCategoryElement = document.getElementById('templateCategory');
            if (templateCategoryElement) {
                templateCategoryElement.value = template.category || 'general';
            }
            
            // Set thumbnail preview and url if exists
            const thumbnailPreview = document.getElementById('thumbnailPreview');
            const thumbnailUrlInput = document.getElementById('thumbnailUrlInput');
            
            if (thumbnailPreview && template.thumbnailUrl) {
                thumbnailPreview.innerHTML = `<img src="${template.thumbnailUrl}" alt="Template thumbnail">`;
            } else if (thumbnailPreview) {
                thumbnailPreview.innerHTML = '<span>No image</span>';
            }
            
            if (thumbnailUrlInput) {
                thumbnailUrlInput.value = template.thumbnailUrl || '';
            }

            // Set original palette description
            const originalPaletteInput = document.getElementById('template-original-palette-input');
            if (originalPaletteInput) {
                originalPaletteInput.value = template.originalPalette || '';
            }

            // Set recommended model (wait for models to be loaded)
            const recommendedModelSelect = document.getElementById('template-recommended-model');
            console.log('DEBUG FILL - Template recommendedModel from DB:', template.recommendedModel);
            console.log('DEBUG FILL - Recommended model select element:', recommendedModelSelect);
            if (recommendedModelSelect && template.recommendedModel) {
                // Function to set the value once models are loaded
                const setRecommendedModel = () => {
                    const options = Array.from(recommendedModelSelect.options).map(opt => opt.value);
                    console.log('DEBUG FILL - Available options:', options);

                    if (options.includes(template.recommendedModel)) {
                        recommendedModelSelect.value = template.recommendedModel;
                        console.log('DEBUG FILL - Set select value to:', recommendedModelSelect.value);
                    } else {
                        console.log('DEBUG FILL - Model not found in options, waiting...');
                        // Try again after a short delay
                        setTimeout(setRecommendedModel, 200);
                    }
                };

                // Try to set immediately, or wait if models aren't loaded yet
                if (recommendedModelSelect.options.length > 1) {
                    setRecommendedModel();
                } else {
                    // Wait for models to load
                    setTimeout(setRecommendedModel, 500);
                }
            }
            
            // Set template content in editor
            const templateEditor = document.getElementById('templateEditor');
            if (templateEditor && template.template) {
                // Use setTimeout to ensure the editor is fully loaded
                setTimeout(() => {
                    if (templateEditor.shadowRoot) {
                        const textarea = templateEditor.shadowRoot.querySelector('textarea');
                        if (textarea) {
                            textarea.value = template.template;
                            console.log('Template content set in editor:', template.template.substring(0, 50) + '...');
                        } else {
                            console.error('Textarea not found in editor shadow root');
                        }
                    } else {
                        console.error('Editor shadow root not found');
                    }
                }, 100);
                
                // Set random options if available
                if (template.randomOptions && typeof templateEditor.setRandomOptions === 'function') {
                    setTimeout(() => {
                        templateEditor.setRandomOptions(template.randomOptions);
                        console.log('Random options set in editor:', template.randomOptions);
                    }, 200);
                }
            }
            
            // Show update and cancel buttons, hide save button
            const saveBtn = document.getElementById('saveTemplateBtn');
            const updateBtn = document.getElementById('updateTemplateBtn');
            const cancelBtn = document.getElementById('cancelEditBtn');
            
            if (saveBtn) saveBtn.style.display = 'none';
            if (updateBtn) updateBtn.style.display = 'inline-block';
            if (cancelBtn) cancelBtn.style.display = 'inline-block';
        }
        
        // Edit template
        async function editTemplate(templateId) {
            try {
                // Set editing mode
                editingTemplateId = templateId;

                // Load from database
                const response = await fetch(`/api/templates/${templateId}`);

                if (!response.ok) {
                    throw new Error(`Failed to load template from database: ${response.status} ${response.statusText}`);
                }

                const template = await response.json();
                console.log('Template loaded from database for editing:', template);

                // Fill form with template data
                fillTemplateForm(template);

                // Update UI state
                updateUIState();
                
                // Change save function to update
                const updateTemplateBtn = document.getElementById('updateTemplateBtn');
                const cancelEditBtn = document.getElementById('cancelEditBtn');
                
                // Remove any existing event listeners (to prevent duplicates)
                if (updateTemplateBtn) {
                    const newUpdateBtn = updateTemplateBtn.cloneNode(true);
                    updateTemplateBtn.parentNode.replaceChild(newUpdateBtn, updateTemplateBtn);
                    newUpdateBtn.addEventListener('click', function() {
                        updateTemplate(templateId);
                    });
                }
                
                if (cancelEditBtn) {
                    const newCancelBtn = cancelEditBtn.cloneNode(true);
                    cancelEditBtn.parentNode.replaceChild(newCancelBtn, cancelEditBtn);
                    newCancelBtn.addEventListener('click', function() {
                        cancelEdit();
                    });
                }
            } catch (error) {
                console.error('Error editing template:', error);
                showToast('Error editing template: ' + error.message, 'error');
            }
        }
        
        // Update template
        async function updateTemplate(templateId) {
            // Get template ID from input if not provided as parameter
            if (!templateId) {
                const templateIdElement = document.getElementById('templateId');
                if (templateIdElement) {
                    templateId = templateIdElement.value;
                } else {
                    showToast('Template ID not found', 'error');
                    return;
                }
            }
            
            // Validate template ID and edit mode
            if (!templateId || !editingTemplateId) {
                showToast('Error: Cannot update template. Please use "Save Template" for new templates or edit an existing template first.', 'error');
                console.log('Update attempted without valid template ID. templateId:', templateId, 'editingTemplateId:', editingTemplateId);
                return;
            }
            
            // Get form elements with null checks
            const templateNameElement = document.getElementById('templateName');
            const templateCategoryElement = document.getElementById('templateCategory');
            const templateEditor = document.getElementById('templateEditor');
            
            if (!templateNameElement || !templateCategoryElement || !templateEditor) {
                showToast('Template form elements not found', 'error');
                return;
            }
            
            const templateName = templateNameElement.value.trim();
            const templateCategory = templateCategoryElement.value;
            
            // Get template content from editor
            let templateContent = '';
            if (templateEditor && templateEditor.shadowRoot) {
                const textarea = templateEditor.shadowRoot.querySelector('textarea');
                if (textarea) {
                    templateContent = textarea.value.trim();
                }
            }
            
            if (!templateContent) {
                showToast('Template content is required', 'error');
                return;
            }
            
            // Get random options directly from the editor component
            let randomOptions = {};
            if (templateEditor && typeof templateEditor.getRandomOptions === 'function') {
                randomOptions = templateEditor.getRandomOptions();
                console.log('Random options from editor for update:', randomOptions);
            }
            
            // Get thumbnail URL
            const thumbnailUrlInput = document.getElementById('thumbnailUrlInput');
            const thumbnailUrl = thumbnailUrlInput ? thumbnailUrlInput.value : '';

            // Get original palette description
            const originalPaletteInput = document.getElementById('template-original-palette-input');
            const originalPalette = originalPaletteInput ? originalPaletteInput.value.trim() : '';

            // Get recommended model
            const recommendedModelSelect = document.getElementById('template-recommended-model');
            const recommendedModel = recommendedModelSelect ? recommendedModelSelect.value : '';

            console.log('DEBUG UPDATE - Recommended model select element:', recommendedModelSelect);
            console.log('DEBUG UPDATE - Recommended model value:', recommendedModel);

            // Create template object
            const template = {
                name: templateNameElement.value,
                category: templateCategoryElement.value,
                template: templateContent,
                thumbnailUrl: thumbnailUrl,
                randomOptions: randomOptions,
                originalPalette: originalPalette,
                recommendedModel: recommendedModel,
                updatedAt: new Date()
            };

            console.log('Updating template with data:', template);
            
            try {
                // Get auth token from cookies
                const getCookie = (name) => {
                    const value = `; ${document.cookie}`;
                    const parts = value.split(`; ${name}=`);
                    if (parts.length === 2) return parts.pop().split(';').shift();
                    return null;
                };
                
                const token = getCookie('token');


                // Update in database
                const response = await fetch(`/api/templates/${templateId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token ? `Bearer ${token}` : ''
                    },
                    body: JSON.stringify(template),
                    credentials: 'include' // Include credentials for cookies
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to update template in database: ${response.status} ${response.statusText}`);
                }
                
                const updatedTemplate = await response.json();
                console.log('Template updated in database:', updatedTemplate);
                
                // Show success message
                showToast('Template updated successfully in database', 'success');
                
                // Reset form
                resetForm();
                
                // Refresh templates list
                loadTemplates();
                
                // Switch to create tab
                switchToCreateTab();
            } catch (error) {
                console.error('Error updating template:', error);
                showToast('Error updating template: ' + error.message, 'error');
            }
        }
        
        // Delete template
        async function deleteTemplate(templateId) {
            if (!confirm('Are you sure you want to delete this template?')) {
                return;
            }
            
            try {
                // Get auth token from cookies
                const getCookie = (name) => {
                    const value = `; ${document.cookie}`;
                    const parts = value.split(`; ${name}=`);
                    if (parts.length === 2) return parts.pop().split(';').shift();
                    return null;
                };
                
                const token = getCookie('token');
                
                // Delete from database
                const response = await fetch(`/api/templates/${templateId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token ? `Bearer ${token}` : ''
                    },
                    credentials: 'include' // Include credentials for cookies
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to delete template from database: ${response.status} ${response.statusText}`);
                }
                
                console.log('Template deleted from database');
                
                // Show success message
                showToast('Template deleted successfully from database', 'success');
                
                // Refresh templates list
                loadTemplates();
            } catch (error) {
                console.error('Error deleting template:', error);
                showToast('Error deleting template: ' + error.message, 'error');
            }
        }
        
        // Reset form
        function resetForm() {
            const templateNameElement = document.getElementById('templateName');
            const templateEditor = document.querySelector('prompt-template-editor');

            // Reset editing mode
            editingTemplateId = null;

            // Reset form with null checks
            if (templateNameElement) templateNameElement.value = '';

            if (templateEditor && templateEditor.shadowRoot) {
                const textarea = templateEditor.shadowRoot.querySelector('textarea');
                if (textarea) textarea.value = '';
            }

            if (thumbnailPreview) thumbnailPreview.innerHTML = '<span>No image</span>';
            if (templateThumbnail) templateThumbnail.value = '';

            // Reset thumbnail URL input
            const thumbnailUrlInput = document.getElementById('thumbnailUrlInput');
            if (thumbnailUrlInput) thumbnailUrlInput.value = '';

            // Reset original palette input
            const originalPaletteInput = document.getElementById('template-original-palette-input');
            if (originalPaletteInput) originalPaletteInput.value = '';

            // Reset recommended model select
            const recommendedModelSelect = document.getElementById('template-recommended-model');
            if (recommendedModelSelect) recommendedModelSelect.value = '';

            // Reset hidden template ID field
            const templateIdElement = document.getElementById('templateId');
            if (templateIdElement) templateIdElement.value = '';

            // Update UI state
            updateUIState();
        }
        
        // Switch to create tab
        function switchToCreateTab() {
            const createTabElement = document.querySelector('[data-tab="createTab"]');
            if (createTabElement && typeof createTabElement.click === 'function') {
                createTabElement.click();
            }
        }
        
        // Cancel edit
        function cancelEdit() {
            // Use the resetForm function to reset all form elements
            resetForm();
            
            // Switch to create tab
            switchToCreateTab();
        }
        
        // Clean up storage
        function cleanupStorage() {
            showToast('Storage cleanup not needed - using database only', 'info');
            // No localStorage operations needed
        }
    </script>
</body>
</html>
