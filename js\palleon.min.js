!function(oe){"use strict";oe.fn.palleon=function(e){var h=oe(this),t=document.body.clientWidth,d=oe.extend({baseURL:"./",fontFamily:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",fontSize:60,fontWeight:"normal",fontStyle:"normal",canvasColor:"transparent",fill:"#000",stroke:"#fff",strokeWidth:0,textBackgroundColor:"rgba(255,255,255,0)",textAlign:"left",lineHeight:1.2,borderColor:"#000",borderDashArray:[4,4],borderOpacityWhenMoving:.5,borderScaleFactor:2,editingBorderColor:"rgba(0,0,0,0.5)",cornerColor:"#fff",cornerSize:12,cornerStrokeColor:"#000",cornerStyle:"circle",transparentCorners:!1,cursorColor:"#000",cursorWidth:2,enableGLFiltering:!0,textureSize:4096,watermark:!1,watermarkText:"palleon.website",watermarkFontFamily:"Georgia, serif",watermarkFontStyle:"normal",watermarkFontColor:"#000",watermarkFontSize:40,watermarkFontWeight:"bold",watermarkBackgroundColor:"#FFF",watermarkLocation:"bottom-right",customFunctions:function(){},saveTemplate:function(){},saveImage:function(){}},e),a="",l="none",n="",i="",p="",f="",g=0,o=1,r=1,s="left",c="top",m="",u=[],b="",v="",y="",w="",k=0,x="add-to-canvas",C=["circle","square","rectangle","triangle","ellipse","trapezoid","emerald","star"],j=["square","rectangle","triangle"],I=[["Helvetica Neue","'Helvetica Neue', 'Helvetica', 'Arial', sans-serif"],["Impact","Impact, Charcoal, sans-serif"],["Georgia","Georgia, serif"],["Palatino Linotype","'Palatino Linotype', 'Book Antiqua', Palatino, serif"],["Times New Roman","'Times New Roman', Times, serif"],["Arial","Arial, Helvetica, sans-serif"],["Arial Black","'Arial Black', Gadget, sans-serif"],["Comic Sans","'Comic Sans MS', cursive, sans-serif"],["Lucida Sans","'Lucida Sans Unicode', 'Lucida Grande', sans-serif"],["Tahoma","Tahoma, Geneva, sans-serif"],["Trebuchet","'Trebuchet MS', Helvetica, sans-serif"],["Verdana","Verdana, Geneva, sans-serif"],["Courier New","'Courier New', Courier, monospace"],["Lucida Console","'Lucida Console', Monaco, monospace"]];h.find(".crop-custom").css("display","none"),new FontFaceObserver("Material Icons").load(null,1e4).then(function(){oe("#palleon").find("#palleon-main-loader").fadeOut(200)}).catch(function(e){console.log(e),oe("#palleon").find("#palleon-main-loader").hide()});for(var e=new LazyLoad({callback_error:e=>{e.setAttribute("src",d.baseURL+"assets/placeholder.png"),oe(e).parent().css("min-height","auto"),oe(e).parent().find(".palleon-img-loader").remove()},callback_loaded:e=>{oe(e).parent().css("min-height","auto"),oe(e).parent().find(".palleon-img-loader").remove()}}),A=0;A<I.length;A++)h.find("#websafe-fonts").append(oe('<option class="websafe-font"></option>').attr("value",I[A][1]).text(I[A][0]));function S(e){for(var t=e.split(","),e=t[0].match(/:(.*?);/)[1],a=atob(t[1]),l=a.length,n=new Uint8Array(l);l--;)n[l]=a.charCodeAt(l);return new Blob([n],{type:e})}function O(e,t){var a=new XMLHttpRequest;a.onload=function(){var e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsDataURL(a.response)},a.open("GET",e),a.responseType="blob",a.send()}function F(){h.removeClass("panel-closed"),h.find(".palleon-icon-menu-btn").removeClass("active"),h.find("#palleon-icon-menu").removeClass("closed"),h.find("#palleon-toggle-left").removeClass("closed"),h.find("#palleon-toggle-left").find(".material-icons").html("chevron_left"),h.find("#palleon-icon-panel").show()}function P(){h.addClass("panel-closed"),h.find(".palleon-icon-menu-btn").removeClass("active"),h.find("#palleon-icon-menu").addClass("closed"),h.find("#palleon-toggle-left").addClass("closed"),h.find("#palleon-toggle-left").find(".material-icons").html("chevron_right"),h.find("#palleon-icon-panel").hide()}oe.getJSON(d.baseURL+"json/google-fonts.json",function(e){for(var t=0;t<e.items.length;t++)h.find("#google-fonts").append(oe('<option class="google-font"></option>').attr("value",e.items[t].family).text(e.items[t].family))}),oe.getJSON(d.baseURL+"json/material-icons.json",function(e){for(var t=0;t<e.categories.length;t++)for(var a=e.categories[t],l=0;l<a.icons.length;l++){var n=d.baseURL+"files/icons/"+a.icons[l].group_id+"/"+a.icons[l].ligature;h.find("#palleon-icons .palleon-grid").append('<div class="palleon-element add-element" data-elsource="'+n+'" data-loader="no" title="'+a.icons[l].name+'"><span class="material-icons">'+a.icons[l].ligature+"</div>")}}),h.find(".palleon-select.palleon-select2").select2({theme:"dark",width:"100%",templateSelection:le,templateResult:le,allowHtml:!0}),h.find(".palleon-colorpicker.disallow-empty").spectrum({allowEmpty:!1,showInitial:!0}),h.find(".palleon-colorpicker.allow-empty").spectrum({allowEmpty:!0,showInitial:!1}),toastr.options.closeButton=!0,toastr.options.positionClass="toast-bottom-left",toastr.options.progressBar=!0,toastr.options.newestOnTop=!0,toastr.options.showEasing="swing",toastr.options.hideEasing="linear",toastr.options.closeEasing="linear",h.find("#palleon-canvas-wrap").draggable({disabled:!0}),h.find(".paginated").each(function(){var e,t,a,l,n;e=oe(this),a=e.find(">*"),l=a.length,(n=parseInt(e.data("perpage")))<l&&(a.slice(n).hide(),t='<div id="'+e.attr("id")+'-pagination" class="palleon-pagination"></div>',e.after(t),h.find("#"+e.attr("id")+"-pagination").pagination({items:l,itemsOnPage:n,prevText:'<span class="material-icons">navigate_before</span>',nextText:'<span class="material-icons">navigate_next</span>',displayedPages:3,onPageClick:function(e,t){void 0!==t&&t.preventDefault();t=n*(e-1),e=t+n;a.hide().slice(t,e).show()}}),h.find("#"+e.attr("id")+"-pagination").pagination("selectPage",1))}),h.find("#palleon-toggle-left").on("click",function(){(oe(this).hasClass("closed")?F:P)()}),h.find("#palleon-toggle-right").on("click",function(){oe(this).hasClass("closed")?(h.removeClass("layers-closed"),oe(this).removeClass("closed"),oe(this).find(".material-icons").html("chevron_right"),h.find("#palleon-right-col").show()):(h.addClass("layers-closed"),oe(this).addClass("closed"),oe(this).find(".material-icons").html("chevron_left"),h.find("#palleon-right-col").hide())}),h.find(".palleon-toggle-right").on("click",function(e){e.preventDefault(),h.find("#palleon-toggle-right").trigger("click")}),t<=1200&&(h.find("#palleon-toggle-right").trigger("click"),h.find("#palleon-toggle-left").trigger("click")),h.find(".palleon-icon-menu-btn").on("click",function(){oe(this).data("target")&&(oe(this).hasClass("active")?P():(F(),oe(this).addClass("active"),h.find(".palleon-icon-panel-content").addClass("panel-hide"),h.find(oe(this).data("target")).removeClass("panel-hide"))),"palleon-btn-elements"==oe(this).attr("id")&&h.find("#palleon-all-elements-open").trigger("click")}),h.find(".palleon-dropdown-wrap").on("click",function(){oe(this).hasClass("opened")?(oe(this).removeClass("opened"),oe(this).find(".palleon-dropdown").hide()):(oe(this).addClass("opened"),oe(this).find(".palleon-dropdown").show())}),h.find(".palleon-icon-panel-content ul.palleon-accordion > li > a").on("click",function(e){e.preventDefault();e=oe(this).parent().parent();oe(this).parent().hasClass("opened")?e.find("li").removeClass("opened"):(e.find("li").removeClass("opened"),oe(this).parent().addClass("opened"))}),h.find(".palleon-lock-unlock").on("click",function(){oe(this).hasClass("active")?(oe(this).removeClass("active"),oe(this).find(".material-icons").html("lock_open")):(oe(this).addClass("active"),oe(this).find(".material-icons").html("lock"))}),h.find(".palleon-slider").on("input",function(){oe(this).parent().parent().find(".slider-label span").html(oe(this).val()),h.find("span.tm-count-zoom").html(oe(this).val())}),h.find('input[type="checkbox"]').on("change",function(){oe(this).data("conditional")&&(oe(this).is(":checked")?h.find(oe(this).data("conditional")).removeClass("d-none"):h.find(oe(this).data("conditional")).addClass("d-none"))}),h.find(".palleon-tabs-menu li").on("click",function(){var e=oe(this).data("target"),t=oe(this).parent().parent();t.find("> .palleon-tab").removeClass("active"),oe(e).addClass("active"),t.find("> .palleon-tabs-menu li").removeClass("active"),oe(this).addClass("active")}),h.find('input[type="number"],.numeric-field').bind("input paste keyup keydown",function(){this.value=this.value.replace(/(?!^-)[^0-9.]/g,"").replace(/(\..*)\./g,"$1"),oe(this).data("max")&&this.value>oe(this).data("max")&&(this.value=oe(this).data("max")),oe(this).data("min")&&this.value<oe(this).data("min")&&(this.value=oe(this).data("min"))}),h.find(".palleon-counter .counter-plus").on("click",function(){var e=oe(this).parent().find("input.palleon-form-field"),t=parseInt(e.val())+parseInt(e.data("step"));e.data("max")&&t>e.data("max")&&(t=e.data("max")),e.data("min")&&t<e.data("min")&&(t=e.data("min")),t<0&&(t=0),e.val(t),"palleon-img-zoom-in"==oe(this).attr("id")&&J(t)}),h.find(".palleon-counter .counter-minus").on("click",function(){var e=oe(this).parent().find("input.palleon-form-field"),t=parseInt(e.val())-parseInt(e.data("step"));e.data("max")&&t>e.data("max")&&(t=e.data("max")),e.data("min")&&t<e.data("min")&&(t=e.data("min")),t<0&&(t=0),e.val(t),"palleon-img-zoom-out"==oe(this).attr("id")&&J(t)}),fabric.enableGLFiltering=d.enableGLFiltering,fabric.textureSize=parseInt(d.textureSize),fabric.Object.prototype.borderColor=d.borderColor,fabric.Object.prototype.borderDashArray=d.borderDashArray,fabric.Object.prototype.borderOpacityWhenMoving=d.borderOpacityWhenMoving,fabric.Object.prototype.borderScaleFactor=d.borderScaleFactor,fabric.Object.prototype.editingBorderColor=d.editingBorderColor,fabric.Object.prototype.cornerColor=d.cornerColor,fabric.Object.prototype.cornerSize=d.cornerSize,fabric.Object.prototype.cornerStrokeColor=d.cornerStrokeColor,fabric.Object.prototype.cornerStyle=d.cornerStyle,fabric.Object.prototype.transparentCorners=d.transparentCorners,fabric.Object.prototype.cursorColor=d.cursorColor,fabric.Object.prototype.cursorWidth=d.cursorWidth,fabric.Object.prototype.strokeUniform=!0,fabric.Group.prototype.padding=0,fabric.Object.prototype.erasable=!1;var R=document.createElement("img");function T(e,t){t=t.target;"activeSelection"===t.type?(oe.each(t._objects,function(e,t){h.find("#palleon-layers #"+t.id).find("a.delete-layer").trigger("click")}),m.discardActiveObject()):h.find("#palleon-layers #"+t.id).find("a.delete-layer").trigger("click")}function q(e,t,a,l,n){e.save(),e.translate(t,a),e.rotate(fabric.util.degreesToRadians(n.angle)),e.drawImage(R,-12,-12,24,24),e.restore()}R.src="data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg version='1.1' id='tm_delete_btn' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' width='512px' height='512px' viewBox='0 0 512 512' style='enable-background:new 0 0 512 512;' xml:space='preserve'%3E%3Ccircle style='fill:%23F44336;' cx='256' cy='256' r='256'/%3E%3Cg%3E%3Crect x='120.001' y='239.987' transform='matrix(-0.7071 -0.7071 0.7071 -0.7071 256.0091 618.0168)' style='fill:%23FFFFFF;' width='271.997' height='32'/%3E%3Crect x='240' y='119.989' transform='matrix(-0.7071 -0.7071 0.7071 -0.7071 256.0091 618.0168)' style='fill:%23FFFFFF;' width='32' height='271.997'/%3E%3C/g%3E%3C/svg%3E";var X,Y=document.createElement("img");function z(e,t){t=t.target;"activeSelection"===t.type?toastr.warning(palleonParams.noDuplicate,palleonParams.warning):h.find("#palleon-layers #"+t.id).find("a.duplicate-layer").trigger("click")}function E(e,t,a,l,n){e.save(),e.translate(t,a),e.rotate(fabric.util.degreesToRadians(n.angle)),e.drawImage(Y,-12,-12,24,24),e.restore()}function B(e,t){""==e&&(e=(new Date).getTime()),""!=t&&"jpg"!=t||(t="jpeg"),h.find(".palleon-file-name").val(e),h.find(".palleon-file-name").data("default",e),h.find("#palleon-save-img-format").val(t),h.find("#palleon-save-img-format").trigger("change")}function G(e){var t,a;g=0,h.find("#palleon-canvas-loader").css("display","flex"),h.find("#palleon-canvas-wrap, .palleon-content-bar").css("visibility","visible"),l=e,m.backgroundImage&&(u=m.backgroundImage.filters),"canvas"==l&&(h.find("#palleon-canvas-color").trigger("change"),a=document.createElement("canvas"),t=new fabric.Canvas(a),""==(e=parseInt(h.find("#palleon-canvas-width").val()))&&(e=800),""==(a=parseInt(h.find("#palleon-canvas-height").val()))&&(a=800),t.setWidth(e),t.setHeight(a),t.backgroundColor="transparent",a=S(t.toDataURL({format:"png",enableRetinaScaling:!1})),a=URL.createObjectURL(a),h.find("#palleon-canvas-img").attr("src",a),t.dispose()),h.find("#palleon-canvas-img-wrap").imagesLoaded(function(){n=h.find("#palleon-canvas-img")[0],i=h.find("#palleon-canvas-img").attr("src"),p=n.width,f=n.height,$(n),m.setDimensions({width:p,height:f}),fabric.Image.fromURL(i,function(e){m.setBackgroundImage(e,m.renderAll.bind(m),{objectType:"BG",mode:l,scaleX:o,scaleY:r,selectable:!1,lockMovementX:!0,lockMovementY:!0,lockRotation:!0,erasable:!0},{crossOrigin:"anonymous"})}),J(),D(),setTimeout(function(){r=o=1,s="left",c="top",void(g=0)!==m.overlayImage&&null!==m.overlayImage&&(m.overlayImage=null),h.find("#keep-data").is(":checked")?(m.backgroundImage.filters=u,m.backgroundImage.applyFilters()):(m.backgroundImage.filters=[],h.find("#palleon-adjust .conditional-settings").addClass("d-none"),h.find("#palleon-brightness").prop("checked",!1),h.find("#brightness").val(0),h.find("#palleon-contrast").prop("checked",!1),h.find("#contrast").val(0),h.find("#palleon-saturation").prop("checked",!1),h.find("#saturation").val(0),h.find("#palleon-hue").prop("checked",!1),h.find("#hue").val(0),h.find("#palleon-filters input[type=checkbox]").prop("checked",!1),h.find("#palleon-gamma").prop("checked",!1),h.find("#gamma-red").val(1),h.find("#gamma-green").val(1),h.find("#gamma-blue").val(1),h.find("#palleon-blend-color").prop("checked",!1),h.find("#blend-color-mode").val("add"),h.find("#blend-color-color").spectrum("set","#ffffff"),h.find("#blend-color-alpha").val(.5),h.find("#blend-color-alpha").parent().parent().find(".slider-label span").html(.5),h.find("#palleon-duotone-color").prop("checked",!1),h.find("#duotone-light-color").spectrum("set","green"),h.find("#duotone-dark-color").spectrum("set","blue"),h.find("#palleon-swap-colors").prop("checked",!1),h.find("#palleon-blur").prop("checked",!1),h.find("#blur").val(0),h.find("#palleon-noise").prop("checked",!1),h.find("#noise").val(0),h.find("#palleon-pixelate").prop("checked",!1),h.find("#pixelate").val(1),m.getObjects().filter(e=>"BG"!=e.objectType).forEach(e=>m.remove(e)),h.find("#palleon-layers li").remove(),N()),m.fire("selection:cleared"),m.requestRenderAll(),_('<span class="material-icons">flag</span>'+palleonParams.started)},100),h.find("#palleon-canvas-loader").hide()})}function D(){"none"==l?(h.find("#palleon-icon-menu, #palleon-icon-panel, #palleon-ruler-icon").css("pointer-events","none"),h.find(".palleon-keep, #modal-add-new .palleon-modal-close").hide(),h.find("#modal-add-new").show(),h.find("#palleon-save").prop("disabled",!0)):(h.find("#palleon-canvas-wrap, .palleon-content-bar").css("visibility","visible"),h.find("#palleon-icon-menu, #palleon-icon-panel, #palleon-ruler-icon").css("pointer-events","auto"),h.find(".palleon-keep, #modal-add-new .palleon-modal-close").show(),h.find("#modal-add-new").hide(),h.find("#palleon-save").prop("disabled",!1)),"canvas"==l?h.find(".hide-on-canvas-mode").hide():h.find(".hide-on-canvas-mode").show()}function L(t){h.find("#palleon-canvas-loader").css("display","flex"),g=t.backgroundImage.angle,o=t.backgroundImage.scaleX,r=t.backgroundImage.scaleY,s=t.backgroundImage.originX,c=t.backgroundImage.originY,m.clear(),h.find("#palleon-layers li").remove(),l=t.backgroundImage.mode;var e=S(t.backgroundImage.src);i=URL.createObjectURL(e),h.find("#palleon-canvas-img").attr("src",i),p=t.backgroundImage.width,f=t.backgroundImage.height;for(var e={width:p,height:f},a=0;a<t.objects.length;a++)"textbox"==t.objects[a].objectType&&(t.objects[a].fontFamily=t.objects[a].fontFamily+"-palleon");m.loadFromJSON(t,function(){var e=m.getObjects().filter(e=>"textbox"==e.objectType);0!==(e=e).length&&oe.each(e,function(e,t){var a=t.fontFamily.replace("-palleon","");t.fontFamily=d.fontFamily;var l,n,i,o,r="yes";if(""==a)r="no";else for(var s=0;s<I.length;s++)if(I[s][1]==a){r="no";break}"yes"==r?(WebFont.load({google:{families:[a+":regular,bold",a+":italic,regular,bold"]}}),l=new FontFaceObserver(a,{weight:"normal",style:"normal"}),n=new FontFaceObserver(a,{weight:"bold",style:"normal"}),i=new FontFaceObserver(a,{weight:"normal",style:"italic"}),o=new FontFaceObserver(a,{weight:"bold",style:"italic"}),Promise.all([l.load(null,5e3),n.load(null,5e3),i.load(null,5e3),o.load(null,5e3)]).then(function(){t.set("fontFamily",a),m.requestRenderAll()}).catch(function(e){console.log(e)})):(t.set("fontFamily",a),m.requestRenderAll())}),N(),h.find("#palleon-canvas-color").spectrum("set",m.backgroundColor),h.find("#custom-image-background").spectrum("set",m.backgroundColor),n=h.find("#palleon-canvas-img")[0],m.requestRenderAll(),h.find("#palleon-canvas-loader").hide()},function(){},{crossOrigin:"anonymous"}),B((new Date).getTime(),""),$(e),J(),D(),m.fire("selection:cleared"),setTimeout(function(){var e;h.find("#palleon-layers > li").removeClass("active"),e=t.backgroundImage.filters,h.find("#palleon-brightness").prop("checked",!1),h.find("#palleon-contrast").prop("checked",!1),h.find("#palleon-saturation").prop("checked",!1),h.find("#palleon-hue").prop("checked",!1),h.find("#grayscale").prop("checked",!1),h.find("#sepia").prop("checked",!1),h.find("#brownie").prop("checked",!1),h.find("#blackwhite").prop("checked",!1),h.find("#vintage").prop("checked",!1),h.find("#kodachrome").prop("checked",!1),h.find("#polaroid").prop("checked",!1),h.find("#technicolor").prop("checked",!1),h.find("#invert").prop("checked",!1),h.find("#sharpen").prop("checked",!1),h.find("#emboss").prop("checked",!1),h.find("#palleon-gamma").prop("checked",!1),h.find("#palleon-blend-color").prop("checked",!1),h.find("#palleon-duotone-color").prop("checked",!1),h.find("#palleon-blur").prop("checked",!1),h.find("#palleon-noise").prop("checked",!1),h.find("#palleon-pixelate").prop("checked",!1),0!==e.length&&oe.each(e,function(e,t){"Brightness"==t.type?(h.find("#palleon-brightness").prop("checked",!0),h.find("#brightness").val(t.brightness),h.find("#brightness").parent().parent().find(".slider-label span").html(t.brightness)):"Contrast"==t.type?(h.find("#palleon-contrast").prop("checked",!0),h.find("#contrast").val(t.brightness),h.find("#contrast").parent().parent().find(".slider-label span").html(t.contrast)):"Saturation"==t.type?(h.find("#palleon-saturation").prop("checked",!0),h.find("#saturation").val(t.brightness),h.find("#saturation").parent().parent().find(".slider-label span").html(t.saturation)):"HueRotation"==t.type?(h.find("#palleon-hue").prop("checked",!0),h.find("#hue").val(t.rotation),h.find("#hue").parent().parent().find(".slider-label span").html(t.rotation)):"Grayscale"==t.type?h.find("#grayscale").prop("checked",!0):"Sepia"==t.type?h.find("#sepia").prop("checked",!0):"Brownie"==t.type?h.find("#brownie").prop("checked",!0):"BlackWhite"==t.type?h.find("#blackwhite").prop("checked",!0):"Vintage"==t.type?h.find("#vintage").prop("checked",!0):"Kodachrome"==t.type?h.find("#kodachrome").prop("checked",!0):"Polaroid"==t.type?h.find("#polaroid").prop("checked",!0):"Technicolor"==t.type?h.find("#technicolor").prop("checked",!0):"Invert"==t.type?h.find("#invert").prop("checked",!0):"Convolute"==t.type?"[0,-1,0,-1,5,-1,0,-1,0]"==t.matrix?h.find("#sharpen").prop("checked",!0):"[1,1,1,1,0.7,-1,-1,-1,-1]"==t.matrix?h.find("#emboss").prop("checked",!0):"[-1,0,1,-2,0,2,-1,0,1]"==t.matrix?h.find("#sobelX").prop("checked",!0):"[-1,-2,-1,0,0,0,1,2,1]"==t.matrix&&h.find("#sobelY").prop("checked",!0):"Gamma"==t.type?(h.find("#palleon-gamma").prop("checked",!0),h.find("#gamma-red").val(t.gamma[0]),h.find("#gamma-red").parent().parent().find(".slider-label span").html(t.gamma[0]),h.find("#gamma-green").val(t.gamma[1]),h.find("#gamma-green").parent().parent().find(".slider-label span").html(t.gamma[1]),h.find("#gamma-blue").val(t.gamma[2]),h.find("#gamma-blue").parent().parent().find(".slider-label span").html(t.gamma[2])):"BlendColor"==t.type?(h.find("#palleon-blend-color").prop("checked",!0),h.find("#blend-color-mode").val(t.mode),h.find("#blend-color-color").val(t.color),h.find("#blend-color-alpha").val(t.alpha),h.find("#blend-color-alpha").parent().parent().find(".slider-label span").html(t.alpha)):"Composed"==t.type?(h.find("#palleon-duotone-color").prop("checked",!0),h.find("#duotone-light-color").val(t.subFilters[1].color),h.find("#duotone-dark-color").val(t.subFilters[2].color)):"Blur"==t.type?(h.find("#palleon-blur").prop("checked",!0),h.find("#blur").val(t.blur),h.find("#blur").parent().parent().find(".slider-label span").html(t.blur)):"Noise"==t.type?(h.find("#palleon-noise").prop("checked",!0),h.find("#noise").val(t.noise),h.find("#noise").parent().parent().find(".slider-label span").html(t.noise)):"Pixelate"==t.type&&(h.find("#palleon-pixelate").prop("checked",!0),h.find("#pixelate").val(t.blocksize),h.find("#pixelate").parent().parent().find(".slider-label span").html(t.blocksize))}),h.find("#palleon-brightness").trigger("change"),h.find("#palleon-contrast").trigger("change"),h.find("#palleon-saturation").trigger("change"),h.find("#palleon-hue").trigger("change"),h.find("#palleon-gamma").trigger("change"),h.find("#palleon-blend-color").trigger("change"),h.find("#palleon-blur").trigger("change"),h.find("#palleon-noise").trigger("change"),h.find("#palleon-pixelate").trigger("change")},100)}function M(){var e,t;d.watermark&&(e=d.watermarkLocation,t=p*d.watermarkFontSize/1400,t=new fabric.Textbox(" "+d.watermarkText+" ",{objectType:"watermark",gradientFill:"none",fontSize:t,fontFamily:d.watermarkFontFamily,fontWeight:d.watermarkFontWeight,fontStyle:d.watermarkFontStyle,lineHeight:1,fill:d.watermarkFontColor,textBackgroundColor:d.watermarkBackgroundColor,width:ae()[0],left:0}),m.add(t),"bottom-right"==e?(t.textAlign="right",t.top=ae()[1]-t.height):"bottom-left"==e?(t.textAlign="left",t.top=ae()[1]-t.height):"top-right"==e?(t.textAlign="right",t.top=0):"top-left"==e&&(t.textAlign="left",t.top=0),t.moveTo(999))}function W(){d.watermark&&m.getObjects().filter(e=>"watermark"===e.objectType).forEach(e=>m.remove(e))}function U(e){var t=palleonParams.object,a="category";return null==e?(t=palleonParams.object,a="category"):"textbox"==e?(t=palleonParams.text,a="title"):"drawing"==e?(t=palleonParams.freeDrawing,a="brush"):"frame"==e?(t=palleonParams.frame,a="wallpaper"):"image"==e?(t=palleonParams.image,a="image"):"circle"==e?t=palleonParams.circle:"square"==e?t=palleonParams.square:"rectangle"==e?t=palleonParams.rectangle:"triangle"==e?t=palleonParams.triangle:"ellipse"==e?t=palleonParams.ellipse:"trapezoid"==e?t=palleonParams.trapezoid:"emerald"==e?t=palleonParams.emerald:"star"==e?t=palleonParams.star:"element"==e?(t=palleonParams.element,a="star"):"BG"==e?(t=palleonParams.bg,a="image"):"customSVG"==e?t=palleonParams.customSvg:"qrCode"==e&&(t=palleonParams.qrCode,a="qr_code"),'<span class="material-icons">'+a+"</span>"+t}function _(e){var t=h.find("#palleon-history-list"),a=new Date,l=String(a.getHours()).padStart(2,"0")+":"+String(a.getMinutes()).padStart(2,"0")+":"+String(a.getSeconds()).padStart(2,"0"),a=m.toJSON(["objectType","gradientFill","roundedCorders","mode","selectable","lockMovementX","lockMovementY","lockRotation","crossOrigin","layerName"]);h.find("#palleon-history").prop("disabled",!1),t.find("li").removeClass("active"),t.prepend('<li class="active"><div class="info">'+e+'<span class="time">'+l+'</span></div><div><button type="button" class="palleon-btn primary"><span class="material-icons">restore</span>Restore</button><button type="button" class="palleon-btn danger"><span class="material-icons">clear</span>Delete</button><script type="text/json">'+JSON.stringify(a)+"<\/script></div></li>");a=t.find("li").length;t.data("max")<a&&t.find("li").last().remove(),h.find("#palleon-history-count").html(t.find("li").length);a=t.find("li.active").next("li"),t=t.find("li.active").prev("li");a.length?h.find("#palleon-undo").prop("disabled",!1):h.find("#palleon-undo").prop("disabled",!0),t.length?h.find("#palleon-redo").prop("disabled",!1):h.find("#palleon-redo").prop("disabled",!0)}Y.src="data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg version='1.1' id='tm_add_btn' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' width='512px' height='512px' viewBox='0 0 512 512' style='enable-background:new 0 0 512 512;' xml:space='preserve'%3E%3Ccircle style='fill:%23009688;' cx='256' cy='256' r='256'/%3E%3Cg%3E%3Crect x='240' y='120' style='fill:%23FFFFFF;' width='32' height='272'/%3E%3Crect x='120' y='240' style='fill:%23FFFFFF;' width='272' height='32'/%3E%3C/g%3E%3C/svg%3E",fabric.Image.filters.Shift=fabric.util.createClass(fabric.Image.filters.ColorMatrix,{type:"Shift",matrix:[0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0],mainParameter:!1,colorsOnly:!0}),a=h.find("#palleon-canvas")[0],(m=new fabric.Canvas(a)).backgroundColor=d.canvasColor,""!=h.find("#palleon-canvas-img").attr("src")&&(l="image",X=h.find("#palleon-canvas-img").data("filename"),a=h.find("#palleon-canvas-img").attr("src").match(/\.[0-9a-z]+$/i)[0].replace(/\./g,""),B(X,a),G(l)),D(),""!=h.find("#palleon-canvas-img").data("template")&&(X=h.find("#palleon-canvas-img").data("filename"),h.find("#palleon-canvas-loader").css("display","flex"),h.find("#palleon-canvas-wrap, .palleon-content-bar").css("visibility","visible"),h.find(".palleon-modal").hide(),m.getObjects().filter(e=>"BG"!=e.objectType).forEach(e=>m.remove(e)),h.find("#palleon-layers li").remove(),N(),oe.getJSON(h.find("#palleon-canvas-img").data("template"),function(e){L(e),setTimeout(function(){_('<span class="material-icons">flag</span>'+palleonParams.started),B(X,"")},100)}).fail(function(e,t,a){toastr.error("Request Failed: "+a,palleonParams.error)}).always(function(){h.find("#palleon-canvas-loader").hide()})),h.find(".palleon-modal-open").on("click",function(e){e.preventDefault();e=oe(this).data("target");h.find(".palleon-modal").hide(),h.find(e).show()}),h.find(".palleon-modal-close").on("click",function(e){e.preventDefault();e=oe(this).data("target");h.find(e).hide()}),h.find("#palleon-image-upload").on("change",function(){h.find(".palleon-modal").hide(),h.find("#palleon-canvas-wrap, .palleon-content-bar").css("visibility","visible");var t=new FileReader;t.onload=function(e){h.find("#palleon-canvas-img").attr("src",t.result),G("image")},t.readAsDataURL(this.files[0]),B(this.files[0].name.replace(/\.[^/.]+$/,""),this.files[0].name.match(/\.[0-9a-z]+$/i)[0].replace(/\./g,""))}),h.find("#palleon-canvas-create").on("click",function(){B((new Date).getTime(),""),G("canvas")}),h.find("#palleon-template-search").on("click",function(){var e,t=h.find("#palleon-templates-menu").val(),a=oe(this).parent().find("input");h.find("#palleon-all-templates-noimg").addClass("d-none"),h.find("#palleon-templates-grid .grid-item").each(function(){oe(this).attr("data-keyword",oe(this).data("keyword").toLowerCase())}),oe(this).hasClass("cancel")?(h.find("#palleon-templates-menu").val("all").change(),h.find("#palleon-templates-menu").parent().find("span.select2-container").css("opacity",1),oe(this).removeClass("cancel"),oe(this).find(".material-icons").html("search"),oe(this).removeClass("danger"),oe(this).addClass("primary"),a.val(""),h.find("#palleon-templates-grid .grid-item").show(),h.find("#palleon-templates-grid-pagination").length&&(h.find("#palleon-templates-grid-pagination").pagination("redraw"),h.find("#palleon-templates-grid-pagination").pagination("selectPage",1)),a.prop("disabled",!1),h.find("#palleon-templates-menu").prop("disabled",!1)):(h.find("#palleon-templates-menu").parent().find("span.select2-container").css("opacity",.5),oe(this).addClass("cancel"),oe(this).find(".material-icons").html("close"),oe(this).removeClass("primary"),oe(this).addClass("danger"),(""==(e=a.val().toLowerCase().replace(/\s/g," "))||e.length<1)&&"all"==t?(h.find("#palleon-templates-grid .grid-item").show(),h.find("#palleon-templates-grid-pagination").length&&(h.find("#palleon-templates-grid-pagination").pagination("redraw"),h.find("#palleon-templates-grid-pagination").pagination("selectPage",1))):(h.find("#palleon-templates-grid-pagination").length&&h.find("#palleon-templates-grid-pagination").pagination("destroy"),"all"==t?(""!=e||1<e.length)&&h.find("#palleon-templates-grid .grid-item").hide().filter('[data-keyword*="'+e+'"]').show():(""!=e||1<e.length?h.find("#palleon-templates-grid .grid-item").hide().filter('[data-keyword*="'+e+'"]'):h.find("#palleon-templates-grid .grid-item").hide()).filter('[data-category*="'+t+'"]').show(),0===h.find("#palleon-templates-grid .grid-item:visible").length&&h.find("#palleon-all-templates-noimg").removeClass("d-none")),a.prop("disabled",!0),h.find("#palleon-templates-menu").prop("disabled",!0))}),h.find("#palleon-json-save").on("click",function(){var t=m.toJSON(["objectType","gradientFill","roundedCorders","mode","selectable","lockMovementX","lockMovementY","lockRotation","crossOrigin","layerName"]);O(t.backgroundImage.src,function(e){t.backgroundImage.src=e;e=JSON.stringify(t);d.saveTemplate.call(this,h,e),h.find(".palleon-modal").hide()})}),h.find("#palleon-json-download").on("click",function(){var a=h.find("#palleon-json-download-name").val(),l=m.toJSON(["objectType","gradientFill","roundedCorders","mode","selectable","lockMovementX","lockMovementY","lockRotation","crossOrigin","layerName"]);O(l.backgroundImage.src,function(e){l.backgroundImage.src=e;var t=JSON.stringify(l),e=document.createElement("a"),t=new Blob([t],{type:"text/plain"});e.href=URL.createObjectURL(t),e.download=a+".json",e.click(),h.find(".palleon-modal").hide()})}),h.find("#palleon-json-upload").on("change",function(e){h.find("#palleon-canvas-wrap, .palleon-content-bar").css("visibility","visible"),h.find("#palleon-canvas-loader").css("display","flex");var t=new FileReader;t.onload=function(e){L(JSON.parse(t.result)),h.find("#palleon-canvas-loader").hide(),setTimeout(function(){_('<span class="material-icons">flag</span>'+palleonParams.started)},100)},t.readAsText(e.target.files[0]),h.find(".palleon-modal").hide()}),h.find(".template-selection").on("click",".palleon-select-template",function(){h.find("#palleon-canvas-wrap, .palleon-content-bar").css("visibility","visible"),h.find(".palleon-modal").hide(),h.find("#palleon-canvas-loader").css("display","flex"),m.getObjects().filter(e=>"BG"!=e.objectType).forEach(e=>m.remove(e)),h.find("#palleon-layers li").remove(),N(),oe.getJSON(oe(this).data("json"),function(e){L(e),setTimeout(function(){_('<span class="material-icons">flag</span>'+palleonParams.started)},100)}).fail(function(e,t,a){toastr.error("Request Failed: "+a,palleonParams.error)}).always(function(){h.find("#palleon-canvas-loader").hide()})}),h.find("#palleon-my-templates-search").on("click",function(){var e,t=oe(this).parent().find("input");h.find("#palleon-my-templates-noimg").addClass("d-none"),h.find("#palleon-my-templates li").each(function(){oe(this).attr("data-keyword",oe(this).data("keyword").toLowerCase())}),""!=t.val()&&(oe(this).hasClass("cancel")?(oe(this).removeClass("cancel"),oe(this).find(".material-icons").html("search"),oe(this).removeClass("danger"),oe(this).addClass("primary"),t.val(""),h.find("#palleon-my-templates li").show(),h.find("#palleon-my-templates-pagination").length&&(h.find("#palleon-my-templates-pagination").pagination("redraw"),h.find("#palleon-my-templates-pagination").pagination("selectPage",1)),t.prop("disabled",!1)):(oe(this).addClass("cancel"),oe(this).find(".material-icons").html("close"),oe(this).removeClass("primary"),oe(this).addClass("danger"),e=t.val().toLowerCase().replace(/\s/g," "),console.log(e),""==e||e.length<1?(h.find("#palleon-my-templates li").show(),h.find("#palleon-my-templates-pagination").length&&(h.find("#palleon-my-templates-pagination").pagination("redraw"),h.find("#palleon-my-templates-pagination").pagination("selectPage",1))):(h.find("#palleon-my-templates-pagination").length&&h.find("#palleon-my-templates-pagination").pagination("destroy"),h.find("#palleon-my-templates li").hide().filter('[data-keyword*="'+e+'"]').show(),0===h.find("#palleon-my-templates li:visible").length&&h.find("#palleon-my-templates-noimg").removeClass("d-none")),t.prop("disabled",!0)))}),h.find("#palleon-download").on("click",function(){var l=h.find("#palleon-download-name").val(),e=parseFloat(h.find("#palleon-download-quality").val()),n=h.find("#palleon-download-format").val(),i=document.createElement("a");M(),m.setZoom(1),h.find("#palleon-img-zoom").val(100);var t=f,a=p;0!=g&&180!=g&&-180!=g||(t=p,a=f),m.setWidth(t),m.setHeight(a);var o,r,s,d,c="";"svg"==n?(o=m.toSVG({suppressPreamble:!1,width:p,height:f}),a=m.getObjects().filter(e=>"textbox"==e.objectType),r='<defs><style type="text/css"><![CDATA[',s=[],d="",oe.each(a,function(e,t){for(var a=t.fontFamily,l="yes",n=0;n<I.length;n++)if(I[n][1]==a){l="no";break}"yes"==l&&(s.includes(a)||s.push(a))}),0<s.length?oe.each(s,function(e,t){var a=e==s.length-1,t=t.replace(/ /g,"+");oe.ajax({url:"https://fonts.googleapis.com/css?family="+t+":italic,regular,bold",type:"GET",dataType:"text",crossDomain:!0,success:function(e){r+=e,setTimeout(function(){a&&(o=o.replace("<defs>",r+"]]></style>"),c=new Blob([o],{type:"image/svg+xml;charset=utf-8"}),d=URL.createObjectURL(c),i.download=l+"."+n,i.href=d,i.click())},500)},error:function(e,t,a){e.status&&400==e.status?toastr.error(e.responseText,palleonParams.error):toastr.error(palleonParams.wrong,palleonParams.error)}})}):(c=new Blob([o],{type:"image/svg+xml;charset=utf-8"}),d=URL.createObjectURL(c),i.download=l+"."+n,i.href=d,i.click())):(e=m.toDataURL({format:n,quality:e,enableRetinaScaling:!1}),"webp"!=n&&(e=changeDpiDataUrl(e,h.find("#palleon-download-img-dpi").val())),c=S(e),d=URL.createObjectURL(c),i.download=l+"."+n,i.href=d,i.click()),W(),J(),m.requestRenderAll(),h.find(".palleon-modal").hide()}),h.find("#palleon-download-format").on("change",function(){"png"==oe(this).val()||"svg"==oe(this).val()?h.find("#palleon-download-quality").prop("disabled",!0):h.find("#palleon-download-quality").prop("disabled",!1)}),h.find("#palleon-save-img-format").on("change",function(){"png"==oe(this).val()||"svg"==oe(this).val()?h.find("#palleon-save-img-quality").prop("disabled",!0):h.find("#palleon-save-img-quality").prop("disabled",!1)}),h.find("#palleon-canvas-size-select").on("change",function(){var e=oe(this).val();"custom"==e?(h.find("#palleon-canvas-width").prop("disabled",!1),h.find("#palleon-canvas-height").prop("disabled",!1)):(h.find("#palleon-canvas-width").prop("disabled",!0),h.find("#palleon-canvas-height").prop("disabled",!0)),"blog-banner"==e?(h.find("#palleon-canvas-width").val(2240),h.find("#palleon-canvas-height").val(1260)):"fb-cover"==e?(h.find("#palleon-canvas-width").val(851),h.find("#palleon-canvas-height").val(315)):"fb-ad"==e?(h.find("#palleon-canvas-width").val(1200),h.find("#palleon-canvas-height").val(628)):"instagram"==e?(h.find("#palleon-canvas-width").val(1080),h.find("#palleon-canvas-height").val(1080)):"pinterest"==e?(h.find("#palleon-canvas-width").val(750),h.find("#palleon-canvas-height").val(1120)):"fb-post"==e?(h.find("#palleon-canvas-width").val(940),h.find("#palleon-canvas-height").val(788)):"twitter-post"==e?(h.find("#palleon-canvas-width").val(1600),h.find("#palleon-canvas-height").val(900)):"youtube"==e?(h.find("#palleon-canvas-width").val(1280),h.find("#palleon-canvas-height").val(720)):"wallpaper"==e&&(h.find("#palleon-canvas-width").val(1920),h.find("#palleon-canvas-height").val(1080))}),h.find("#palleon-canvas-color").on("change",function(){var e=oe(this).val();h.find("#custom-image-background").spectrum("set",e),m.backgroundColor=""==e?"transparent":e,m.requestRenderAll()}),h.find("#palleon-media-library").on("click",function(){x="add-to-canvas"}),h.find("#palleon-img-media-library").on("click",function(){x="add-as-object"}),h.find("#palleon-img-replace-media-library").on("click",function(){x="replace-image"}),h.find("#palleon-overlay-img-media-library").on("click",function(){x="overlay-image"}),h.find("#modal-media-library").on("click",".media-library-grid>.palleon-masonry-item>.palleon-masonry-item-inner",function(){h.find("#palleon-canvas-loader").css("display","flex");var e,t=oe(this).find("img").data("full"),a=new Image;"add-to-canvas"==x?(e=t.substring(0,t.indexOf("?")),B(oe(this).find("img").data("filename"),(""!=e?e:t).match(/\.[0-9a-z]+$/i)[0].replace(/\./g,"")),O(t,function(e){a.src=e,a.onload=function(){h.find("#palleon-canvas-img").attr("src",e),G("image")}})):"add-as-object"==x?O(t,function(e){a.src=e,a.onload=function(){var e=new fabric.Image(a,{objectType:"image",roundedCorders:0,stroke:"#fff",strokeWidth:0,top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"});m.add(e),e.scaleToWidth(ae()[0]/2),e.isPartiallyOnScreen()&&e.scaleToHeight(ae()[1]/2),m.setActiveObject(e),m.requestRenderAll(),h.find("#palleon-canvas-loader").hide(),m.fire("palleon:history",{type:"image",text:palleonParams.added})}}):"replace-image"==x?O(t,function(e){a.src=e,a.onload=function(){m.getActiveObject().setSrc(e),m.requestRenderAll(),h.find("#palleon-canvas-loader").hide(),m.fire("palleon:history",{type:"image",text:palleonParams.replaced})}}):"overlay-image"==x&&fabric.Image.fromURL(t,function(e){e.set({scaleX:ae()[0]/e.width,scaleY:ae()[1]/e.height,objectCaching:!1,originX:"left",originY:"top",selectable:!1,lockMovementX:!0,lockMovementY:!0,lockRotation:!0,erasable:!0}),m.setOverlayImage(e,m.renderAll.bind(m)),setTimeout(function(){h.find("#palleon-canvas-loader").hide()},500)}),h.find("#modal-media-library").hide()}),h.find("#palleon-library-my-search").on("click",function(){var e,t=oe(this).parent().find("input");h.find("#palleon-library-my-noimg").addClass("d-none"),""!=t.val()&&(oe(this).hasClass("cancel")?(oe(this).removeClass("cancel"),oe(this).find(".material-icons").html("search"),oe(this).removeClass("danger"),oe(this).addClass("primary"),t.val(""),h.find("#palleon-library-my .palleon-masonry-item").show(),h.find("#palleon-library-my-pagination").length&&(h.find("#palleon-library-my-pagination").pagination("redraw"),h.find("#palleon-library-my-pagination").pagination("selectPage",1)),t.prop("disabled",!1)):(oe(this).addClass("cancel"),oe(this).find(".material-icons").html("close"),oe(this).removeClass("primary"),oe(this).addClass("danger"),""==(e=t.val().toLowerCase().replace(/\s/g," "))||e.length<1?(h.find("#palleon-library-my .palleon-masonry-item").show(),h.find("#palleon-library-my-pagination").length&&(h.find("#palleon-library-my-pagination").pagination("redraw"),h.find("#palleon-library-my-pagination").pagination("selectPage",1))):(h.find("#palleon-library-my-pagination").length&&h.find("#palleon-library-my-pagination").pagination("destroy"),h.find("#palleon-library-my .palleon-masonry-item").hide().filter('[data-keyword*="'+e+'"]').show(),0===h.find("#palleon-library-my .palleon-masonry-item:visible").length&&h.find("#palleon-library-my-noimg").removeClass("d-none")),t.prop("disabled",!0)))}),h.find("#palleon-library-all-search").on("click",function(){var e,t=oe(this).parent().find("input");h.find("#palleon-library-all-noimg").addClass("d-none"),""!=t.val()&&(oe(this).hasClass("cancel")?(oe(this).removeClass("cancel"),oe(this).find(".material-icons").html("search"),oe(this).removeClass("danger"),oe(this).addClass("primary"),t.val(""),h.find("#palleon-library-all .palleon-masonry-item").show(),h.find("#palleon-library-all-pagination").length&&(h.find("#palleon-library-all-pagination").pagination("redraw"),h.find("#palleon-library-all-pagination").pagination("selectPage",1)),t.prop("disabled",!1)):(oe(this).addClass("cancel"),oe(this).find(".material-icons").html("close"),oe(this).removeClass("primary"),oe(this).addClass("danger"),""==(e=t.val().toLowerCase().replace(/\s/g," "))||e.length<1?(h.find("#palleon-library-all .palleon-masonry-item").show(),h.find("#palleon-library-all-pagination").length&&(h.find("#palleon-library-all-pagination").pagination("redraw"),h.find("#palleon-library-all-pagination").pagination("selectPage",1))):(h.find("#palleon-library-all-pagination").length&&h.find("#palleon-library-all-pagination").pagination("destroy"),h.find("#palleon-library-all .palleon-masonry-item").hide().filter('[data-keyword*="'+e+'"]').show(),0===h.find("#palleon-library-all .palleon-masonry-item:visible").length&&h.find("#palleon-library-all-noimg").removeClass("d-none")),t.prop("disabled",!0)))}),h.find("#palleon-save-img").on("click",function(){var e=parseFloat(h.find("#palleon-save-img-quality").val()),t=h.find("#palleon-save-img-format").val(),l="";M(),m.setZoom(1),h.find("#palleon-img-zoom").val(100);var n,i,a=f,o=p;0!=g&&180!=g&&-180!=g||(a=p,o=f),m.setWidth(a),m.setHeight(o),"svg"==t?(l=m.toSVG({suppressPreamble:!1,width:p,height:f}),o=m.getObjects().filter(e=>"textbox"==e.objectType),n='<defs><style type="text/css"><![CDATA[',i=[],oe.each(o,function(e,t){for(var a=t.fontFamily,l="yes",n=0;n<I.length;n++)if(I[n][1]==a){l="no";break}"yes"==l&&(i.includes(a)||i.push(a))}),0<i.length&&oe.each(i,function(e,t){var a=e==i.length-1,t=t.replace(/ /g,"+");oe.ajax({url:"https://fonts.googleapis.com/css?family="+t+":italic,regular,bold",type:"GET",dataType:"text",crossDomain:!0,success:function(e){n+=e,setTimeout(function(){a&&(l=l.replace("<defs>",n+"]]></style>"))},500)},error:function(e,t,a){e.status&&400==e.status?toastr.error(e.responseText,palleonParams.error):toastr.error(palleonParams.wrong,palleonParams.error)}})})):(l=m.toDataURL({format:t,quality:e,enableRetinaScaling:!1}),"webp"!=t&&(l=changeDpiDataUrl(l,h.find("#palleon-download-img-dpi").val()))),d.saveImage.call(this,h,l),h.find(".palleon-modal").hide(),W(),J(),m.requestRenderAll()}),h.find(".svg-library-grid").on("click",">.palleon-masonry-item>.palleon-masonry-item-inner",function(){var e=oe(this).find("img").data("full");fabric.loadSVGFromURL(e,function(e,t){t=fabric.util.groupSVGElements(e,t);t.set("originX","center"),t.set("originY","center"),t.set("left",ae()[0]/2),t.set("top",ae()[1]/2),t.set("objectType","customSVG"),t.scaleToWidth(ae()[0]/2),t.scaleToHeight(ae()[1]/2),m.add(t),m.setActiveObject(t),m.requestRenderAll()},function(){},{crossOrigin:"anonymous"}),h.find("#modal-svg-library").hide()}),h.find("#palleon-svg-library-my-search").on("click",function(){var e,t=oe(this).parent().find("input");h.find("#palleon-svg-library-my-noimg").addClass("d-none"),""!=t.val()&&(oe(this).hasClass("cancel")?(oe(this).removeClass("cancel"),oe(this).find(".material-icons").html("search"),oe(this).removeClass("danger"),oe(this).addClass("primary"),t.val(""),h.find("#palleon-svg-library-my .palleon-masonry-item").show(),h.find("#palleon-svg-library-my-pagination").length&&(h.find("#palleon-svg-library-my-pagination").pagination("redraw"),h.find("#palleon-svg-library-my-pagination").pagination("selectPage",1)),t.prop("disabled",!1)):(oe(this).addClass("cancel"),oe(this).find(".material-icons").html("close"),oe(this).removeClass("primary"),oe(this).addClass("danger"),""==(e=t.val().toLowerCase().replace(/\s/g," "))||e.length<1?(h.find("#palleon-svg-library-my .palleon-masonry-item").show(),h.find("#palleon-svg-library-my-pagination").length&&(h.find("#palleon-svg-library-my-pagination").pagination("redraw"),h.find("#palleon-svg-library-my-pagination").pagination("selectPage",1))):(h.find("#palleon-svg-library-my-pagination").length&&h.find("#palleon-svg-library-my-pagination").pagination("destroy"),h.find("#palleon-svg-library-my .palleon-masonry-item").hide().filter('[data-keyword*="'+e+'"]').show(),0===h.find("#palleon-svg-library-my .palleon-masonry-item:visible").length&&h.find("#palleon-svg-library-my-noimg").removeClass("d-none")),t.prop("disabled",!0)))}),h.find("#palleon-svg-library-all-search").on("click",function(){var e,t=oe(this).parent().find("input");h.find("#palleon-library-all-noimg").addClass("d-none"),""!=t.val()&&(oe(this).hasClass("cancel")?(oe(this).removeClass("cancel"),oe(this).find(".material-icons").html("search"),oe(this).removeClass("danger"),oe(this).addClass("primary"),t.val(""),h.find("#palleon-svg-library-all .palleon-masonry-item").show(),h.find("#palleon-svg-library-all-pagination").length&&(h.find("#palleon-svg-library-all-pagination").pagination("redraw"),h.find("#palleon-svg-library-all-pagination").pagination("selectPage",1)),t.prop("disabled",!1)):(oe(this).addClass("cancel"),oe(this).find(".material-icons").html("close"),oe(this).removeClass("primary"),oe(this).addClass("danger"),""==(e=t.val().toLowerCase().replace(/\s/g," "))||e.length<1?(h.find("#palleon-svg-library-all .palleon-masonry-item").show(),h.find("#palleon-svg-library-all-pagination").length&&(h.find("#palleon-svg-library-all-pagination").pagination("redraw"),h.find("#palleon-svg-library-all-pagination").pagination("selectPage",1))):(h.find("#palleon-svg-library-all-pagination").length&&h.find("#palleon-svg-library-all-pagination").pagination("destroy"),h.find("#palleon-svg-library-all .palleon-masonry-item").hide().filter('[data-keyword*="'+e+'"]').show(),0===h.find("#palleon-svg-library-all .palleon-masonry-item:visible").length&&h.find("#palleon-svg-library-all-noimg").removeClass("d-none")),t.prop("disabled",!0)))}),h.find("#palleon-undo").on("click",function(){var e=h.find("#palleon-history-list li.active").next("li");e.length?(e.find(".palleon-btn.primary").trigger("click"),h.find("#palleon-redo").prop("disabled",!1)):h.find("#palleon-undo").prop("disabled",!0)}),h.find("#palleon-redo").on("click",function(){var e=h.find("#palleon-history-list li.active").prev("li");e.length?(e.find(".palleon-btn.primary").trigger("click"),h.find("#palleon-undo").prop("disabled",!1)):h.find("#palleon-redo").prop("disabled",!0)}),h.find("#palleon-history-list").on("click",".palleon-btn.danger",function(){oe(this).parent().parent().remove(),oe("#palleon-history-list li").length||(h.find("#palleon-history").prop("disabled",!0),h.find("#palleon-undo").prop("disabled",!0),h.find("#palleon-redo").prop("disabled",!0),h.find(".palleon-modal").hide())}),h.find("#palleon-history-list").on("click",".palleon-btn.primary",function(){h.find("#palleon-history-list li").removeClass("active"),oe(this).parent().parent().addClass("active");var e=h.find("#palleon-history-list li.active").next("li"),t=h.find("#palleon-history-list li.active").prev("li");e.length?h.find("#palleon-undo").prop("disabled",!1):h.find("#palleon-undo").prop("disabled",!0),t.length?h.find("#palleon-redo").prop("disabled",!1):h.find("#palleon-redo").prop("disabled",!0);var a=JSON.parse(oe(this).parent().find("script").html());h.find(".palleon-modal").hide(),O(a.backgroundImage.src,function(e){a.backgroundImage.src=e,L(a),h.find("#palleon-canvas-loader").hide()})}),h.find("#palleon-clear-history").on("click",function(){window.confirm(palleonParams.question1)&&(h.find("#palleon-history-list li").remove(),h.find("#palleon-history").prop("disabled",!0),h.find("#palleon-undo").prop("disabled",!0),h.find("#palleon-redo").prop("disabled",!0),h.find(".palleon-modal").hide())}),m.on("palleon:history",function(e){_(U(e.type)+" "+e.text)});var H=!1;function N(){h.find("#palleon-layers li").length?(h.find("#palleon-no-layer").hide(),h.find("#palleon-layer-delete-wrap").css("visibility","visible")):(h.find("#palleon-no-layer").show(),h.find("#palleon-layer-delete-wrap").css("visibility","hidden"))}function V(e){if(h.find("#palleon-layers li").removeClass("active"),2<=e.length)for(var t=0;t<e.length;t++)h.find("#palleon-layers #"+e[t].id).addClass("active");else(e=e[0]).objectType?("textbox"==e.objectType?(h.find("#palleon-text-settings").show(),n=e,h.find("#palleon-text-input").val(n.text),h.find("#palleon-font-family").val(n.fontFamily),h.find("#palleon-font-family").trigger("change"),"none"==n.gradientFill?(h.find("#palleon-text-gradient").val("none"),h.find("#palleon-text-color").spectrum("set",n.fill)):"vertical"==n.gradientFill?(h.find("#palleon-text-gradient").val("vertical"),4==n.fill.colorStops.length?(h.find("#text-gradient-color-1").spectrum("set",n.fill.colorStops[0].color),h.find("#text-gradient-color-2").spectrum("set",n.fill.colorStops[1].color),h.find("#text-gradient-color-3").spectrum("set",n.fill.colorStops[2].color),h.find("#text-gradient-color-4").spectrum("set",n.fill.colorStops[3].color)):3==n.fill.colorStops.length?(h.find("#text-gradient-color-1").spectrum("set",n.fill.colorStops[0].color),h.find("#text-gradient-color-2").spectrum("set",n.fill.colorStops[1].color),h.find("#text-gradient-color-3").spectrum("set",n.fill.colorStops[2].color),h.find("#text-gradient-color-4").spectrum("set","")):2==n.fill.colorStops.length&&(h.find("#text-gradient-color-1").spectrum("set",n.fill.colorStops[0].color),h.find("#text-gradient-color-2").spectrum("set",n.fill.colorStops[1].color),h.find("#text-gradient-color-3").spectrum("set",""),h.find("#text-gradient-color-4").spectrum("set",""))):"horizontal"==n.gradientFill&&(h.find("#palleon-text-gradient").val("horizontal"),4==n.fill.colorStops.length?(h.find("#text-gradient-color-1").spectrum("set",n.fill.colorStops[0].color),h.find("#text-gradient-color-2").spectrum("set",n.fill.colorStops[1].color),h.find("#text-gradient-color-3").spectrum("set",n.fill.colorStops[2].color),h.find("#text-gradient-color-4").spectrum("set",n.fill.colorStops[3].color)):3==n.fill.colorStops.length?(h.find("#text-gradient-color-1").spectrum("set",n.fill.colorStops[0].color),h.find("#text-gradient-color-2").spectrum("set",n.fill.colorStops[1].color),h.find("#text-gradient-color-3").spectrum("set",n.fill.colorStops[2].color),h.find("#text-gradient-color-4").spectrum("set","")):2==n.fill.colorStops.length&&(h.find("#text-gradient-color-1").spectrum("set",n.fill.colorStops[0].color),h.find("#text-gradient-color-2").spectrum("set",n.fill.colorStops[1].color),h.find("#text-gradient-color-3").spectrum("set",""),h.find("#text-gradient-color-4").spectrum("set",""))),h.find("#palleon-text-gradient").trigger("change"),"bold"==n.fontWeight?h.find("#format-bold").addClass("active"):h.find("#format-bold").removeClass("active"),"italic"==n.fontStyle?h.find("#format-italic").addClass("active"):h.find("#format-italic").removeClass("active"),1==n.underline?h.find("#format-underline").addClass("active"):h.find("#format-underline").removeClass("active"),"left"==n.textAlign&&(h.find(".format-align").removeClass("active"),h.find("#format-align-left").addClass("active")),"right"==n.textAlign&&(h.find(".format-align").removeClass("active"),h.find("#format-align-right").addClass("active")),"center"==n.textAlign&&(h.find(".format-align").removeClass("active"),h.find("#format-align-center").addClass("active")),"justify"==n.textAlign&&(h.find(".format-align").removeClass("active"),h.find("#format-align-justify").addClass("active")),h.find("#palleon-font-size").val(n.fontSize),h.find("#palleon-outline-size").val(n.strokeWidth),h.find("#palleon-line-height").val(n.lineHeight),h.find("#palleon-letter-spacing").val(n.charSpacing),h.find("#palleon-outline-color").spectrum("set",n.stroke),h.find("#palleon-text-background").spectrum("set",n.textBackgroundColor),null==n.shadow?h.find("#palleon-text-shadow").prop("checked",!1):(h.find("#palleon-text-shadow").prop("checked",!0),h.find("#text-shadow-color").spectrum("set",n.shadow.color),h.find("#text-shadow-blur").val(n.shadow.blur),h.find("#text-shadow-offset-x").val(n.shadow.offsetX),h.find("#text-shadow-offset-y").val(n.shadow.offsetY)),h.find("#palleon-text-shadow").trigger("change"),1==n.flipX?h.find("#text-flip-x").addClass("active"):h.find("#text-flip-x").removeClass("active"),1==n.flipY?h.find("#text-flip-y").addClass("active"):h.find("#text-flip-y").removeClass("active"),h.find("#text-skew-x").val(n.skewX),h.find("#text-skew-x").parent().parent().find(".slider-label span").html(n.skewX),h.find("#text-skew-y").val(n.skewY),h.find("#text-skew-y").parent().parent().find(".slider-label span").html(n.skewY),h.find("#text-rotate").val(parseInt(n.angle)),h.find("#text-rotate").parent().parent().find(".slider-label span").html(parseInt(n.angle)),h.find("#palleon-btn-text").hasClass("active")||h.find("#palleon-btn-text").trigger("click"),h.find("#palleon-font-family").trigger("change")):h.find("#palleon-text-settings").hide(),"image"==e.objectType?(h.find("#palleon-image-settings").show(),l=e,h.find("#img-border-radius").val(l.roundedCorders),h.find("#img-border-radius").parent().parent().find(".slider-label span").html(l.roundedCorders),null==l.shadow?h.find("#palleon-image-shadow").prop("checked",!1):(h.find("#palleon-image-shadow").prop("checked",!0),h.find("#image-shadow-color").spectrum("set",l.shadow.color),h.find("#image-shadow-blur").val(l.shadow.blur),h.find("#image-shadow-offset-x").val(l.shadow.offsetX),h.find("#image-shadow-offset-y").val(l.shadow.offsetY)),h.find("#palleon-image-shadow").trigger("change"),h.find("#img-border-width").val(l.strokeWidth),h.find("#img-border-color").spectrum("set",l.stroke),h.find("#img-opacity").val(l.opacity),h.find("#img-opacity").parent().parent().find(".slider-label span").html(l.opacity),h.find("#img-skew-x").val(l.skewX),h.find("#img-skew-x").parent().parent().find(".slider-label span").html(l.skewX),h.find("#img-skew-y").val(l.skewY),h.find("#img-skew-y").parent().parent().find(".slider-label span").html(l.skewY),h.find("#img-rotate").val(parseInt(l.angle)),h.find("#img-rotate").parent().parent().find(".slider-label span").html(parseInt(l.angle)),h.find("#palleon-btn-image").hasClass("active")||(h.find("#palleon-btn-image").trigger("click"),h.find("#palleon-img-mode").trigger("click"))):h.find("#palleon-image-settings").hide(),"frame"==e.objectType&&(h.find("#palleon-btn-frames").hasClass("active")||h.find("#palleon-btn-frames").trigger("click")),"element"==e.objectType?(h.find("#palleon-custom-element-options").show(),h.find("#palleon-custom-element-options-info").hide(),"none"==(l=e).gradientFill?(h.find("#palleon-element-gradient").val("none"),h.find("#palleon-element-color").spectrum("set",l.fill)):"vertical"==l.gradientFill?(h.find("#palleon-element-gradient").val("vertical"),4==l.fill.colorStops.length?(h.find("#element-gradient-color-1").spectrum("set",l.fill.colorStops[0].color),h.find("#element-gradient-color-2").spectrum("set",l.fill.colorStops[1].color),h.find("#element-gradient-color-3").spectrum("set",l.fill.colorStops[2].color),h.find("#element-gradient-color-4").spectrum("set",l.fill.colorStops[3].color)):3==l.fill.colorStops.length?(h.find("#element-gradient-color-1").spectrum("set",l.fill.colorStops[0].color),h.find("#element-gradient-color-2").spectrum("set",l.fill.colorStops[1].color),h.find("#element-gradient-color-3").spectrum("set",l.fill.colorStops[2].color),h.find("#element-gradient-color-4").spectrum("set","")):2==l.fill.colorStops.length&&(h.find("#element-gradient-color-1").spectrum("set",l.fill.colorStops[0].color),h.find("#element-gradient-color-2").spectrum("set",l.fill.colorStops[1].color),h.find("#element-gradient-color-3").spectrum("set",""),h.find("#element-gradient-color-4").spectrum("set",""))):"horizontal"==l.gradientFill&&(h.find("#palleon-element-gradient").val("horizontal"),4==l.fill.colorStops.length?(h.find("#element-gradient-color-1").spectrum("set",l.fill.colorStops[0].color),h.find("#element-gradient-color-2").spectrum("set",l.fill.colorStops[1].color),h.find("#element-gradient-color-3").spectrum("set",l.fill.colorStops[2].color),h.find("#element-gradient-color-4").spectrum("set",l.fill.colorStops[3].color)):3==l.fill.colorStops.length?(h.find("#element-gradient-color-1").spectrum("set",l.fill.colorStops[0].color),h.find("#element-gradient-color-2").spectrum("set",l.fill.colorStops[1].color),h.find("#element-gradient-color-3").spectrum("set",l.fill.colorStops[2].color),h.find("#element-gradient-color-4").spectrum("set","")):2==l.fill.colorStops.length&&(h.find("#element-gradient-color-1").spectrum("set",l.fill.colorStops[0].color),h.find("#element-gradient-color-2").spectrum("set",l.fill.colorStops[1].color),h.find("#element-gradient-color-3").spectrum("set",""),h.find("#element-gradient-color-4").spectrum("set",""))),h.find("#palleon-element-gradient").trigger("change"),h.find("#element-opacity").val(l.opacity),h.find("#element-opacity").parent().parent().find(".slider-label span").html(l.opacity),h.find("#element-skew-x").val(l.skewX),h.find("#element-skew-x").parent().parent().find(".slider-label span").html(l.skewX),h.find("#element-skew-y").val(l.skewX),h.find("#element-skew-y").parent().parent().find(".slider-label span").html(l.skewY),h.find("#element-rotate").val(parseInt(l.angle)),h.find("#element-rotate").parent().parent().find(".slider-label span").html(parseInt(l.angle)),null==l.shadow?h.find("#palleon-element-shadow").prop("checked",!1):(h.find("#palleon-element-shadow").prop("checked",!0),h.find("#element-shadow-color").spectrum("set",l.shadow.color),h.find("#element-shadow-blur").val(l.shadow.blur),h.find("#element-shadow-offset-x").val(l.shadow.offsetX),h.find("#element-shadow-offset-y").val(l.shadow.offsetY)),h.find("#palleon-element-shadow").trigger("change"),h.find("#palleon-btn-elements").hasClass("active")||h.find("#palleon-btn-elements").trigger("click"),h.find("#palleon-custom-element-open").trigger("click")):(h.find("#palleon-custom-element-options").hide(),h.find("#palleon-custom-element-options-info").show()),"customSVG"==e.objectType?(h.find("#palleon-custom-svg-options").show(),a=e,h.find("#customsvg-opacity").val(a.opacity),h.find("#customsvg-opacity").parent().parent().find(".slider-label span").html(a.opacity),h.find("#customsvg-skew-x").val(a.skewX),h.find("#customsvg-skew-x").parent().parent().find(".slider-label span").html(a.skewX),h.find("#customsvg-skew-y").val(a.skewY),h.find("#customsvg-skew-y").parent().parent().find(".slider-label span").html(a.skewY),h.find("#customsvg-rotate").val(parseInt(a.angle)),h.find("#customsvg-rotate").parent().parent().find(".slider-label span").html(parseInt(a.angle)),h.find("#palleon-btn-icons").hasClass("active")||h.find("#palleon-btn-icons").trigger("click"),h.find("#palleon-custom-svg-open").trigger("click")):h.find("#palleon-custom-svg-options").hide(),C.includes(e.objectType)?(j.includes(e.objectType)?h.find("#shape-custom-width-wrap").show():h.find("#shape-custom-width-wrap").hide(),h.find("#palleon-shape-settings").show(),a=e,h.find("#shape-outline-width").val(a.strokeWidth),"none"==a.gradientFill?(h.find("#palleon-shape-gradient").val("none"),h.find("#palleon-shape-color").spectrum("set",a.fill)):"vertical"==a.gradientFill?(h.find("#palleon-shape-gradient").val("vertical"),4==a.fill.colorStops.length?(h.find("#shape-gradient-color-1").spectrum("set",a.fill.colorStops[0].color),h.find("#shape-gradient-color-2").spectrum("set",a.fill.colorStops[1].color),h.find("#shape-gradient-color-3").spectrum("set",a.fill.colorStops[2].color),h.find("#shape-gradient-color-4").spectrum("set",a.fill.colorStops[3].color)):3==a.fill.colorStops.length?(h.find("#shape-gradient-color-1").spectrum("set",a.fill.colorStops[0].color),h.find("#shape-gradient-color-2").spectrum("set",a.fill.colorStops[1].color),h.find("#shape-gradient-color-3").spectrum("set",a.fill.colorStops[2].color),h.find("#shape-gradient-color-4").spectrum("set","")):2==a.fill.colorStops.length&&(h.find("#shape-gradient-color-1").spectrum("set",a.fill.colorStops[0].color),h.find("#shape-gradient-color-2").spectrum("set",a.fill.colorStops[1].color),h.find("#shape-gradient-color-3").spectrum("set",""),h.find("#shape-gradient-color-4").spectrum("set",""))):"horizontal"==a.gradientFill&&(h.find("#palleon-shape-gradient").val("horizontal"),4==a.fill.colorStops.length?(h.find("#shape-gradient-color-1").spectrum("set",a.fill.colorStops[0].color),h.find("#shape-gradient-color-2").spectrum("set",a.fill.colorStops[1].color),h.find("#shape-gradient-color-3").spectrum("set",a.fill.colorStops[2].color),h.find("#shape-gradient-color-4").spectrum("set",a.fill.colorStops[3].color)):3==a.fill.colorStops.length?(h.find("#shape-gradient-color-1").spectrum("set",a.fill.colorStops[0].color),h.find("#shape-gradient-color-2").spectrum("set",a.fill.colorStops[1].color),h.find("#shape-gradient-color-3").spectrum("set",a.fill.colorStops[2].color),h.find("#shape-gradient-color-4").spectrum("set","")):2==a.fill.colorStops.length&&(h.find("#shape-gradient-color-1").spectrum("set",a.fill.colorStops[0].color),h.find("#shape-gradient-color-2").spectrum("set",a.fill.colorStops[1].color),h.find("#shape-gradient-color-3").spectrum("set",""),h.find("#shape-gradient-color-4").spectrum("set",""))),h.find("#palleon-shape-gradient").trigger("change"),h.find("#shape-outline-color").spectrum("set",a.stroke),null==a.shadow?h.find("#palleon-shape-shadow").prop("checked",!1):(h.find("#palleon-shape-shadow").prop("checked",!0),h.find("#shape-shadow-color").spectrum("set",a.shadow.color),h.find("#shape-shadow-blur").val(a.shadow.blur),h.find("#shape-shadow-offset-x").val(a.shadow.offsetX),h.find("#shape-shadow-offset-y").val(a.shadow.offsetY)),h.find("#palleon-shape-shadow").trigger("change"),h.find("#shape-opacity").val(a.opacity),h.find("#shape-opacity").parent().parent().find(".slider-label span").html(a.opacity),h.find("#shape-skew-x").val(a.skewX),h.find("#shape-skew-x").parent().parent().find(".slider-label span").html(a.skewX),h.find("#shape-skew-y").val(a.skewX),h.find("#shape-skew-y").parent().parent().find(".slider-label span").html(a.skewY),h.find("#shape-rotate").val(parseInt(a.angle)),h.find("#shape-rotate").parent().parent().find(".slider-label span").html(parseInt(a.angle)),h.find("#shape-custom-width").val(""),h.find("#shape-custom-height").val(""),h.find("#palleon-btn-shapes").hasClass("active")||h.find("#palleon-btn-shapes").trigger("click")):h.find("#palleon-shape-settings").hide(),e.id&&h.find("#palleon-layers #"+e.id).addClass("active")):oe.each(e,function(e,t){h.find("#palleon-layers #"+t.id).addClass("active")});var a,l,n}function Z(){fabric.Image.fromURL(i,function(e){m.setBackgroundImage(e,m.renderAll.bind(m),{objectType:"BG",mode:l,top:0,left:0,scaleX:o,scaleY:r,selectable:!1,angle:g,originX:s,originY:c,lockMovementX:!0,lockMovementY:!0,lockRotation:!0,erasable:!0},{crossOrigin:"anonymous"})})}function J(e){var t,a,l=f,n=p;0!=g&&180!=g&&-180!=g||(l=p,n=f),e?(e/=100,m.setZoom(e)):(t=h.find("#palleon-img-zoom").val(),a=1,e=0,l<h.find("#palleon-content").width()&&n<h.find("#palleon-content").height()?(m.setZoom(1),h.find("#palleon-img-zoom").val(100)):(l>h.find("#palleon-content").width()&&100*(a=(h.find("#palleon-content").width()-60)/l).toFixed(2)<t&&(m.setZoom(a.toFixed(2)),h.find("#palleon-img-zoom").val(100*a.toFixed(2)),e=a.toFixed(2)),n>h.find("#palleon-content").height()&&100*(a=h.find("#palleon-content").height()/n).toFixed(2)<t&&(0===e||e>a.toFixed(2))&&(m.setZoom(a.toFixed(2)),h.find("#palleon-img-zoom").val(100*a.toFixed(2))))),m.setWidth(l*m.getZoom()),m.setHeight(n*m.getZoom()),!0===m.isDrawingMode&&(h.find("#palleon-erase-btn").hasClass("active")&&h.find("#eraser-width").trigger("input"),h.find("#palleon-draw-btn").hasClass("active")&&h.find("#brush-width").trigger("input"))}m.on("mouse:up",function(e){e=e.target;null!==e&&(e=e.objectType,H&&_(U(e)+" "+palleonParams.moved)),void 0!==m.overlayImage&&null!==m.overlayImage&&m.overlayImage.set("opacity",1)}),m.on("object:moving",function(e){H=!0,void 0!==m.overlayImage&&null!==m.overlayImage&&m.overlayImage.set("opacity",.7);var t=f,a=p;0!=g&&180!=g&&-180!=g||(t=p,a=f);var l=e.target,n=l.getScaledWidth(),e=l.getScaledHeight();l.isPartiallyOnScreen()&&"clipPath"==l.objectType&&(l.top<0&&l.left<0?(l.top=0,l.left=0,l.lockMovementX=!0,l.lockMovementY=!0):l.top<0&&n+l.left>t?(l.top=0,l.left=t-n,l.lockMovementX=!0,l.lockMovementY=!0):e+l.top>a&&l.left<0?(l.top=a-e,l.left=0,l.lockMovementX=!0,l.lockMovementY=!0):e+l.top>a&&n+l.left>t?(l.top=a-e,l.left=t-n,l.lockMovementX=!0,l.lockMovementY=!0):l.top<0?(l.top=0,l.lockMovementY=!0):l.left<0?(l.left=0,l.lockMovementX=!0):n+l.left>t?(l.left=t-n,l.lockMovementX=!0):e+l.top>a&&(l.top=a-e,l.lockMovementY=!0),l.setCoords())}),m.on("object:scaling",function(e){var t=f,a=p;0!=g&&180!=g&&-180!=g||(t=p,a=f);var l=e.target,n=l.getScaledWidth(),e=l.getScaledHeight();l.isPartiallyOnScreen()&&"clipPath"==l.objectType&&(t<=n&&(l.set({scaleX:t/l.width}),l.lockScalingX=!0),a<=e&&(l.set({scaleY:a/l.height}),l.lockScalingY=!0),l.top<0&&(l.top=0,l.lockScalingX=!0,l.lockScalingY=!0,l.setCoords()),l.left<0&&(l.left=0,l.lockScalingX=!0,l.lockScalingY=!0,l.setCoords()),n+l.left>t&&(l.left=t-n,l.lockScalingX=!0,l.lockScalingY=!0,l.setCoords()),e+l.top>a&&(l.top=a-e,l.lockScalingX=!0,l.lockScalingY=!0,l.setCoords()))}),m.on("object:added",function(e){var t,a,l,n,i,o,r,s,d,c,p,f,g=e.target;"clipPath"!=g.objectType&&"drawing"!=g.objectType&&"watermark"!=g.objectType&&(!0===m.isDrawingMode?(g.set("objectType","drawing"),g.set("selectable",!1),g.set("lockMovementX",!0),g.set("lockMovementY",!0),g.set("lockRotation",!0)):(t=m.getObjects().indexOf(g),r="",a="Object",l="category",n="layer-visible",i="visibility",o="layer-unlocked",e="lock_open",0==g.visible&&(n="layer-hidden",i="visibility_off"),0==g.selectable&&(o="layer-locked",e="lock"),g.set("id",(new Date).getTime()),h.find("#palleon-layers > li").removeClass("active"),"textbox"==g.objectType?(a=g.text,l="title"):"drawing"==g.objectType?(a=palleonParams.freeDrawing,l="brush"):"frame"==g.objectType?(a=palleonParams.frame,l="wallpaper"):"image"==g.objectType?(a=palleonParams.image,l="image"):"circle"==g.objectType?a=palleonParams.circle:"square"==g.objectType?a=palleonParams.square:"rectangle"==g.objectType?a=palleonParams.rectangle:"triangle"==g.objectType?a=palleonParams.triangle:"ellipse"==g.objectType?a=palleonParams.ellipse:"trapezoid"==g.objectType?a=palleonParams.trapezoid:"emerald"==g.objectType?a=palleonParams.emerald:"star"==g.objectType?a=palleonParams.star:"element"==g.objectType?(a=palleonParams.element,l="star"):"customSVG"==g.objectType?a=palleonParams.customSvg:"qrCode"==g.objectType&&(a=palleonParams.qrCode,l="qr_code"),"layerName"in g&&(a=g.layerName),r='<li id="'+g.id+'" data-type="'+g.objectType+'" class="layer-'+g.objectType+' active" data-sort="'+t+'"><span class="material-icons">'+l+'</span><input class="layer-name" autocomplete="off" value="'+a+'" /><span class="material-icons layer-settings">settings</span><div class="layer-icons"><a class="material-icons lock-layer '+o+'" title="'+palleonParams.lockunlock+'">'+e+'</a><a class="material-icons text-success duplicate-layer" title="'+palleonParams.duplicate+'">content_copy</a><a class="material-icons layer-visibility '+n+'" title="'+palleonParams.showhide+'">'+i+'</a><a class="material-icons text-danger delete-layer" title="'+palleonParams.delete+'">clear</a></div></li>',h.find("#palleon-layers").prepend(r),p=g.id,(f=h.find("#palleon-layers #"+p)).find("a.delete-layer").on("click",function(e){e.preventDefault(),m.fire("palleon:history",{type:f.data("type"),text:palleonParams.removed}),m.getObjects().filter(e=>e.id==p).forEach(e=>m.remove(e)),f.remove(),m.requestRenderAll(),h.find("#palleon-layers").sortable("refresh"),N()}),d=g.id,(c=h.find("#palleon-layers #"+d)).find("a.duplicate-layer").on("click",function(e){e.preventDefault(),m.getObjects().filter(e=>e.id==d).forEach(t=>t.clone(function(e){e.set("id",(new Date).getTime()),e.set("objectType",t.objectType),m.add(e),m.setActiveObject(e)})),m.requestRenderAll(),h.find("#palleon-layers").sortable("refresh"),m.fire("palleon:history",{type:c.data("type"),text:palleonParams.added})}),s=g.id,h.find("#palleon-layers #"+s).find("a.layer-visibility").on("click",function(e){e.preventDefault();e=m.getObjects();oe(this).hasClass("layer-visible")?(oe(this).removeClass("layer-visible"),oe(this).addClass("layer-hidden"),oe(this).html("visibility_off"),e.filter(e=>e.id==s).forEach(e=>e.set("visible",!1))):oe(this).hasClass("layer-hidden")&&(oe(this).removeClass("layer-hidden"),oe(this).addClass("layer-visible"),oe(this).html("visibility"),e.filter(e=>e.id==s).forEach(e=>e.set("visible",!0))),m.requestRenderAll()}),r=g.id,h.find("#palleon-layers #"+r).find("a.lock-layer").on("click",function(e){e.preventDefault();e=m.getActiveObject();oe(this).hasClass("layer-unlocked")?(oe(this).removeClass("layer-unlocked"),oe(this).addClass("layer-locked"),oe(this).html("lock"),e.set({lockMovementX:!0,lockMovementY:!0,lockRotation:!0,selectable:!1})):oe(this).hasClass("layer-locked")&&(oe(this).removeClass("layer-locked"),oe(this).addClass("layer-unlocked"),oe(this).html("lock_open"),e.set({lockMovementX:!1,lockMovementY:!1,lockRotation:!1,selectable:!0})),m.requestRenderAll()}),r=g.id,h.find("#palleon-layers #"+r).on("click",function(e){var t=m.getObjects(),a=oe(this).attr("id");t.filter(e=>e.id==a).forEach(e=>m.setActiveObject(e)),h.find("#palleon-layers > li").removeClass("active"),oe(this).addClass("active"),m.requestRenderAll()}),r=g.id,h.find("#palleon-layers #"+r).find(".layer-name").on("change",function(e){var t=m.getObjects(),a=oe(this).parent("li").attr("id");t.filter(e=>e.id==a).forEach(e=>e.set({layerName:oe(this).val()}))}),h.find("#palleon-layers").sortable("refresh"),N(),g.controls.deleteControl=new fabric.Control({x:0,y:.5,offsetY:22,offsetX:14,cursorStyle:"pointer",mouseUpHandler:T,render:q,cornerSize:24}),g.controls.cloneControl=new fabric.Control({x:0,y:.5,offsetY:22,offsetX:-14,cursorStyle:"pointer",mouseUpHandler:z,render:E,cornerSize:24})))}),m.on("object:modified",function(e){e=e.target;"textbox"==e.objectType&&e.id&&(h.find("#palleon-layers #"+e.id+" .layer-name").html(e.text),h.find("#text-rotate").val(parseInt(m.getActiveObject().angle)),h.find("#text-rotate").parent().parent().find(".slider-label span").html(parseInt(m.getActiveObject().angle))),"image"==e.objectType&&e.id&&(h.find("#img-rotate").val(parseInt(m.getActiveObject().angle)),h.find("#img-rotate").parent().parent().find(".slider-label span").html(parseInt(m.getActiveObject().angle))),"element"==e.objectType&&e.id&&(h.find("#element-rotate").val(parseInt(m.getActiveObject().angle)),h.find("#element-rotate").parent().parent().find(".slider-label span").html(parseInt(m.getActiveObject().angle))),"customSVG"==e.objectType&&e.id&&(h.find("#customsvg-rotate").val(parseInt(m.getActiveObject().angle)),h.find("#customsvg-rotate").parent().parent().find(".slider-label span").html(parseInt(m.getActiveObject().angle))),C.includes(e.objectType)&&e.id&&(h.find("#shape-rotate").val(parseInt(m.getActiveObject().angle)),h.find("#shape-rotate").parent().parent().find(".slider-label span").html(parseInt(m.getActiveObject().angle))),"clipPath"==e.objectType&&(e.lockScalingX=!1,e.lockScalingY=!1,e.lockMovementX=!1,e.lockMovementY=!1)}),h.find(".palleon-horizontal-center").on("click",function(){var e=m.getActiveObject();e.set("originX","center"),e.set("left",ae()[0]/2),m.requestRenderAll()}),h.find(".palleon-vertical-center").on("click",function(){var e=m.getActiveObject();e.set("originY","center"),e.set("top",ae()[1]/2),m.requestRenderAll()}),m.on("selection:created",function(e){V(e.selected)}),m.on("selection:updated",function(e){V(e.selected),h.find("#palleon-font-family").trigger("change")}),m.on("selection:cleared",function(){h.find("#palleon-text-settings").hide(),h.find("#palleon-image-settings").hide(),h.find("#palleon-shape-settings").hide(),h.find("#palleon-custom-element-options").hide(),h.find("#palleon-custom-element-options-info").show(),h.find("#palleon-custom-svg-options").hide(),h.find("#palleon-layers > li").removeClass("active")}),h.find("#palleon-layers").sortable({placeholder:"layer-placeholder",axis:"y",update:function(e,t){var l=m.getObjects();oe("#palleon-layers li").each(function(t,a){oe(this).attr("data-sort",t+1),l.filter(e=>e.id==a.id).forEach(e=>e.moveTo(-(t+1)))}),m.requestRenderAll()},create:function(e,t){N()}}).disableSelection(),h.find("#palleon-layers").on("click",".layer-settings",function(){var e=oe(this).next();oe(this).hasClass("active")?(oe(this).removeClass("active"),e.hide()):(h.find("#palleon-layers .layer-icons").hide(),h.find("#palleon-layers .layer-settings").removeClass("active"),oe(this).addClass("active"),e.show())}),h.find("#palleon-layer-delete").on("click",function(){var t,e;window.confirm(palleonParams.question2)&&(t=h.find("#palleon-layer-select").val(),e=m.getObjects(),"all"==t?(e.forEach(e=>m.remove(e)),h.find("#palleon-layers > li").remove()):(e.filter(e=>e.objectType==t).forEach(e=>m.remove(e)),h.find("#palleon-layers > li.layer-"+t).remove()),m.requestRenderAll(),h.find("#palleon-layers").sortable("refresh"),N())}),h.find("#palleon-img-drag").on("click",function(){oe(this).hasClass("active")?(oe(this).removeClass("active"),h.find("#palleon-canvas-overlay").hide(),h.find("#palleon-canvas-wrap").draggable("disable")):(oe(this).addClass("active"),h.find("#palleon-canvas-overlay").show(),h.find("#palleon-canvas-wrap").draggable("enable"))}),h.find(".palleon-counter input.palleon-form-field").on("input",function(){J(parseInt(oe(this).val()))});var $=function(e){h.find("#palleon-resize-width").val(Math.round(e.width)),h.find("#palleon-resize-height").val(Math.round(e.height)),h.find("#palleon-img-width").html(Math.round(e.width)),h.find("#palleon-img-height").html(Math.round(e.height)),h.find("#palleon-crop-width").val(Math.round(e.width/2)),h.find("#palleon-crop-height").val(Math.round(e.height/2)),h.find("#palleon-resize-width").data("size",Math.round(e.width)),h.find("#palleon-resize-height").data("size",Math.round(e.height)),"image"==l&&(h.find("#palleon-crop-width").data("max",Math.round(e.width)),h.find("#palleon-crop-height").data("max",Math.round(e.height)))};function K(){var e=m.getObjects();e.filter(e=>"BG"!=e.objectType).forEach(e=>e.set("visible",!1)),m.backgroundColor="transparent";var t=S(m.toDataURL({format:"png",enableRetinaScaling:!1}));i=URL.createObjectURL(t),h.find("#palleon-canvas-img").attr("src",i),m.backgroundColor=h.find("#custom-image-background").val(),e.filter(e=>"BG"!=e.objectType).forEach(e=>e.set("visible",!0))}function Q(){var e=m.getObjects();b.moveTo(9999),m.setActiveObject(b),h.find("#palleon-crop-btns").removeClass("disabled"),h.find(".palleon-icon-panel-content ul.palleon-accordion > li, #palleon-icon-menu, #palleon-top-bar, #palleon-right-col").css("pointer-events","none"),h.find(".palleon-icon-panel-content ul.palleon-accordion > li.accordion-crop").css("pointer-events","auto"),e.filter(e=>"clipPath"!=e.objectType&&"BG"!=e.objectType).forEach(e=>e.set("selectable",!1))}function ee(e){r=0==g||180==g||-180==g?(m.setDimensions({width:f,height:p}),o=m.height/n.width,m.width/n.height):(m.setDimensions({width:p,height:f}),o=m.width/n.width,m.height/n.height),"right"==e?0==g?(g=90,s="left",c="bottom"):90==g?(g=180,s="right",c="bottom"):180==g?(g=270,s="right",c="top"):270==g||-90==g?(g=0,s="left",c="top"):-180==g?(g=-90,s="right",c="top"):-270==g&&(g=-180,s="right",c="bottom"):"left"==e&&(0==g?(g=-90,s="right",c="top"):-90==g?(g=-180,s="right",c="bottom"):-180==g?(g=-270,s="left",c="bottom"):-270==g||90==g?(g=0,s="left",c="top"):180==g?(g=90,s="left",c="bottom"):270==g&&(g=180,s="right",c="bottom")),m.backgroundImage.set({scaleX:o,scaleY:r,angle:g,originX:s,originY:c});var t=new fabric.Rect({radius:50,fill:"transparent",stroke:"transparent",strokeWidth:0,objectType:"clipPath",width:m.height,height:m.width,gradientFill:"none",top:0,left:0,originX:"left",originY:"top"});m.add(t),m.discardActiveObject();var a=new fabric.ActiveSelection(m.getObjects(),{canvas:m});m.setActiveObject(a);a=m.getActiveObject();"right"==e?a.set({angle:90,originX:"left",originY:"bottom"}):"left"==e&&a.set({angle:-90,originX:"right",originY:"top"}),m.remove(t),m.discardActiveObject(),$(m),J(),m.requestRenderAll(),m.fire("palleon:history",{type:"BG",text:palleonParams.rotated})}h.find("#palleon-crop-style").on("change",function(){var e,t;""!=oe(this).val()&&oe(this).select2("enable",!1),"freeform"==oe(this).val()?((b=new fabric.Rect({fill:"rgba(156, 39, 176, 0.3)",width:p/2,height:f/2,excludeFromExport:!0,objectType:"clipPath"})).controls={...fabric.Rect.prototype.controls,mtr:new fabric.Control({visible:!1})},m.add(b),Q()):"custom"==oe(this).val()?(h.find(".crop-custom").css("display","flex"),e=parseInt(h.find("#palleon-crop-width").val()),t=parseInt(h.find("#palleon-crop-height").val()),(b=new fabric.Rect({fill:"rgba(156, 39, 176, 0.3)",width:e,height:t,excludeFromExport:!0,objectType:"clipPath"})).controls={...fabric.Rect.prototype.controls,mtr:new fabric.Control({visible:!1}),ml:new fabric.Control({visible:!1}),mb:new fabric.Control({visible:!1}),mr:new fabric.Control({visible:!1}),mt:new fabric.Control({visible:!1}),tl:new fabric.Control({visible:!1}),bl:new fabric.Control({visible:!1}),tr:new fabric.Control({visible:!1}),br:new fabric.Control({visible:!1})},m.add(b),Q()):"square"==oe(this).val()?(t=f<=p?p/2:f/2,(b=new fabric.Rect({fill:"rgba(156, 39, 176, 0.3)",width:t,height:t,excludeFromExport:!0,objectType:"clipPath"})).controls={...fabric.Rect.prototype.controls,mtr:new fabric.Control({visible:!1}),ml:new fabric.Control({visible:!1}),mb:new fabric.Control({visible:!1}),mr:new fabric.Control({visible:!1}),mt:new fabric.Control({visible:!1})},m.add(b),Q()):"original"==oe(this).val()?((b=new fabric.Rect({fill:"rgba(156, 39, 176, 0.3)",width:p/2,height:f/2,excludeFromExport:!0,objectType:"clipPath"})).controls={...fabric.Rect.prototype.controls,mtr:new fabric.Control({visible:!1}),ml:new fabric.Control({visible:!1}),mb:new fabric.Control({visible:!1}),mr:new fabric.Control({visible:!1}),mt:new fabric.Control({visible:!1})},m.add(b),Q()):(m.getObjects().filter(e=>"clipPath"!=e.objectType&&"BG"!=e.objectType&&"drawing"!=e.objectType).forEach(e=>e.set("selectable",!0)),h.find(".crop-custom").css("display","none"),h.find("#palleon-crop-btns").addClass("disabled"),h.find(".palleon-icon-panel-content ul.palleon-accordion > li, #palleon-icon-menu, #palleon-top-bar, #palleon-right-col").css("pointer-events","auto"))}),h.find("#palleon-crop-cancel").on("click",function(){h.find("#palleon-crop-btns").addClass("disabled"),h.find("#palleon-crop-style").select2("enable"),h.find("#palleon-crop-style").val(""),h.find("#palleon-crop-style").trigger("change"),m.remove(""),m.remove(b)}),h.find("#palleon-crop-apply").on("click",function(){var e;window.confirm(palleonParams.question3)&&(h.find("#palleon-crop-btns").addClass("disabled"),h.find("#palleon-crop-style").select2("enable"),h.find("#palleon-crop-style").val(""),h.find("#palleon-crop-style").trigger("change"),m.setZoom(1),h.find("#palleon-img-zoom").val(100),e=b.getBoundingRect(),m.setWidth(e.width-1),m.setHeight(e.height-1),m.backgroundImage.set({top:-e.top,left:-e.left}),m.remove(""),m.remove(b),K(),h.find("#palleon-canvas-img-wrap").imagesLoaded(function(){p=m.width,f=m.height,g=0,s="left",c="top",r=o=1,Z(),$(m),J(),m.requestRenderAll(),setTimeout(function(){m.fire("palleon:history",{type:"BG",text:palleonParams.cropped})},500)}))}),h.find("#palleon-crop-width").bind("input paste",function(){var e;h.find("#palleon-crop-lock").hasClass("active")&&(e=oe(this).data("max")/h.find("#palleon-crop-height").data("max"),h.find("#palleon-crop-height").val(Math.round(this.value/e))),b.set("width",parseInt(oe(this).val())),b.set("height",parseInt(h.find("#palleon-crop-height").val())),m.requestRenderAll()}),h.find("#palleon-crop-height").bind("input paste",function(){var e;h.find("#palleon-crop-lock").hasClass("active")&&(e=oe(this).data("max")/h.find("#palleon-crop-width").data("max"),h.find("#palleon-crop-width").val(Math.round(this.value/e))),b.set("height",parseInt(oe(this).val())),b.set("width",parseInt(h.find("#palleon-crop-width").val())),m.requestRenderAll()}),h.find("#palleon-resize-apply").on("click",function(){var e,t;window.confirm(palleonParams.question4)&&(e=parseInt(h.find("#palleon-resize-width").val()),t=parseInt(h.find("#palleon-resize-height").val()),p=e,f=t,m.setZoom(1),h.find("#palleon-img-zoom").val(100),m.setWidth(e),m.setHeight(t),r=0==g||180==g||-180==g?(o=m.width/h.find("#palleon-canvas-img")[0].width,m.height/h.find("#palleon-canvas-img")[0].height):(o=m.height/h.find("#palleon-canvas-img")[0].width,m.width/h.find("#palleon-canvas-img")[0].height),m.backgroundImage.set({scaleX:o,scaleY:r}),m.discardActiveObject(),t=new fabric.ActiveSelection(m.getObjects(),{canvas:m}),m.setActiveObject(t),m.requestRenderAll(),(t=m.getActiveObject()).set({top:t.top*r,left:t.left*o,scaleX:o,scaleY:r}),K(),h.find("#palleon-canvas-img-wrap").imagesLoaded(function(){m.discardActiveObject(),p=m.width,f=m.height,g=0,s="left",c="top",r=o=1,Z(),$(m),J(),m.requestRenderAll(),setTimeout(function(){m.fire("palleon:history",{type:"BG",text:palleonParams.resized})},500)}))}),h.find("#palleon-resize-width").bind("input paste",function(){var e;h.find("#palleon-resize-lock").hasClass("active")&&(e=oe(this).data("size")/h.find("#palleon-resize-height").data("size"),h.find("#palleon-resize-height").val(Math.round(this.value/e)))}),h.find("#palleon-resize-height").bind("input paste",function(){var e;h.find("#palleon-resize-lock").hasClass("active")&&(e=oe(this).data("size")/h.find("#palleon-resize-width").data("size"),h.find("#palleon-resize-width").val(Math.round(this.value/e)))}),h.find("#palleon-rotate-right").on("click",function(){ee("right")}),h.find("#palleon-rotate-left").on("click",function(){ee("left")}),h.find("#palleon-flip-horizontal").on("click",function(){m.backgroundImage.toggle("flipX");var e=new fabric.Rect({radius:50,fill:"transparent",stroke:"transparent",strokeWidth:0,objectType:"clipPath",width:ae()[0],height:ae()[1],gradientFill:"none",top:0,left:0,originX:"left",originY:"top"});m.add(e),m.discardActiveObject();var t=new fabric.ActiveSelection(m.getObjects(),{canvas:m});m.setActiveObject(t);t=m.getActiveObject();0==g||180==g||-180==g?t.toggle("flipX"):t.toggle("flipY"),m.remove(e),m.discardActiveObject(),m.requestRenderAll(),m.fire("palleon:history",{type:"BG",text:palleonParams.flipped})}),h.find("#palleon-flip-vertical").on("click",function(){m.backgroundImage.toggle("flipY");var e=new fabric.Rect({radius:50,fill:"transparent",stroke:"transparent",strokeWidth:0,objectType:"clipPath",width:ae()[0],height:ae()[1],gradientFill:"none",top:0,left:0,originX:"left",originY:"top"});m.add(e),m.discardActiveObject();var t=new fabric.ActiveSelection(m.getObjects(),{canvas:m});m.setActiveObject(t);t=m.getActiveObject();0==g||180==g||-180==g?t.toggle("flipY"):t.toggle("flipX"),m.remove(e),m.discardActiveObject(),m.requestRenderAll(),m.fire("palleon:history",{type:"BG",text:palleonParams.flipped})}),h.find("#palleon-brightness").on("change",function(){oe(this).is(":checked")?m.backgroundImage.filters.push(new fabric.Image.filters.Brightness):(h.find("#brightness").val(0),h.find("#brightness").parent().parent().find(".slider-label span").html(0),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Brightness"!=e.type),m.backgroundImage.applyFilters()),m.requestRenderAll()}),h.find("#brightness").on("input",function(){m.backgroundImage.filters.filter(e=>"Brightness"==e.type).forEach(e=>e.brightness=parseFloat(this.value)),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#brightness").on("change",function(e){e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-contrast").on("change",function(){oe(this).is(":checked")?m.backgroundImage.filters.push(new fabric.Image.filters.Contrast):(h.find("#contrast").val(0),h.find("#contrast").parent().parent().find(".slider-label span").html(0),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Contrast"!=e.type),m.backgroundImage.applyFilters()),m.requestRenderAll()}),h.find("#contrast").on("input",function(){m.backgroundImage.filters.filter(e=>"Contrast"==e.type).forEach(e=>e.contrast=parseFloat(this.value)),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#contrast").on("change",function(e){e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-saturation").on("change",function(){oe(this).is(":checked")?m.backgroundImage.filters.push(new fabric.Image.filters.Saturation):(h.find("#saturation").val(0),h.find("#saturation").parent().parent().find(".slider-label span").html(0),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Saturation"!=e.type),m.backgroundImage.applyFilters()),m.requestRenderAll()}),h.find("#saturation").on("input",function(){m.backgroundImage.filters.filter(e=>"Saturation"==e.type).forEach(e=>e.saturation=parseFloat(this.value)),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#saturation").on("change",function(e){e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-hue").on("change",function(){oe(this).is(":checked")?m.backgroundImage.filters.push(new fabric.Image.filters.HueRotation):(h.find("#hue").val(0),h.find("#hue").parent().parent().find(".slider-label span").html(0),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"HueRotation"!=e.type),m.backgroundImage.applyFilters()),m.requestRenderAll()}),h.find("#hue").on("input",function(){m.backgroundImage.filters.filter(e=>"HueRotation"==e.type).forEach(e=>e.rotation=parseFloat(this.value)),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#hue").on("change",function(e){e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-filters input[type=checkbox]").on("change",function(e){oe(this).is(":checked")?"grayscale"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Grayscale):"sepia"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Sepia):"blackwhite"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.BlackWhite):"brownie"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Brownie):"vintage"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Vintage):"kodachrome"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Kodachrome):"technicolor"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Technicolor):"polaroid"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Polaroid):"shift"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Shift):"invert"==oe(this).attr("id")?m.backgroundImage.filters.push(new fabric.Image.filters.Invert):"sharpen"==oe(this).attr("id")?(h.find("#emboss").prop("checked",!1),h.find("#sobelX").prop("checked",!1),h.find("#sobelY").prop("checked",!1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Convolute"!=e.type),m.backgroundImage.filters.push(new fabric.Image.filters.Convolute({matrix:[0,-1,0,-1,5,-1,0,-1,0]}))):"emboss"==oe(this).attr("id")?(h.find("#sharpen").prop("checked",!1),h.find("#sobelX").prop("checked",!1),h.find("#sobelY").prop("checked",!1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Convolute"!=e.type),m.backgroundImage.filters.push(new fabric.Image.filters.Convolute({matrix:[1,1,1,1,.7,-1,-1,-1,-1]}))):"sobelX"==oe(this).attr("id")?(h.find("#emboss").prop("checked",!1),h.find("#sharpen").prop("checked",!1),h.find("#sobelY").prop("checked",!1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Convolute"!=e.type),m.backgroundImage.filters.push(new fabric.Image.filters.Convolute({matrix:[-1,0,1,-2,0,2,-1,0,1]}))):"sobelY"==oe(this).attr("id")&&(h.find("#emboss").prop("checked",!1),h.find("#sharpen").prop("checked",!1),h.find("#sobelX").prop("checked",!1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Convolute"!=e.type),m.backgroundImage.filters.push(new fabric.Image.filters.Convolute({matrix:[-1,-2,-1,0,0,0,1,2,1]}))):"grayscale"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Grayscale"!=e.type):"sepia"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Sepia"!=e.type):"blackwhite"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"BlackWhite"!=e.type):"brownie"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Brownie"!=e.type):"vintage"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Vintage"!=e.type):"kodachrome"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Kodachrome"!=e.type):"technicolor"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Technicolor"!=e.type):"polaroid"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Polaroid"!=e.type):"shift"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Shift"!=e.type):"invert"==oe(this).attr("id")?m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Invert"!=e.type):"sharpen"==oe(this).attr("id")?(h.find("#emboss").prop("checked",!1),h.find("#sobelX").prop("checked",!1),h.find("#sobelY").prop("checked",!1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Convolute"!=e.type)):"emboss"==oe(this).attr("id")?(h.find("#sharpen").prop("checked",!1),h.find("#sobelX").prop("checked",!1),h.find("#sobelY").prop("checked",!1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Convolute"!=e.type)):"sobelX"==oe(this).attr("id")?(h.find("#emboss").prop("checked",!1),h.find("#sharpen").prop("checked",!1),h.find("#sobelY").prop("checked",!1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Convolute"!=e.type)):"sobelY"==oe(this).attr("id")&&(h.find("#emboss").prop("checked",!1),h.find("#sharpen").prop("checked",!1),h.find("#sobelX").prop("checked",!1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Convolute"!=e.type)),m.backgroundImage.applyFilters(),m.requestRenderAll(),e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-gamma").on("change",function(){oe(this).is(":checked")?m.backgroundImage.filters.push(new fabric.Image.filters.Gamma):(h.find("#gamma-red").val(1),h.find("#gamma-red").parent().parent().find(".slider-label span").html(1),h.find("#gamma-green").val(1),h.find("#gamma-green").parent().parent().find(".slider-label span").html(1),h.find("#gamma-blue").val(1),h.find("#gamma-blue").parent().parent().find(".slider-label span").html(1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Gamma"!=e.type),m.backgroundImage.applyFilters()),m.requestRenderAll()}),h.find("#palleon-gamma-settings input").on("input",function(){var t=[parseFloat(oe("#gamma-red").val()),parseFloat(oe("#gamma-green").val()),parseFloat(oe("#gamma-blue").val())];m.backgroundImage.filters.filter(e=>"Gamma"==e.type).forEach(e=>e.gamma=t),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#palleon-gamma-settings input").on("change",function(e){e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-blur").on("change",function(){oe(this).is(":checked")?m.backgroundImage.filters.push(new fabric.Image.filters.Blur):(h.find("#blur").val(0),h.find("#blur").parent().parent().find(".slider-label span").html(0),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Blur"!=e.type),m.backgroundImage.applyFilters()),m.requestRenderAll()}),h.find("#blur").on("change",function(e){m.backgroundImage.filters.filter(e=>"Blur"==e.type).forEach(e=>e.blur=parseFloat(this.value)),m.backgroundImage.applyFilters(),m.requestRenderAll(),e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-noise").on("change",function(){oe(this).is(":checked")?m.backgroundImage.filters.push(new fabric.Image.filters.Noise):(h.find("#noise").val(0),h.find("#noise").parent().parent().find(".slider-label span").html(0),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Noise"!=e.type),m.backgroundImage.applyFilters()),m.requestRenderAll()}),h.find("#noise").on("input",function(){m.backgroundImage.filters.filter(e=>"Noise"==e.type).forEach(e=>e.noise=parseInt(this.value,10)),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#noise").on("change",function(e){e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-pixelate").on("change",function(){oe(this).is(":checked")?m.backgroundImage.filters.push(new fabric.Image.filters.Pixelate):(h.find("#pixelate").val(1),h.find("#pixelate").parent().parent().find(".slider-label span").html(1),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Pixelate"!=e.type),m.backgroundImage.applyFilters()),m.requestRenderAll()}),h.find("#pixelate").on("change",function(e){m.backgroundImage.filters.filter(e=>"Pixelate"==e.type).forEach(e=>e.blocksize=parseInt(this.value,10)),m.backgroundImage.applyFilters(),m.requestRenderAll(),e.originalEvent&&_(palleonParams.bg+" "+palleonParams.edited)}),h.find("#palleon-blend-color").on("change",function(){var t,a,l;oe(this).is(":checked")?(t=h.find("#blend-color-mode").val(),a=h.find("#blend-color-color").val(),l=parseFloat(h.find("#blend-color-alpha").val()),m.backgroundImage.filters.push(new fabric.Image.filters.BlendColor),m.backgroundImage.filters.filter(e=>"BlendColor"==e.type).forEach(e=>e.mode=t,e=>e.color=a,e=>e.alpha=parseFloat(l))):(h.find("#blend-color-mode").val("add"),h.find("#blend-color-color").spectrum("set","#ffffff"),h.find("#blend-color-alpha").val(.5),h.find("#blend-color-alpha").parent().parent().find(".slider-label span").html(.5),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"BlendColor"!=e.type)),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#blend-color-mode").on("change",function(){m.backgroundImage.filters.filter(e=>"BlendColor"==e.type).forEach(e=>e.mode=this.value),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#blend-color-color").on("change",function(){m.backgroundImage.filters.filter(e=>"BlendColor"==e.type).forEach(e=>e.color=this.value),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#blend-color-alpha").on("input",function(){m.backgroundImage.filters.filter(e=>"BlendColor"==e.type).forEach(e=>e.alpha=parseFloat(this.value)),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#palleon-duotone-color").on("change",function(){oe(this).is(":checked")?(w=new fabric.Image.filters.Composed({subFilters:[new fabric.Image.filters.Grayscale({mode:"luminosity"}),new fabric.Image.filters.BlendColor({color:h.find("#duotone-light-color").val()}),new fabric.Image.filters.BlendColor({color:h.find("#duotone-dark-color").val(),mode:"lighten"})]}),m.backgroundImage.filters.push(w)):(h.find("#duotone-light-color").spectrum("set","green"),h.find("#duotone-dark-color").spectrum("set","blue"),m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Composed"!=e.type)),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#duotone-light-color").on("change",function(){m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Composed"!=e.type),m.backgroundImage.filters.push(w),w.subFilters[1].color=oe(this).val(),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#duotone-dark-color").on("change",function(){m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"Composed"!=e.type),m.backgroundImage.filters.push(w),w.subFilters[2].color=oe(this).val(),m.backgroundImage.applyFilters(),m.requestRenderAll()}),h.find("#palleon-swap-apply").on("click",function(){var e=new fabric.Image.filters.SwapColor({colorSource:h.find("#color-source").val(),colorDestination:h.find("#color-destination").val()});m.backgroundImage.filters.push(e),m.backgroundImage.applyFilters(),m.requestRenderAll(),oe(this).prop("disabled",!0),h.find("#palleon-swap-remove").prop("disabled",!1)}),h.find("#palleon-swap-remove").on("click",function(){m.backgroundImage.filters=m.backgroundImage.filters.filter(e=>"SwapColor"!=e.type),m.backgroundImage.applyFilters(),m.requestRenderAll(),oe(this).prop("disabled",!0),h.find("#palleon-swap-apply").prop("disabled",!1)}),h.find("#palleon-swap-colors").on("change",function(){oe(this).is(":checked")||h.find("#palleon-swap-remove").trigger("click")});function te(e){var t=m.getActiveObject(),a=0;t.set("gradientFill",h.find("#palleon-"+e+"-gradient").val());var l="";if(""==h.find("#"+e+"-gradient-color-3").val()&&""==h.find("#"+e+"-gradient-color-4").val()?l=[{offset:0,color:h.find("#"+e+"-gradient-color-1").val()},{offset:1,color:h.find("#"+e+"-gradient-color-2").val()}]:""!=h.find("#"+e+"-gradient-color-3").val()&&""==h.find("#"+e+"-gradient-color-4").val()?l=[{offset:0,color:h.find("#"+e+"-gradient-color-1").val()},{offset:.5,color:h.find("#"+e+"-gradient-color-2").val()},{offset:1,color:h.find("#"+e+"-gradient-color-3").val()}]:""!=h.find("#"+e+"-gradient-color-1").val()&&""!=h.find("#"+e+"-gradient-color-2").val()&&""!=h.find("#"+e+"-gradient-color-3").val()&&""!=h.find("#"+e+"-gradient-color-4").val()&&(l=[{offset:0,color:h.find("#"+e+"-gradient-color-1").val()},{offset:.25,color:h.find("#"+e+"-gradient-color-2").val()},{offset:.75,color:h.find("#"+e+"-gradient-color-3").val()},{offset:1,color:h.find("#"+e+"-gradient-color-4").val()}]),"vertical"==h.find("#palleon-"+e+"-gradient").val()){if(h.find("#"+e+"-gradient-settings").show(),h.find("#"+e+"-fill-color").hide(),t.set("fill",new fabric.Gradient({type:"linear",gradientUnits:"percentage",coords:{x1:0,y1:0,x2:0,y2:1},colorStops:l})),"element"==t.objectType&&t._objects)for(a=0;a<t._objects.length;a++)""!=t._objects[a].fill&&t._objects[a].set({fill:new fabric.Gradient({type:"linear",gradientUnits:"percentage",coords:{x1:0,y1:0,x2:0,y2:1},colorStops:l})})}else if("horizontal"==h.find("#palleon-"+e+"-gradient").val()){if(h.find("#"+e+"-gradient-settings").show(),h.find("#"+e+"-fill-color").hide(),t.set("fill",new fabric.Gradient({type:"linear",gradientUnits:"percentage",coords:{x1:0,y1:0,x2:1,y2:0},colorStops:l})),"element"==t.objectType&&t._objects)for(a=0;a<t._objects.length;a++)""!=t._objects[a].fill&&t._objects[a].set({fill:new fabric.Gradient({type:"linear",gradientUnits:"percentage",coords:{x1:0,y1:0,x2:1,y2:0},colorStops:l})})}else if(h.find("#"+e+"-gradient-settings").hide(),h.find("#"+e+"-fill-color").show(),t.set("fill",h.find("#palleon-"+e+"-color").val()),"element"==t.objectType&&t._objects)for(a=0;a<t._objects.length;a++)""!=t._objects[a].fill&&t._objects[a].set("fill",h.find("#palleon-"+e+"-color").val());m.requestRenderAll()}oe.each(["text","image","shape","element"],function(e,t){h.find("#palleon-"+t+"-shadow").on("change",function(){var e=new fabric.Shadow({color:h.find("#"+t+"-shadow-color").val(),blur:h.find("#"+t+"-shadow-blur").val(),offsetX:h.find("#"+t+"-shadow-offset-x").val(),offsetY:h.find("#"+t+"-shadow-offset-y").val()});oe(this).is(":checked")?m.getActiveObject().shadow=e:m.getActiveObject().shadow=null,m.requestRenderAll()}),h.find("#"+t+"-shadow-color").bind("change",function(){m.getActiveObject().shadow.color=oe(this).val(),m.requestRenderAll()}),h.find("#"+t+"-shadow-settings input[type=number]").bind("input paste keyup keydown",function(){var e=oe(this).val();oe(this).attr("id")==t+"-shadow-blur"?m.getActiveObject().shadow.blur=parseInt(e):oe(this).attr("id")==t+"-shadow-offset-x"?m.getActiveObject().shadow.offsetX=parseInt(e):oe(this).attr("id")==t+"-shadow-offset-y"&&(m.getActiveObject().shadow.offsetY=parseInt(e)),m.requestRenderAll()})});function ae(){var e=m.backgroundImage.getScaledHeight(),t=m.backgroundImage.getScaledWidth();return 0!=g&&180!=g&&-180!=g||(e=m.backgroundImage.getScaledWidth(),t=m.backgroundImage.getScaledHeight()),[e,t]}function le(e){var t=e.element;return oe(t).data("icon")?oe('<div class="select2-item"><span class="material-icons">'+oe(t).data("icon")+"</span>"+e.text+"</div>"):oe('<div class="select2-item">'+e.text+"</div>")}oe.each(["text","shape","element"],function(e,t){h.find("#palleon-"+t+"-gradient").on("change",function(){te(t)}),h.find("#"+t+"-gradient-color-1").on("change",function(){te(t)}),h.find("#"+t+"-gradient-color-2").on("change",function(){te(t)}),h.find("#"+t+"-gradient-color-3").on("change",function(){te(t)}),h.find("#"+t+"-gradient-color-4").on("change",function(){te(t)})}),h.find("#palleon-add-text").on("click",function(){var e=new fabric.Textbox(palleonParams.textbox,{objectType:"textbox",gradientFill:"none",fontSize:d.fontSize,fontFamily:d.fontFamily,fontWeight:d.fontWeight,fontStyle:d.fontStyle,lineHeight:d.lineHeight,fill:d.fill,stroke:d.stroke,strokeWidth:d.strokeWidth,textBackgroundColor:d.textBackgroundColor,textAlign:d.textAlign,width:ae()[0]/2,top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"});m.add(e),m.setActiveObject(e),m.fire("palleon:history",{type:"textbox",text:palleonParams.added})}),h.find("#palleon-text-input").bind("input paste",function(){m.getActiveObject().set("text",oe(this).val()),h.find("#palleon-layers #"+m.getActiveObject().id+" .layer-name").html(m.getActiveObject().text),m.requestRenderAll()}),h.find("#palleon-text-input").bind("focusout",function(){m.fire("palleon:history",{type:"textbox",text:palleonParams.edited})}),h.find("#palleon-font-family").on("change",function(){for(var e,t,a,l,n=oe(this).val(),i="yes",o=0;o<I.length;o++)if(I[o][1]==n){i="no";break}"yes"==i?(WebFont.load({google:{families:[n+":regular,bold",n+":italic,regular,bold"]}}),e=new FontFaceObserver(n,{weight:"normal",style:"normal"}),t=new FontFaceObserver(n,{weight:"bold",style:"normal"}),a=new FontFaceObserver(n,{weight:"normal",style:"italic"}),l=new FontFaceObserver(n,{weight:"bold",style:"italic"}),Promise.all([e.load(null,5e3),t.load(null,5e3),a.load(null,5e3),l.load(null,5e3)]).then(function(){m.getActiveObject().set("fontFamily",n),m.requestRenderAll(),m.fire("palleon:history",{type:"textbox",text:palleonParams.edited})}).catch(function(e){console.log(e)})):(m.getActiveObject().set("fontFamily",n),m.requestRenderAll())}),h.find("#palleon-text-format-btns > .palleon-btn").on("click",function(){var e;"format-uppercase"==oe(this).attr("id")&&(e=(e=h.find("#palleon-text-input").val())===e.toUpperCase()?e.toLowerCase():e.toUpperCase(),h.find("#palleon-text-input").val(e),h.find("#palleon-text-input").trigger("input")),oe(this).hasClass("active")?("format-bold"==oe(this).attr("id")&&(m.getActiveObject().set("fontWeight","normal"),oe(this).removeClass("active")),"format-italic"==oe(this).attr("id")&&(m.getActiveObject().set("fontStyle","normal"),oe(this).removeClass("active")),"format-underlined"==oe(this).attr("id")&&(m.getActiveObject().set("underline",!1),oe(this).removeClass("active"))):("format-bold"==oe(this).attr("id")&&m.getActiveObject().set("fontWeight","bold"),"format-italic"==oe(this).attr("id")&&m.getActiveObject().set("fontStyle","italic"),"format-underlined"==oe(this).attr("id")&&m.getActiveObject().set("underline",!0),"format-align-left"==oe(this).attr("id")&&m.getActiveObject().set("textAlign","left"),"format-align-right"==oe(this).attr("id")&&m.getActiveObject().set("textAlign","right"),"format-align-center"==oe(this).attr("id")&&m.getActiveObject().set("textAlign","center"),"format-align-justify"==oe(this).attr("id")&&m.getActiveObject().set("textAlign","justify"),h.find(".format-align").removeClass("active"),"format-uppercase"!=oe(this).attr("id")&&oe(this).addClass("active")),m.requestRenderAll(),m.fire("palleon:history",{type:"textbox",text:palleonParams.edited})}),h.find("#palleon-text-settings input[type=number]").bind("input paste keyup keydown",function(){var e=oe(this).val();"palleon-font-size"==oe(this).attr("id")?m.getActiveObject().set("fontSize",parseInt(e)):"palleon-outline-size"==oe(this).attr("id")?m.getActiveObject().set("strokeWidth",parseInt(e)):"palleon-line-height"==oe(this).attr("id")?m.getActiveObject().set("lineHeight",parseFloat(e)):"palleon-letter-spacing"==oe(this).attr("id")&&m.getActiveObject().set("charSpacing",parseInt(e)),m.requestRenderAll()}),h.find("#palleon-text-settings input[type=number]").bind("input",function(){window.clearTimeout(k),k=setTimeout(function(){m.fire("palleon:history",{type:"textbox",text:palleonParams.edited})},500)}),h.find("#palleon-text-settings .palleon-colorpicker").bind("change",function(){var e=oe(this).val();"palleon-text-color"==oe(this).attr("id")?m.getActiveObject().set("fill",e):"palleon-outline-color"==oe(this).attr("id")?m.getActiveObject().set("stroke",e):"palleon-text-background"==oe(this).attr("id")&&m.getActiveObject().set("textBackgroundColor",e),m.requestRenderAll(),m.fire("palleon:history",{type:"textbox",text:palleonParams.edited})}),h.find("#palleon-text-flip-btns > .palleon-btn").on("click",function(){oe(this).hasClass("active")?("text-flip-x"==oe(this).attr("id")?m.getActiveObject().set("flipX",!1):"text-flip-y"==oe(this).attr("id")&&m.getActiveObject().set("flipY",!1),oe(this).removeClass("active")):("text-flip-x"==oe(this).attr("id")?m.getActiveObject().set("flipX",!0):"text-flip-y"==oe(this).attr("id")&&m.getActiveObject().set("flipY",!0),oe(this).addClass("active")),m.requestRenderAll(),m.fire("palleon:history",{type:"textbox",text:palleonParams.edited})}),h.find("#palleon-text-settings input[type=range]").bind("input click",function(){var e=oe(this).val();"text-skew-x"==oe(this).attr("id")?m.getActiveObject().set("skewX",parseInt(e)):"text-skew-y"==oe(this).attr("id")?m.getActiveObject().set("skewY",parseInt(e)):"text-rotate"==oe(this).attr("id")?m.getActiveObject().set("angle",parseInt(e)):"text-opacity"==oe(this).attr("id")&&m.getActiveObject().set("opacity",parseFloat(e)),m.requestRenderAll()}),h.find("#palleon-text-settings input[type=range]").bind("change",function(){m.fire("palleon:history",{type:"textbox",text:palleonParams.edited})}),h.find("#palleon-img-upload").on("change",function(e){var t=new FileReader;t.onload=function(e){var t=new Image;O(e.target.result,function(e){t.src=e,t.onload=function(){var e=new fabric.Image(t);e.set({objectType:"image",roundedCorders:0,stroke:"#fff",strokeWidth:0,top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"}),m.add(e),e.scaleToWidth(ae()[0]/2),e.isPartiallyOnScreen()&&e.scaleToHeight(ae()[1]/2),m.setActiveObject(e),m.requestRenderAll()}})},t.readAsDataURL(e.target.files[0]),m.fire("palleon:history",{type:"image",text:palleonParams.added})}),h.find("#palleon-overlay-img-upload").on("change",function(e){var t;""!=oe(this).val()&&(h.find("#palleon-canvas-loader").css("display","flex"),(t=new FileReader).onload=function(e){fabric.Image.fromURL(e.target.result,function(e){e.set({scaleX:ae()[0]/e.width,scaleY:ae()[1]/e.height,objectCaching:!1,originX:"left",originY:"top",selectable:!1,lockMovementX:!0,lockMovementY:!0,lockRotation:!0,erasable:!0}),m.setOverlayImage(e,m.renderAll.bind(m)),setTimeout(function(){h.find("#palleon-canvas-loader").hide()},500)})},t.readAsDataURL(e.target.files[0]),m.fire("palleon:history",{type:"image",text:palleonParams.added}))}),h.find("#palleon-overlay-delete").on("click",function(){void 0!==m.overlayImage&&null!==m.overlayImage&&(m.overlayImage=null,m.requestRenderAll())}),h.find("#img-flip-horizontal").on("click",function(){m.getActiveObject().toggle("flipX"),m.requestRenderAll(),m.fire("palleon:history",{type:"image",text:palleonParams.edited})}),h.find("#img-flip-vertical").on("click",function(){m.getActiveObject().toggle("flipY"),m.requestRenderAll(),m.fire("palleon:history",{type:"image",text:palleonParams.edited})});h.find("#img-border-radius").on("input",function(){var e,t;m.getActiveObject().set("clipPath",(e=m.getActiveObject(),t=parseInt(oe(this).val()),new fabric.Rect({width:e.width,height:e.height,rx:t/e.scaleX,ry:t/e.scaleY,left:-e.width/2,top:-e.height/2}))),m.getActiveObject().set("roundedCorders",parseInt(oe(this).val())),m.requestRenderAll()}),h.find("#img-border-radius").bind("change",function(){m.fire("palleon:history",{type:"image",text:palleonParams.edited})}),h.find("#img-border-color").bind("change",function(){m.getActiveObject().set("stroke",oe(this).val()),m.requestRenderAll(),m.fire("palleon:history",{type:"image",text:palleonParams.edited})}),h.find("#palleon-image-settings input[type=number]").on("input paste",function(){var e=parseInt(oe(this).val());"img-border-width"==oe(this).attr("id")&&m.getActiveObject().set("strokeWidth",e),m.requestRenderAll()}),h.find("#palleon-image-settings input[type=number]").bind("input",function(){window.clearTimeout(k),k=setTimeout(function(){m.fire("palleon:history",{type:"image",text:palleonParams.edited})},500)}),h.find("#palleon-image-settings input[type=range]").bind("input click",function(){var e=oe(this).val();"img-skew-x"==oe(this).attr("id")?m.getActiveObject().set("skewX",parseInt(e)):"img-skew-y"==oe(this).attr("id")?m.getActiveObject().set("skewY",parseInt(e)):"img-rotate"==oe(this).attr("id")?m.getActiveObject().set("angle",parseInt(e)):"img-opacity"==oe(this).attr("id")&&m.getActiveObject().set("opacity",parseFloat(e)),m.requestRenderAll()}),h.find("#palleon-image-settings input[type=range]").bind("change",function(){m.fire("palleon:history",{type:"image",text:palleonParams.edited})}),h.find("#palleon-shape-select").on("change",function(){var e=oe(this).val();"none"==e||"custom"==e?h.find("#palleon-shape-add").prop("disabled",!0):h.find("#palleon-shape-add").prop("disabled",!1)}),h.find("#palleon-shape-add").on("click",function(){var e=h.find("#palleon-shape-select").val(),t="",a="";"circle"==e?(t=new fabric.Circle({radius:50,fill:"#fff",stroke:"#000",strokeWidth:0,objectType:"circle",width:100,height:100,gradientFill:"none",top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"})).controls={...fabric.Rect.prototype.controls,ml:new fabric.Control({visible:!1}),mb:new fabric.Control({visible:!1}),mr:new fabric.Control({visible:!1}),mt:new fabric.Control({visible:!1})}:"ellipse"==e?t=new fabric.Ellipse({rx:75,ry:50,fill:"#fff",stroke:"#000",strokeWidth:0,objectType:"ellipse",width:100,height:100,gradientFill:"none",top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"}):"square"==e?(t=new fabric.Rect({radius:50,fill:"#fff",stroke:"#000",strokeWidth:0,objectType:"square",width:100,height:100,gradientFill:"none",top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"})).controls={...fabric.Rect.prototype.controls,ml:new fabric.Control({visible:!1}),mb:new fabric.Control({visible:!1}),mr:new fabric.Control({visible:!1}),mt:new fabric.Control({visible:!1})}:"rectangle"==e?t=new fabric.Rect({radius:50,fill:"#fff",stroke:"#000",strokeWidth:0,objectType:"rectangle",width:200,height:150,gradientFill:"none",top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"}):"triangle"==e?t=new fabric.Triangle({radius:50,fill:"#fff",stroke:"#000",strokeWidth:0,objectType:"triangle",width:100,height:100,gradientFill:"none",top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"}):"trapezoid"==e?(a=[{x:-100,y:-50},{x:100,y:-50},{x:150,y:50},{x:-150,y:50}],t=new fabric.Polygon(a,{fill:"#fff",stroke:"#000",strokeWidth:0,objectType:"trapezoid",width:100,height:100,gradientFill:"none",top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"})):"emerald"==e?(a=[{x:850,y:75},{x:958,y:137.5},{x:958,y:262.5},{x:850,y:325},{x:742,y:262.5},{x:742,y:137.5}],t=new fabric.Polygon(a,{fill:"#fff",stroke:"#000",strokeWidth:0,objectType:"emerald",width:100,height:100,gradientFill:"none",top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"})):"star"==e&&(a=[{x:350,y:75},{x:380,y:160},{x:470,y:160},{x:400,y:215},{x:423,y:301},{x:350,y:250},{x:277,y:301},{x:303,y:215},{x:231,y:161},{x:321,y:161}],t=new fabric.Polygon(a,{fill:"#fff",stroke:"#000",strokeWidth:0,objectType:"star",width:100,height:100,gradientFill:"none",top:ae()[1]/2,left:ae()[0]/2,originX:"center",originY:"center"})),m.add(t),t.scaleToWidth(ae()[0]/6),t.isPartiallyOnScreen()&&t.scaleToHeight(ae()[1]/6),m.setActiveObject(t),m.requestRenderAll(),m.fire("palleon:history",{type:e,text:palleonParams.added})}),h.find("#palleon-shape-settings .palleon-colorpicker").bind("change",function(){var e=oe(this).val();"palleon-shape-color"==oe(this).attr("id")?m.getActiveObject().set("fill",e):"shape-outline-color"==oe(this).attr("id")&&m.getActiveObject().set("stroke",e),m.requestRenderAll(),m.fire("palleon:history",{type:m.getActiveObject().objectType,text:palleonParams.edited})}),h.find("#palleon-shape-settings input[type=range]").bind("input click",function(){var e=oe(this).val();"shape-skew-x"==oe(this).attr("id")?m.getActiveObject().set("skewX",parseInt(e)):"shape-skew-y"==oe(this).attr("id")?m.getActiveObject().set("skewY",parseInt(e)):"shape-rotate"==oe(this).attr("id")?m.getActiveObject().set("angle",parseInt(e)):"shape-opacity"==oe(this).attr("id")&&m.getActiveObject().set("opacity",parseFloat(e)),m.requestRenderAll()}),h.find("#palleon-shape-settings input[type=range]").bind("change",function(){m.fire("palleon:history",{type:m.getActiveObject().objectType,text:palleonParams.edited})}),h.find("#palleon-shape-settings input[type=number]").bind("input paste",function(){var e=parseInt(oe(this).val());"shape-outline-width"==oe(this).attr("id")?m.getActiveObject().set("strokeWidth",e):"shape-custom-width"==oe(this).attr("id")?(m.getActiveObject().set("width",e),m.getActiveObject().set("scaleX",1)):"shape-custom-height"==oe(this).attr("id")&&(m.getActiveObject().set("height",e),m.getActiveObject().set("scaleY",1)),m.requestRenderAll()}),h.find("#palleon-shape-settings input[type=number]").bind("input",function(){window.clearTimeout(k),k=setTimeout(function(){m.fire("palleon:history",{type:m.getActiveObject().objectType,text:palleonParams.edited})},500)}),h.find("#shape-custom-width").bind("input paste",function(){var e,t;h.find("#palleon-shape-ratio-lock").hasClass("active")&&(e=parseInt(oe(this).val()),t=parseInt(h.find("#palleon-shape-ratio-w").val()),t=e*parseInt(h.find("#palleon-shape-ratio-h").val())/t,h.find("#shape-custom-height").val(Math.round(t)),m.getActiveObject().set("height",t),m.getActiveObject().set("scaleY",1))}),h.find("#shape-custom-height").bind("input paste",function(){var e;h.find("#palleon-shape-ratio-lock").hasClass("active")&&(e=oe(this).val()*parseInt(h.find("#palleon-shape-ratio-w").val())/parseInt(h.find("#palleon-shape-ratio-h").val()),h.find("#shape-custom-width").val(Math.round(e)),m.getActiveObject().set("width",e),m.getActiveObject().set("scaleX",1))});function ne(r){var e=m.getObjects().filter(e=>"frame"==e.objectType);oe.each(e,function(e,t){var a=t.angle,l=t.width,n=t.height,i=ae()[0],o=ae()[1];0!=a&&180!=a&&-180!=a||(i=ae()[1],o=ae()[0]),"right"==r?0==a?a=90:90==a?a=180:180==a?a=270:270==a||-90==a?a=0:-180==a?a=-90:-270==a&&(a=-180):"left"==r&&(0==a?a=-90:-90==a?a=-180:-180==a?a=-270:-270==a||90==a?a=0:180==a?a=90:270==a&&(a=180)),t.set("left",ae()[0]/2),t.set("top",ae()[1]/2),t.set("scaleX",i/l),t.set("scaleY",o/n),t.set("angle",a)}),m.requestRenderAll(),m.fire("palleon:history",{type:"frame",text:palleonParams.edited})}h.find("#palleon-frame-search").on("keyup input",function(){h.find("#palleon-noframes").hide();var e=oe(this).val().toLowerCase().replace(/\s/g," ");""==e||e.length<1?(h.find("#palleon-frames-wrap li").show(),h.find("#palleon-frame-search-icon").html("search"),h.find("#palleon-frame-search-icon").removeClass("cancel")):(h.find("#palleon-frame-search-icon").html("clear"),h.find("#palleon-frame-search-icon").addClass("cancel"),e=e,h.find("#palleon-frames-wrap li").hide().filter('[data-keyword*="'+e+'"]').show(),0===h.find("#palleon-frames-wrap li:visible").length&&h.find("#palleon-noframes").show())}),h.find("#palleon-frame-search-icon").on("click",function(){oe(this).hasClass("cancel")&&(oe(this).removeClass("cancel"),oe(this).html("search"),h.find("#palleon-frame-search").val(""),h.find("#palleon-frames-wrap li").show(),h.find("#palleon-noframes").hide())}),h.find(".palleon-frames-grid").on("click",".palleon-frame img",function(){h.find("#palleon-canvas-loader").css("display","flex");var e=oe(this).parent().parent(),t=e.data("elsource");h.find(".palleon-frames-grid .palleon-frame").removeClass("active"),e.addClass("active"),fabric.loadSVGFromURL(t,function(e,t){var a=fabric.util.groupSVGElements(e,t),e=a.width,t=a.height;a.set("originX","center"),a.set("originY","center"),a.set("left",ae()[0]/2),a.set("top",ae()[1]/2),a.set("scaleX",(ae()[0]+2)/e),a.set("scaleY",(ae()[1]+2)/t),a.set("objectType","frame"),m.add(a),m.setActiveObject(a),m.requestRenderAll(),h.find("#palleon-canvas-loader").hide()},function(){},{crossOrigin:"anonymous"}),m.fire("palleon:history",{type:"frame",text:palleonParams.added})}),h.find("#palleon-frame-color").bind("change",function(){var l=oe(this).val(),e=m.getObjects().filter(e=>"frame"==e.objectType);oe.each(e,function(e,t){if(""!=t.fill&&t.set("fill",l),t._objects)for(var a=0;a<t._objects.length;a++)""!=t._objects[a].fill&&t._objects[a].set({fill:l})}),m.requestRenderAll(),m.fire("palleon:history",{type:"frame",text:palleonParams.edited})}),h.find("#palleon-rotate-right-frame").on("click",function(){ne("right")}),h.find("#palleon-rotate-left-frame").on("click",function(){ne("left")}),h.find("#palleon-flip-horizontal-frame").on("click",function(){var e=m.getObjects().filter(e=>"frame"==e.objectType);oe.each(e,function(e,t){t.toggle("flipX")}),m.requestRenderAll(),m.fire("palleon:history",{type:"frame",text:palleonParams.edited})}),h.find("#palleon-flip-vertical-frame").on("click",function(){var e=m.getObjects().filter(e=>"frame"==e.objectType);oe.each(e,function(e,t){t.toggle("flipY")}),m.requestRenderAll(),m.fire("palleon:history",{type:"frame",text:palleonParams.edited})});h.find("#palleon-element-search").on("keyup input",function(){h.find("#palleon-noelements").hide();var e=oe(this).val().toLowerCase().replace(/\s/g," ");""==e||e.length<1?(h.find("#palleon-elements-wrap li").show(),h.find("#palleon-element-search-icon").html("search"),h.find("#palleon-element-search-icon").removeClass("cancel")):(h.find("#palleon-element-search-icon").html("clear"),h.find("#palleon-element-search-icon").addClass("cancel"),e=e,h.find("#palleon-elements-wrap li").hide().filter('[data-keyword*="'+e+'"]').show(),0===h.find("#palleon-elements-wrap li:visible").length&&h.find("#palleon-noelements").show())}),h.find("#palleon-element-search-icon").on("click",function(){oe(this).hasClass("cancel")&&(oe(this).removeClass("cancel"),oe(this).html("search"),h.find("#palleon-element-search").val(""),h.find("#palleon-elements-wrap li").show(),h.find("#palleon-noelements").hide())}),h.find(".palleon-elements-grid").on("click",".palleon-element > *:first-child",function(){var e,t=oe(this).parent(),a=t.data("elsource");"palleon-icons-grid"==t.parent().attr("id")&&(e=h.find("#palleon-icon-style").val(),a=t.data("elsource")+"/"+e+"/24px.svg",console.log(a));var l=t.data("loader");"yes"==l&&h.find("#palleon-canvas-loader").css("display","flex"),h.find(".palleon-elements-grid .palleon-element").removeClass("active"),t.addClass("active"),fabric.loadSVGFromURL(a,function(e,t){t=fabric.util.groupSVGElements(e,t);t.set("originX","center"),t.set("originY","center"),t.set("left",ae()[0]/2),t.set("top",ae()[1]/2),t.set("objectType","element"),t.set("gradientFill","none"),m.add(t),t.scaleToWidth(ae()[0]/8),t.isPartiallyOnScreen()&&t.scaleToHeight(ae()[1]/8),m.setActiveObject(t),m.requestRenderAll(),"yes"==l&&h.find("#palleon-canvas-loader").hide()},function(){},{crossOrigin:"anonymous"}),m.fire("palleon:history",{type:"element",text:palleonParams.added})}),h.find("#palleon-element-upload").on("change",function(e){var t,a=new FileReader;a.onload=function(e){t=a.result,fabric.loadSVGFromURL(t,function(e,t){t=fabric.util.groupSVGElements(e,t);t.set("originX","center"),t.set("originY","center"),t.set("left",ae()[0]/2),t.set("top",ae()[1]/2),t.set("objectType","customSVG"),t.scaleToWidth(ae()[0]/2),t.scaleToHeight(ae()[1]/2),m.add(t),m.setActiveObject(t),m.requestRenderAll()},function(){},{crossOrigin:"anonymous"})},a.readAsDataURL(this.files[0]),m.fire("palleon:history",{type:"element",text:palleonParams.added})}),h.find("#palleon-element-color").bind("change",function(){var e=oe(this).val(),t=m.getActiveObject();if(""!=t.fill&&t.set("fill",e),t._objects)for(var a=0;a<t._objects.length;a++)""!=t._objects[a].fill&&t._objects[a].set({fill:e});m.requestRenderAll(),m.fire("palleon:history",{type:"element",text:palleonParams.edited})}),h.find("#element-flip-horizontal").on("click",function(){m.getActiveObject().toggle("flipX"),m.requestRenderAll(),m.fire("palleon:history",{type:"element",text:palleonParams.edited})}),h.find("#element-flip-vertical").on("click",function(){m.getActiveObject().toggle("flipY"),m.requestRenderAll(),m.fire("palleon:history",{type:"element",text:palleonParams.edited})}),h.find("#palleon-custom-element-options input[type=range]").bind("input click",function(){var e=oe(this).val();"element-skew-x"==oe(this).attr("id")?m.getActiveObject().set("skewX",parseInt(e)):"element-skew-y"==oe(this).attr("id")?m.getActiveObject().set("skewY",parseInt(e)):"element-rotate"==oe(this).attr("id")?m.getActiveObject().set("angle",parseInt(e)):"element-opacity"==oe(this).attr("id")&&m.getActiveObject().set("opacity",parseFloat(e)),m.requestRenderAll()}),h.find("#palleon-custom-element-options input[type=range]").bind("change",function(){m.fire("palleon:history",{type:"element",text:palleonParams.edited})}),h.find("#customsvg-flip-horizontal").on("click",function(){m.getActiveObject().toggle("flipX"),m.requestRenderAll(),m.fire("palleon:history",{type:"customSVG",text:palleonParams.edited})}),h.find("#customsvg-flip-vertical").on("click",function(){m.getActiveObject().toggle("flipY"),m.requestRenderAll(),m.fire("palleon:history",{type:"customSVG",text:palleonParams.edited})}),h.find("#palleon-custom-svg-options input[type=range]").bind("input click",function(){var e=oe(this).val();"customsvg-skew-x"==oe(this).attr("id")?m.getActiveObject().set("skewX",parseInt(e)):"customsvg-skew-y"==oe(this).attr("id")?m.getActiveObject().set("skewY",parseInt(e)):"customsvg-rotate"==oe(this).attr("id")?m.getActiveObject().set("angle",parseInt(e)):"customsvg-opacity"==oe(this).attr("id")&&m.getActiveObject().set("opacity",parseFloat(e)),m.requestRenderAll()}),h.find("#palleon-custom-svg-options input[type=range]").bind("change",function(){m.fire("palleon:history",{type:"customSVG",text:palleonParams.edited})});function ie(e,t){oe("#tm-cursor-1").remove(),h.find("#palleon-canvas-wrap").tmpointer({id:1,native_cursor:"enable",cursorSize:e,cursorColor:t})}h.find("#palleon-icon-search").on("keyup input",function(){h.find("#palleon-noicons").hide();var e=oe(this).val().toLowerCase().replace(/\s/g," ");""==e||e.length<1?(h.find("#palleon-icons-grid .palleon-element").css("display","flex"),h.find("#palleon-icon-search-icon").html("search"),h.find("#palleon-icon-search-icon").removeClass("cancel")):(h.find("#palleon-icon-search-icon").html("clear"),h.find("#palleon-icon-search-icon").addClass("cancel"),e=e,h.find("#palleon-icons-grid .palleon-element").css("display","none").filter('[title*="'+e+'"]').css("display","flex"),0===h.find("#palleon-icons-grid .palleon-element:visible").length&&h.find("#palleon-noicons").show())}),h.find("#palleon-icon-search-icon").on("click",function(){oe(this).hasClass("cancel")&&(oe(this).removeClass("cancel"),oe(this).html("search"),h.find("#palleon-icon-search").val(""),h.find("#palleon-icons-grid .palleon-element").css("display","flex"),h.find("#palleon-noicons").hide())}),h.find("#palleon-generate-qr-code").on("click",function(){var e=kjua({text:h.find("#palleon-qrcode-text").val(),render:"svg",size:h.find("#palleon-qrcode-size").val(),fill:h.find("#palleon-qrcode-fill").val(),back:h.find("#palleon-qrcode-back").val(),rounded:h.find("#palleon-qrcode-rounded").val(),mode:"label",label:h.find("#palleon-qrcode-label").val(),fontname:"sans",fontcolor:h.find("#palleon-qrcode-label-color").val(),mSize:h.find("#palleon-qrcode-label-size").val(),mPosX:h.find("#palleon-qrcode-label-position-x").val(),mPosY:h.find("#palleon-qrcode-label-position-y").val()}),a=ae()[1]/2,l=ae()[0]/2,n=m.getObjects().filter(e=>"printarea"==e.objectType)[0];n&&(a=n.top,l=n.left);e=(new XMLSerializer).serializeToString(e);fabric.loadSVGFromString(e,function(e,t){t=fabric.util.groupSVGElements(e,t);t.set("originX","center"),t.set("originY","center"),t.set("left",l),t.set("top",a),t.set("objectType","qrCode"),t.set("gradientFill","none"),t.controls={...fabric.Rect.prototype.controls,ml:new fabric.Control({visible:!1}),mb:new fabric.Control({visible:!1}),mr:new fabric.Control({visible:!1}),mt:new fabric.Control({visible:!1})},m.add(t),n?t.scaleToWidth(.5*n.width*m.getZoom()):(t.scaleToWidth(ae()[0]/8),t.isPartiallyOnScreen()&&t.scaleToHeight(ae()[1]/8)),m.setActiveObject(t),m.requestRenderAll()})}),h.find("#palleon-draw-btn").on("click",function(){oe(this).hasClass("active")?(h.find("#palleon-draw-undo").prop("disabled",!0),oe("#tm-cursor-1").remove(),h.find("#palleon-draw-settings").hide(),h.find("#palleon-icon-menu").css("pointer-events","auto"),oe(this).removeClass("active"),m.isDrawingMode=!1,oe(this).html('<span class="material-icons">edit</span>Start Drawing')):(h.find("#palleon-draw-undo").prop("disabled",!1),h.find("#palleon-draw-settings").show(),h.find("#palleon-icon-menu").css("pointer-events","none"),oe(this).addClass("active"),h.find("#palleon-brush-select").trigger("change"),m.isDrawingMode=!0,oe(this).html('<span class="material-icons">close</span>Stop Drawing'))}),h.find("#palleon-brush-select").on("change",function(){var e,t,a,l,n=oe(this).val();"pencil"==n?(e=new fabric.PencilBrush(m),m.freeDrawingBrush=e):"circle"==n?(t=new fabric.CircleBrush(m),m.freeDrawingBrush=t):"spray"==n?(t=new fabric.SprayBrush(m),m.freeDrawingBrush=t):"hline"==n?(a=new fabric.PatternBrush(m),(m.freeDrawingBrush=a).getPatternSrc=function(){var e=parseInt(h.find("#brush-pattern-width").val()),t=e/2,a=fabric.document.createElement("canvas");a.width=a.height=e;var l=a.getContext("2d");return l.strokeStyle=h.find("#brush-color").val(),l.lineWidth=t,l.beginPath(),l.moveTo(t,0),l.lineTo(t,e),l.closePath(),l.stroke(),a}):"vline"==n?(a=new fabric.PatternBrush(m),(m.freeDrawingBrush=a).getPatternSrc=function(){var e=parseInt(h.find("#brush-pattern-width").val()),t=e/2,a=fabric.document.createElement("canvas");a.width=a.height=e;var l=a.getContext("2d");return l.strokeStyle=h.find("#brush-color").val(),l.lineWidth=t,l.beginPath(),l.moveTo(0,t),l.lineTo(e,t),l.closePath(),l.stroke(),a}):"square"==n?(l=new fabric.PatternBrush(m),(m.freeDrawingBrush=l).getPatternSrc=function(){var e=parseInt(h.find("#brush-pattern-width").val()),t=parseInt(h.find("#brush-pattern-distance").val()),a=fabric.document.createElement("canvas");a.width=a.height=e+t;t=a.getContext("2d");return t.fillStyle=h.find("#brush-color").val(),t.fillRect(0,0,e,e),a}):"erase"==n&&(l=new fabric.EraserBrush(m),m.freeDrawingBrush=l),(v=m.freeDrawingBrush).getPatternSrc&&(v.source=v.getPatternSrc.call(v)),v.width=parseInt(h.find("#brush-width").val()),"erase"==n?(h.find("#not-erase-brush").hide(),v.shadow=null,v.color="#E91E63"):(m.freeDrawingBrush.inverted=!1,h.find("#palleon-draw-undo").removeClass("active"),h.find("#not-erase-brush").show(),v.color=h.find("#brush-color").val()),h.find("#palleon-brush-shadow").is(":checked")?v.shadow=y:v.shadow=null,ie(v.width*m.getZoom(),v.color),"hline"==n||"vline"==n||"square"==n?h.find("#palleon-brush-pattern-width").css("display","flex"):h.find("#palleon-brush-pattern-width").css("display","none"),"square"==n?h.find("#palleon-brush-pattern-distance").css("display","flex"):h.find("#palleon-brush-pattern-distance").css("display","none")}),h.find("#palleon-brush-shadow").on("change",function(){y=new fabric.Shadow({color:h.find("#brush-shadow-color").val(),blur:h.find("#brush-shadow-width").val(),offsetX:h.find("#brush-shadow-shadow-offset-x").val(),offsetY:h.find("#brush-shadow-shadow-offset-y").val()}),oe(this).is(":checked")?v.shadow=y:v.shadow=null}),h.find("#palleon-draw-settings input[type=number]").bind("input paste keyup keydown",function(){"brush-width"==oe(this).attr("id")?(v.width=parseInt(oe(this).val()),ie(v.width*m.getZoom(),v.color)):"brush-shadow-shadow-offset-x"==oe(this).attr("id")?y.offsetX=parseInt(oe(this).val()):"brush-shadow-shadow-offset-y"==oe(this).attr("id")?y.offsetY=parseInt(oe(this).val()):"brush-shadow-width"==oe(this).attr("id")?y.blur=parseInt(oe(this).val()):"brush-pattern-width"!=oe(this).attr("id")&&"brush-pattern-distance"!=oe(this).attr("id")||h.find("#palleon-brush-select").trigger("change")}),h.find("#palleon-draw-settings .palleon-colorpicker").bind("change",function(){"brush-color"==oe(this).attr("id")?(v.color=oe(this).val(),ie(v.width*m.getZoom(),v.color),h.find("#palleon-brush-select").trigger("change")):"brush-shadow-color"==oe(this).attr("id")&&(y.color=oe(this).val())}),h.find("#palleon-draw-undo").on("click",function(){var e;"erase"==h.find("#palleon-brush-select").val()?m.backgroundImage&&(oe(this).hasClass("active")?(oe(this).removeClass("active"),m.freeDrawingBrush.inverted=!1):(oe(this).addClass("active"),m.freeDrawingBrush.inverted=!0)):(e=m.getObjects().filter(e=>"drawing"==e.objectType).slice(-1)[0],m.remove(e),m.requestRenderAll())}),document.onkeydown=function(e){var t=m.getActiveObject();switch(e.keyCode){case 38:t&&(--t.top,m.requestRenderAll());break;case 40:t&&(t.top+=1,m.requestRenderAll());break;case 37:t&&(--t.left,m.requestRenderAll());break;case 39:t&&(t.left+=1,m.requestRenderAll())}},h.find("#custom-theme").on("change",function(){var e=oe(this).val(),e=d.baseURL+"css/"+e+".css";oe("#palleon-theme-link").attr("href",e)}),h.find("#custom-font-size").on("input",function(){oe("html").css("font-size",oe(this).val()+"px")}),h.find("#custom-background").on("change",function(){var e=oe(this).val();""!=e&&h.find("#palleon-content").css("background-color",e)}),h.find("#custom-image-background").on("change",function(){var e=oe(this).val();h.find("#palleon-canvas-color").spectrum("set",e),m.backgroundColor=""==e?"transparent":e,m.requestRenderAll()}),h.find("#ruler-guide-color").on("change",function(){var e=oe(this).val();""!=e&&(h.find(".guide.h, .guide.v").css("border-color",e),initAligningGuidelines(m))}),h.find("#ruler-guide-size").on("input",function(){var e=oe(this).val();h.find(".guide.h, .guide.v").css("border-width",e+"px"),initAligningGuidelines(m)}),initAligningGuidelines(m),oe(window).on("resize",function(){J()}),d.customFunctions.call(this,h,m,e)}}(jQuery);