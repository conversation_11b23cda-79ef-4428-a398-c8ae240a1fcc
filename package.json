{"name": "test-replicate", "version": "1.0.0", "type": "module", "scripts": {"start": "node server.js", "build": "npm install"}, "engines": {"node": ">=18.x"}, "dependencies": {"@aws-sdk/client-s3": "^3.723.0", "axios": "^1.7.9", "backblaze-b2": "^1.7.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "canvas": "^3.0.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.0", "form-data": "^4.0.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.13.0", "mongoose": "^8.9.0", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "replicate": "^0.25.1", "sharp": "^0.33.5", "stripe": "^17.4.0"}}