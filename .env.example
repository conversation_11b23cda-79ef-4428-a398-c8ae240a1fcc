# MongoDB Connection String
MONGODB_URI=mongodb://localhost:27017/sticker-app

# Server Port
PORT=3005

# JWT Secret (generate a strong secret in production)
JWT_SECRET=your-jwt-secret-key

# Node Environment
NODE_ENV=development

# Replicate API Token (Required)
# Get your token at: https://replicate.com/account
# This is required for image generation using Stable Diffusion XL
REPLICATE_API_TOKEN=your-replicate-api-token

# Storage Configuration
STORAGE_TYPE=local
UPLOAD_DIR=./uploads

# Optional APIs
GOOGLE_APPLICATION_CREDENTIALS=path/to/google-credentials.json
REMOVE_BG_API_KEY=your-remove-bg-api-key

# Analytics Settings
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=30
