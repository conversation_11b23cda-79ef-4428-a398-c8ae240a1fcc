/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    padding: 20px;
}

.app-container {
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Canvas Styles */
.canvas-container {
    padding: 20px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #eee;
}

canvas {
    width: 100%;
    height: 400px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: block;
    margin-bottom: 20px;
}

/* Text Input Controls */
.text-input-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 150px;
}

label {
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 14px;
}

input[type="text"], select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

input[type="range"] {
    width: 100%;
    margin: 5px 0;
}

input[type="color"] {
    width: 50px;
    height: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* Shading Panel Styles */
.shading-panel {
    padding: 20px;
}

.panel-tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    border: none;
    background: none;
    font-size: 14px;
    font-weight: bold;
    color: #777;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: transparent;
}

.tab-btn.active {
    color: #333;
}

.tab-btn.active:after {
    background-color: #3498db;
}

.tab-btn:hover {
    color: #333;
}

.tab-content {
    display: none;
    visibility: hidden;
    height: 0;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-content.active {
    display: block !important;
    visibility: visible;
    height: auto;
    overflow: visible;
    opacity: 1;
}

/* Shadow Presets */
.shadow-presets {
    margin-bottom: 25px;
}

.preset-icons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.preset {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preset:hover {
    border-color: #aaa;
}

.preset.active {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* Shadow Controls */
.shadow-controls {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.shadow-controls .control-group {
    display: grid;
    grid-template-columns: 1fr 120px;
    align-items: center;
    gap: 10px;
}

.shadow-controls label {
    font-size: 14px;
    grid-column: 1 / -1;
    margin-bottom: 5px;
}

.shadow-controls input[type="range"] {
    width: 100%;
}

.shadow-controls span {
    text-align: right;
    font-size: 14px;
    color: #777;
}

/* Color picker and opacity slider in same row */
.shadow-controls .control-group input[type="color"] {
    grid-row: 2;
    grid-column: 1;
    width: 40px;
    height: 30px;
    margin-right: 10px;
}

.shadow-controls .control-group input[type="range"]:first-of-type {
    grid-row: 2;
    grid-column: 1;
    margin-left: 50px;
    width: calc(100% - 50px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .text-input-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .control-group {
        min-width: 100%;
    }
    
    .panel-tabs {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1;
        min-width: 120px;
        padding: 10px;
        text-align: center;
    }
}
