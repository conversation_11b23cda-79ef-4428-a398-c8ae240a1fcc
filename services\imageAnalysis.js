const vision = require('@google-cloud/vision');
const axios = require('axios');
const sharp = require('sharp');
const Tag = require('../models/Tag');

class ImageAnalyzer {
    constructor() {
        // Initialize Google Cloud Vision client
        this.visionClient = new vision.ImageAnnotatorClient({
            keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS
        });
    }

    async analyzeImage(imageBuffer) {
        try {
            const [result] = await this.visionClient.annotateImage({
                image: { content: imageBuffer.toString('base64') },
                features: [
                    { type: 'LABEL_DETECTION' },
                    { type: 'OBJECT_LOCALIZATION' },
                    { type: 'IMAGE_PROPERTIES' },
                    { type: 'SAFE_SEARCH_DETECTION' }
                ]
            });

            return {
                labels: this._processLabels(result.labelAnnotations),
                objects: this._processObjects(result.localizedObjectAnnotations),
                colors: this._processColors(result.imagePropertiesAnnotation),
                safeSearch: result.safeSearchAnnotation,
            };
        } catch (error) {
            console.error('Error analyzing image:', error);
            throw new Error('Failed to analyze image');
        }
    }

    async generateTags(analysis) {
        try {
            const tags = new Set();

            // Add labels as tags
            analysis.labels.forEach(label => {
                if (label.score > 0.7) {
                    tags.add(label.description.toLowerCase());
                }
            });

            // Add objects as tags
            analysis.objects.forEach(obj => {
                if (obj.score > 0.7) {
                    tags.add(obj.name.toLowerCase());
                }
            });

            // Add color tags
            analysis.colors.dominant.forEach(color => {
                tags.add(color.name.toLowerCase());
            });

            // Create or get existing tags
            const tagPromises = Array.from(tags).map(async tagName => {
                let tag = await Tag.findOne({ name: tagName });
                if (!tag) {
                    tag = new Tag({
                        name: tagName,
                        color: this._getRandomColor(),
                        isAutoGenerated: true
                    });
                    await tag.save();
                }
                return tag._id;
            });

            return await Promise.all(tagPromises);
        } catch (error) {
            console.error('Error generating tags:', error);
            return [];
        }
    }

    async enhanceImage(buffer, options = {}) {
        try {
            let pipeline = sharp(buffer);

            // Apply basic enhancements
            pipeline = pipeline.normalize();

            if (options.removeBackground) {
                // Use remove.bg API for background removal
                const removeBgResponse = await axios.post('https://api.remove.bg/v1.0/removebg', {
                    image_file_b64: buffer.toString('base64'),
                    size: 'auto'
                }, {
                    headers: {
                        'X-Api-Key': process.env.REMOVE_BG_API_KEY
                    }
                });
                buffer = Buffer.from(removeBgResponse.data, 'base64');
                pipeline = sharp(buffer);
            }

            // Apply additional enhancements based on options
            if (options.adjustments) {
                pipeline = pipeline
                    .modulate({
                        brightness: options.adjustments.brightness || 1,
                        saturation: options.adjustments.saturation || 1,
                        hue: options.adjustments.hue || 0
                    });
            }

            if (options.format) {
                pipeline = pipeline.toFormat(options.format, {
                    quality: options.quality || 80,
                    effort: 6
                });
            }

            return await pipeline.toBuffer();
        } catch (error) {
            console.error('Error enhancing image:', error);
            throw new Error('Failed to enhance image');
        }
    }

    _processLabels(labels) {
        return labels.map(label => ({
            description: label.description,
            score: label.score
        }));
    }

    _processObjects(objects) {
        return objects.map(obj => ({
            name: obj.name,
            score: obj.score,
            bounds: obj.boundingPoly.normalizedVertices
        }));
    }

    _processColors(properties) {
        const colors = properties.dominantColors.colors;
        return {
            dominant: colors.map(color => ({
                name: this._getColorName(color.color),
                rgb: color.color,
                score: color.score
            })).slice(0, 5)
        };
    }

    _getColorName(color) {
        // Simple color naming based on RGB values
        const r = color.red;
        const g = color.green;
        const b = color.blue;

        if (Math.max(r, g, b) < 30) return 'black';
        if (Math.min(r, g, b) > 225) return 'white';

        const colors = {
            red: r > Math.max(g, b) + 50,
            green: g > Math.max(r, b) + 50,
            blue: b > Math.max(r, g) + 50
        };

        return Object.entries(colors).find(([, value]) => value)?.[0] || 'gray';
    }

    _getRandomColor() {
        const colors = ['#2196F3', '#4CAF50', '#F44336', '#FFC107', '#9C27B0', '#00BCD4'];
        return colors[Math.floor(Math.random() * colors.length)];
    }
}

module.exports = new ImageAnalyzer();
