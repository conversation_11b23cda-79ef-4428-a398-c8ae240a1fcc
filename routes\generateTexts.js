import express from 'express';
import Replicate from 'replicate';
import { auth } from '../middleware/auth.js';

const router = express.Router();

// Initialize Replicate
const replicate = new Replicate({
    auth: process.env.REPLICATE_API_TOKEN,
});

// @desc    Generate texts for template using AI
// @route   POST /api/generate-texts
// @access  Private
router.post('/', auth, async (req, res) => {
    try {
        const { prompt, objectInput, textCount } = req.body;

        if (!prompt || !objectInput) {
            return res.status(400).json({ error: 'Missing required fields: prompt and objectInput' });
        }

        console.log('🤖 Text generation request:', {
            objectInput,
            textCount,
            promptLength: prompt.length
        });

        // Prepare the input for OpenAI o4-mini
        const input = {
            prompt: prompt,
            image_input: [],
            system_prompt: "marketing",
            reasoning_effort: "medium",
            max_completion_tokens: 4096
        };

        console.log('🤖 Calling OpenAI o4-mini with input:', input);

        // Call OpenAI o4-mini via Replicate
        let fullResponse = '';
        for await (const event of replicate.stream("openai/o4-mini", { input })) {
            fullResponse += event.toString();
        }

        console.log('🤖 Raw AI response:', fullResponse);

        // Parse the response to extract texts
        const texts = parseTextResponse(fullResponse, textCount || 3);
        
        console.log('🤖 Parsed texts:', texts);

        if (texts.length === 0) {
            throw new Error('Failed to parse any texts from AI response');
        }

        res.json({
            success: true,
            texts: texts,
            rawResponse: fullResponse
        });

    } catch (error) {
        console.error('Error generating texts:', error);
        res.status(500).json({ 
            error: 'Failed to generate texts',
            details: error.message 
        });
    }
});

// Helper function to parse the AI response and extract texts
function parseTextResponse(response, expectedCount = 3) {
    const texts = [];

    try {
        console.log('🔍 Parsing AI response:', response);

        // Look for patterns like "text-01:", "text-02:", etc.
        const lines = response.split('\n');

        // Create an array to store texts in the correct order
        const orderedTexts = new Array(expectedCount).fill(null);

        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
            const trimmedLine = lines[lineIndex].trim();
            if (!trimmedLine) continue;

            // Try different patterns for each expected text number
            for (let i = 1; i <= expectedCount; i++) {
                if (orderedTexts[i - 1]) continue; // Skip if already found

                const patterns = [
                    // Standard format with colon
                    new RegExp(`text-0?${i}:\\s*(.+)`, 'i'),
                    new RegExp(`Text\\s*${i}:\\s*(.+)`, 'i'),
                    // Format without colon (just text-01 followed by content)
                    new RegExp(`^text-0?${i}\\s+(.+)`, 'i'),
                    new RegExp(`^Text\\s*${i}\\s+(.+)`, 'i'),
                    // Numbered list format
                    new RegExp(`${i}\\.\\s*(.+)`, 'i'),
                    // Just the label without content (content might be on next line)
                    new RegExp(`^text-0?${i}\\s*$`, 'i'),
                    new RegExp(`^Text\\s*${i}\\s*$`, 'i')
                ];

                for (const pattern of patterns) {
                    const match = trimmedLine.match(pattern);
                    if (match) {
                        let foundText = '';

                        if (match[1]) {
                            // Content found on same line
                            foundText = match[1].trim();
                        } else {
                            // Label only, check next line for content
                            if (lineIndex + 1 < lines.length) {
                                const nextLine = lines[lineIndex + 1].trim();
                                if (nextLine && !nextLine.match(/^text-\d+/i)) {
                                    foundText = nextLine;
                                }
                            }
                        }

                        if (foundText) {
                            // Remove any quotes or extra formatting
                            foundText = foundText.replace(/^["']|["']$/g, '');
                            orderedTexts[i - 1] = foundText;
                            console.log(`✅ Found text-${i}: "${foundText}"`);
                            break;
                        }
                    }
                }
            }
        }

        // Add non-null texts to the result array in order
        for (let i = 0; i < expectedCount; i++) {
            if (orderedTexts[i]) {
                texts.push(orderedTexts[i]);
            }
        }

        console.log('📝 Parsed texts in order:', texts);

        // If we couldn't parse with patterns, try to extract any meaningful text
        if (texts.length === 0) {
            console.log('⚠️ No structured texts found, trying fallback parsing...');
            const meaningfulLines = lines
                .filter(line => line.trim().length > 3)
                .filter(line => !line.toLowerCase().includes('recommend'))
                .filter(line => !line.toLowerCase().includes('t-shirt'))
                .slice(0, expectedCount);

            texts.push(...meaningfulLines.map(line => line.trim()));
            console.log('📝 Fallback texts:', texts);
        }

    } catch (error) {
        console.error('Error parsing text response:', error);
    }

    return texts.slice(0, expectedCount); // Ensure we don't return more than expected
}

export default router;
