<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Effect Layering Test v6</title>
    <style>
        body { background-color: #333; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
        canvas { background-color: #555; border: 1px solid white; }
    </style>
</head>
<body>
    <canvas id="testCanvas" width="600" height="400"></canvas>
    <script>
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');

        // --- Configuration ---
        const text = {
            content: "TEST",
            x: canvas.width / 2,
            y: canvas.height / 2,
            font: "bold 100px Arial",
            color: "#FFFFFF", // White fill for contrast
            textAlign: "center",
            textBaseline: "middle"
        };

        const shadow = {
            color: "rgba(0, 0, 0, 0.8)", // Darker shadow
            offsetX: 10, // Increased offset
            offsetY: 10,
            blur: 8      // Increased blur
        };

        const stroke = {
            color: "#FF0000", // Red stroke
            width: 4
        };

        const decoration = {
            type: "horizontalLines", // Example decoration
            color: "#00FF00", // Green lines
            lineWeight: 5,
            lineDistance: 10
        };
        // --- End Configuration ---

        // --- Drawing Logic ---
        ctx.font = text.font;
        ctx.textAlign = text.textAlign;
        ctx.textBaseline = text.textBaseline;

        // --- Layer 1: Shadow ---
        // Apply shadow settings
        ctx.save();
        ctx.shadowColor = shadow.color;
        ctx.shadowOffsetX = shadow.offsetX;
        ctx.shadowOffsetY = shadow.offsetY;
        ctx.shadowBlur = shadow.blur;
        // Draw the text shape with an opaque color JUST to cast the shadow.
        // This draw will be completely covered by subsequent layers.
        ctx.fillStyle = 'black'; // Use an opaque color
        ctx.fillText(text.content, text.x, text.y);
        ctx.restore(); // Restore context (removes shadow settings)

        // --- Layer 2: Decoration / Fill ---
        if (decoration.type === "horizontalLines") {
            ctx.save(); // Save context before clipping decoration

            // Draw the decoration pattern (only the lines)
            ctx.fillStyle = decoration.color;
            const metrics = ctx.measureText(text.content);
            const approxHeight = parseInt(ctx.font.match(/\d+/)[0]) * 1.2;
            const approxWidth = metrics.width;
            const textTop = text.y - approxHeight / 2;
            const textBottom = text.y + approxHeight / 2;
            const patternX = text.x - approxWidth * 0.6; // Adjust for alignment
            const patternWidth = approxWidth * 1.2;

            for (let y = textTop; y < textBottom; y += decoration.lineDistance + decoration.lineWeight) {
                ctx.fillRect(patternX, y, patternWidth, decoration.lineWeight);
            }

            // Clip the pattern to the text shape
            ctx.globalCompositeOperation = 'destination-in';

            // Draw the text shape again (as a mask), using an opaque color
            ctx.fillStyle = 'white'; // Use white (or any opaque color) for the mask
            ctx.fillText(text.content, text.x, text.y);

            // Restore context (removes clipping and resets composite operation)
            ctx.restore();
        } else {
             // Fallback: Standard Fill if no decoration is active
             ctx.fillStyle = text.color;
             ctx.fillText(text.content, text.x, text.y);
        }

        // --- Layer 3: Stroke ---
        // Draw stroke last so it outlines the decorated text
        if (stroke.width > 0) {
            ctx.strokeStyle = stroke.color;
            ctx.lineWidth = stroke.width;
            ctx.strokeText(text.content, text.x, text.y);
        }

        console.log("Drawing complete. Order: Shadow -> Decoration/Fill (clipped) -> Stroke");

    </script>
</body>
</html>
