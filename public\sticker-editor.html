<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sticker Editor</title>
    <link rel="stylesheet" href="/css/fonts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/styles.css">
    <style>
        .editor-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 20px;
            display: flex;
            gap: 2rem;
        }

        .left-sidebar {
            width: 70px;
            background: #1a1a1a;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 0;
            position: relative;
        }

        .sidebar-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
            color: #ccc;
            font-size: 12px;
            position: relative;
            border-left: 3px solid transparent;
        }

        .sidebar-item:hover {
            background: #2a2a2a;
        }

        .sidebar-item.active {
            border-left-color: #0066ff;
            color: #fff;
        }

        .sidebar-item i {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .sidebar-tab {
            position: absolute;
            left: 70px;
            top: 0;
            background: #1a1a1a;
            border-radius: 0 8px 8px 0;
            padding: 15px;
            width: 250px;
            min-height: 200px;
            display: none;
            z-index: 10;
            box-shadow: 5px 0 15px rgba(0, 0, 0, 0.2);
        }

        .sidebar-tab.active {
            display: block;
        }

        .sidebar-tab-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333;
        }

        .sidebar-tab-header h3 {
            margin: 0;
            color: #fff;
        }

        .sidebar-tab-header .close-tab {
            background: none;
            border: none;
            color: #ccc;
            cursor: pointer;
            font-size: 16px;
        }

        .sidebar-tab-content {
            color: #fff;
        }

        .text-preset {
            background: #2a2a2a;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .text-preset:hover {
            background: #333;
        }

        .shapes-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .shape-item {
            background: #2a2a2a;
            aspect-ratio: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            cursor: pointer;
        }

        .shape-item:hover {
            background: #333;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .image-item {
            background: #2a2a2a;
            aspect-ratio: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            cursor: pointer;
            overflow: hidden;
        }

        .image-item img {
            max-width: 100%;
            max-height: 100%;
        }

        .image-item:hover {
            opacity: 0.8;
        }

        .canvas-container {
            flex: 1;
            background: #1a1a1a;
            border-radius: 8px;
            padding: 20px;
            min-height: 600px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        #editor-canvas {
            max-width: 100%;
            background: #2a2a2a;
            border: 2px solid #333;
        }

        .tools-panel {
            width: 300px;
            background: #1a1a1a;
            border-radius: 8px;
            padding: 20px;
        }

        .tool-section {
            margin-bottom: 20px;
        }

        .tool-section h3 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            color: #fff;
        }

        .color-picker {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .color-option.active {
            border-color: #fff;
        }

        .button-group {
            display: flex; 
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }

        .control-button {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .control-button:hover {
            background: #333;
        }

        .control-button.primary {
            background: #0066ff;
        }

        .control-button.primary:hover {
            background: #0052cc;
        }

        .control-button.small {
            padding: 8px;
            width: 36px;
            height: 36px;
            justify-content: center;
        }

        .control-button.small.active {
            background: #0066ff;
        }

        .control-button.danger {
            background: #dc3545;
        }

        .control-button.danger:hover {
            background: #bd2130;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .slider {
            flex: 1;
            -webkit-appearance: none;
            appearance: none;
            height: 4px;
            background: #2a2a2a;
            border-radius: 2px;
            outline: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #0066ff;
            border-radius: 50%;
            cursor: pointer;
        }

        .slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: #0066ff;
            border-radius: 50%;
            cursor: pointer;
        }

        .slider-value {
            min-width: 40px;
            color: #fff;
            font-size: 14px;
        }

        .canvas-menu {
            position: absolute;
            right: 20px;
            top: 20px;
            background: rgba(88, 88, 88, 0.95);
            border-radius: 8px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .canvas-menu button {
            display: flex;
            align-items: center;
            gap: 8px;
            background: none;
            border: none;
            color: #000000;
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
            width: 100%;
            text-align: left;
        }

        .canvas-menu button:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .canvas-menu button img {
            width: 20px;
            height: 20px;
            opacity: 0.8;
        }

        .canvas-menu button:hover img {
            opacity: 1;
        }

        .inpainting-controls {
          
            margin-top: 15px;
        }

        .inpainting-controls.active {
            display: block;
        }

        .inpainting-prompt {
            width: 100%;
            min-height: 60px;
            padding: 8px;
            background: #2a2a2a;
            border: 1px solid #333;
            color: white;
            border-radius: 4px;
            margin-bottom: 10px;
            resize: vertical;
            font-family: inherit;
        }

        .inpainting-buttons {
            display: flex;
            gap: 8px;
        }

        .brush-size-control {
            margin: 10px 0;
        }

        .brush-size-control label {
            color: white;
            display: block;
            margin-bottom: 5px;
        }

        .brush-size-control input {
            width: 100%;
        }

        .model-selector-container {
            margin-bottom: 15px;
        }

        .model-selector-container label {
            display: block;
            margin-bottom: 5px;
            color: #fff;
            font-size: 14px;
        }

        #modelSelector {
            width: 100%;
            padding: 8px;
            background: #2a2a2a;
            border: 1px solid #333;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }

        #modelSelector option {
            padding: 8px;
            background: #2a2a2a;
        }

        #modelSelector:hover {
            border-color: #444;
        }

        /* Mode sections styling */
        .mode-section {
        
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #444;
        }

        .mode-section.active {
            display: block;
        }

        .mode-button {
            width: 100%;
            margin-bottom: 0.5rem;
            padding: 0.75rem;
            border: none;
            border-radius: 4px;
            background: #2a2a2a;
            color: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .mode-button:hover {
            background: #3a3a3a;
        }

        .mode-button.active {
            background: #0066ff;
        }

        /* Toast notification styles */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 4px;
            z-index: 1000;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
        }

        .toast.error {
            background: rgba(220, 53, 69, 0.9);
        }

        .toast.success {
            background: rgba(40, 167, 69, 0.9);
        }

        .toast.info {
            background: rgba(0, 123, 255, 0.9);
        }

        /* Scrollbar styles for the font menu */
        .font-menu::-webkit-scrollbar {
            width: 8px;
        }

        .font-menu::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        .font-menu::-webkit-scrollbar-thumb {
            background: #333;
            border-radius: 4px;
        }

        .font-menu::-webkit-scrollbar-thumb:hover {
            background: #444;
        }

        .slider-controls {
            margin-top: 20px;
        }

        .slider-group {
            margin-bottom: 20px;
        }

        .color-controls {
            margin-top: 20px;
        }

        .color-group {
            margin-bottom: 20px;
        }

        #imageInput {
            display: none;
        }

        .upload-area {
            border: 2px dashed #333;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .upload-area:hover {
            border-color: #0066ff;
        }

        .brush-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 15px;
        }

        .color-picker-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        #currentColor {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid #333;
            margin-right: 10px;
            background: #ffffff;
        }

        .brush-controls .control-button {
            flex: 1;
        }

        .brush-controls .slider-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .brush-controls label {
            color: #fff;
            font-size: 0.9rem;
        }

        .brush-controls input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }

        .mode-section {
            display: none;
        }

       

        .mode-button.active {
            background: #0066ff;
        }

        #inpaintingTools {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #444;
        }

        #inpaintingTools h3 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            color: #fff;
        }

        #inpaintingModelSelect {
            width: 100%;
            padding: 8px;
            background: #2a2a2a;
            border: 1px solid #333;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }

        #inpaintingPrompt {
            width: 100%;
            min-height: 60px;
            padding: 8px;
            background: #2a2a2a;
            border: 1px solid #333;
            color: white;
            border-radius: 4px;
            margin-bottom: 10px;
            resize: vertical;
            font-family: inherit;
        }

        #applyInpaintingBtn {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        #applyInpaintingBtn:hover {
            background: #333;
        }

        #paintTools {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #444;
        }

        #paintTools h3 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            color: #fff;
        }

        .brush-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 15px;
        }

        .color-picker-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        #currentColor {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid #333;
            margin-right: 10px;
            background: #ffffff;
        }

        .brush-controls .control-button {
            flex: 1;
        }

        .brush-controls .slider-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .brush-controls label {
            color: #fff;
            font-size: 0.9rem;
        }

        .brush-controls input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }

        #inpaintingTools {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #444;
        }

        #inpaintingTools h3 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            color: #fff;
        }

        .slider-container {
            margin: 15px 0;
            display: flex;
            flex-direction: column;
        }
        
        .slider-container label {
            margin-bottom: 5px;
            color: #fff;
        }
        
        .slider {
            -webkit-appearance: none;
            width: 100%;
            height: 10px;
            border-radius: 5px;
            background: #2a2a2a;
            outline: none;
            margin-bottom: 5px;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #0066ff;
            cursor: pointer;
        }
        
        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #0066ff;
            cursor: pointer;
            border: none;
        }
        
        .slider-value {
            color: #fff;
            font-weight: bold;
            text-align: right;
        }
    </style>
</head>
<body>
    <div id="topbar"></div>

    <div class="editor-container">
        <div class="left-sidebar">
            <div class="sidebar-item" data-tab="text-tab">
                <i class="fas fa-font"></i>
                <span>Text</span>
            </div>
            <div class="sidebar-item" data-tab="shapes-tab">
                <i class="fas fa-shapes"></i>
                <span>Shapes & Lines</span>
            </div>
            <div class="sidebar-item" data-tab="images-tab">
                <i class="fas fa-image"></i>
                <span>Images</span>
            </div>
            <div class="sidebar-item" data-tab="charts-tab">
                <i class="fas fa-chart-bar"></i>
                <span>Charts</span>
            </div>
            <div class="sidebar-item" data-tab="maps-tab">
                <i class="fas fa-map-marker-alt"></i>
                <span>Maps</span>
            </div>
            <div class="sidebar-item" data-tab="projects-tab">
                <i class="fas fa-folder"></i>
                <span>Projects</span>
            </div>
            
            <!-- Text Tab -->
            <div id="text-tab" class="sidebar-tab">
                <div class="sidebar-tab-header">
                    <h3>Text</h3>
                    <button class="close-tab"><i class="fas fa-times"></i></button>
                </div>
                <div class="sidebar-tab-content">
                    <div class="text-preset" data-text="Heading 1">
                        <h2>Heading 1</h2>
                    </div>
                    <div class="text-preset" data-text="Heading 2">
                        <h3>Heading 2</h3>
                    </div>
                    <div class="text-preset" data-text="Heading 3">
                        <h4>Heading 3</h4>
                    </div>
                    <div class="text-preset" data-text="Paragraph">
                        <p>Paragraph</p>
                    </div>
                </div>
            </div>
            
            <!-- Shapes Tab -->
            <div id="shapes-tab" class="sidebar-tab">
                <div class="sidebar-tab-header">
                    <h3>Shapes & Lines</h3>
                    <button class="close-tab"><i class="fas fa-times"></i></button>
                </div>
                <div class="sidebar-tab-content">
                    <div class="shapes-grid">
                        <div class="shape-item" data-shape="rectangle">
                            <i class="fas fa-square"></i>
                        </div>
                        <div class="shape-item" data-shape="circle">
                            <i class="fas fa-circle"></i>
                        </div>
                        <div class="shape-item" data-shape="triangle">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="shape-item" data-shape="line">
                            <i class="fas fa-minus"></i>
                        </div>
                        <div class="shape-item" data-shape="arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="shape-item" data-shape="star">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Images Tab -->
            <div id="images-tab" class="sidebar-tab">
                <div class="sidebar-tab-header">
                    <h3>Images</h3>
                    <button class="close-tab"><i class="fas fa-times"></i></button>
                </div>
                <div class="sidebar-tab-content">
                    <button class="control-button primary" style="width: 100%; margin-bottom: 15px;">
                        <i class="fas fa-upload"></i> Upload Image
                    </button>
                    <div class="images-grid">
                        <div class="image-item">
                            <img src="data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22150%22%20height%3D%22150%22%20viewBox%3D%220%200%20150%20150%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_16e0f9a9a6b%20text%20%7B%20fill%3A%23999%3Bfont-weight%3Anormal%3Bfont-family%3A-apple-system%2CBlinkMacSystemFont%2C%26quot%3BSegoe%20UI%26quot%3B%2CRoboto%2C%26quot%3BHelvetica%20Neue%26quot%3B%2CArial%2C%26quot%3BNoto%20Sans%26quot%3B%2Csans-serif%2C%26quot%3BApple%20Color%20Emoji%26quot%3B%2C%26quot%3BSegoe%20UI%20Emoji%26quot%3B%2C%26quot%3BSegoe%20UI%20Symbol%26quot%3B%2C%26quot%3BNoto%20Color%20Emoji%26quot%3B%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_16e0f9a9a6b%22%3E%3Crect%20width%3D%22150%22%20height%3D%22150%22%20fill%3D%22%23373940%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2255%22%20y%3D%2280%22%3EImage%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E" alt="Placeholder">
                        </div>
                        <div class="image-item">
                            <img src="data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22150%22%20height%3D%22150%22%20viewBox%3D%220%200%20150%20150%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_16e0f9a9a6b%20text%20%7B%20fill%3A%23999%3Bfont-weight%3Anormal%3Bfont-family%3A-apple-system%2CBlinkMacSystemFont%2C%26quot%3BSegoe%20UI%26quot%3B%2CRoboto%2C%26quot%3BHelvetica%20Neue%26quot%3B%2CArial%2C%26quot%3BNoto%20Sans%26quot%3B%2Csans-serif%2C%26quot%3BApple%20Color%20Emoji%26quot%3B%2C%26quot%3BSegoe%20UI%20Emoji%26quot%3B%2C%26quot%3BSegoe%20UI%20Symbol%26quot%3B%2C%26quot%3BNoto%20Color%20Emoji%26quot%3B%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_16e0f9a9a6b%22%3E%3Crect%20width%3D%22150%22%20height%3D%22150%22%20fill%3D%22%23373940%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2255%22%20y%3D%2280%22%3EImage%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E" alt="Placeholder">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Tab -->
            <div id="charts-tab" class="sidebar-tab">
                <div class="sidebar-tab-header">
                    <h3>Charts</h3>
                    <button class="close-tab"><i class="fas fa-times"></i></button>
                </div>
                <div class="sidebar-tab-content">
                    <p>Chart elements coming soon...</p>
                </div>
            </div>
            
            <!-- Maps Tab -->
            <div id="maps-tab" class="sidebar-tab">
                <div class="sidebar-tab-header">
                    <h3>Maps</h3>
                    <button class="close-tab"><i class="fas fa-times"></i></button>
                </div>
                <div class="sidebar-tab-content">
                    <p>Map elements coming soon...</p>
                </div>
            </div>
            
            <!-- Projects Tab -->
            <div id="projects-tab" class="sidebar-tab">
                <div class="sidebar-tab-header">
                    <h3>Projects</h3>
                    <button class="close-tab"><i class="fas fa-times"></i></button>
                </div>
                <div class="sidebar-tab-content">
                    <p>Your saved projects will appear here...</p>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="editor-canvas" width="2048" height="2048"></canvas>
            <div class="canvas-menu">
                <button id="addToCollectionBtn" title="Add to Collection">
                    <img src="/images/ph--folder-simple-plus-light.svg" alt="Add to Collection" />
                    Add to Collection
                </button>
                <button id="downloadBtn" title="Download">
                    <img src="/images/ph--download-simple-light.svg" alt="Download" />
                    Download
                </button>
                <button id="deleteImageBtn" title="Delete Image">
                    <img src="/images/ph--trash-light.svg" alt="Delete" />
                    Delete Image
                </button>
            </div>
        </div>
        
        <div class="tools-panel">
            <!-- Mode Selection Buttons -->
            <div class="tool-section" id="modeButtons">
                <h3>Editor Modes</h3>
                <div class="button-group">
                   
                    <button class="control-button mode-button" id="inpaintingModeBtn">
                        <i class="fas fa-magic"></i> Start Inpainting
                    </button>
                </div>
            </div>

            <!-- Tool sections - all hidden by default -->
            <div id="inpaintingTools" class="mode-section">
                <h3>Inpainting</h3>
                <select id="inpaintingModelSelect">
                    <option value="">Select Model</option>
                </select>
                <div class="slider-container">
                    <label for="brushSize">Brush Size</label>
                    <input type="range" id="brushSizeSlider" class="slider" min="5" max="100" value="20">
                    <span class="slider-value" id="brushSizeValue">20</span>
                </div>
                <input type="text" id="inpaintingPrompt" placeholder="Enter prompt for inpainting...">
                <button class="control-button" id="applyInpaintingBtn">
                    <i class="fas fa-magic"></i> Apply Inpainting
                </button>
            </div>

            
        </div>
    </div>

    <script type="module">
        import { createTopbar } from '/js/components/Topbar.js';
        import { Toast } from '/js/components/Toast.js';
        import '/js/components/CollectionModal.js';
  
        import InpaintingMode from '/js/inpaintingMode.js';

        // Initialize topbar
        createTopbar();

        // Get canvas and context
        const canvas = document.getElementById('editor-canvas');
        const ctx = canvas.getContext('2d');

        // Create editor instance
        const editor = {
            canvas: canvas,
            ctx: ctx,
            brushSize: 20,
            isPaintMode: false,
            isInpaintingMode: false
        };

        // Initialize modes
        const textMode = new TextMode(canvas, ctx);
        const paintingMode = new PaintingMode(canvas, ctx);
        const inpaintingMode = new InpaintingMode(canvas, ctx, editor);

        // Current active mode
        let currentMode = null;

        // Initialize sidebar tabs
        const sidebarItems = document.querySelectorAll('.sidebar-item');
        const sidebarTabs = document.querySelectorAll('.sidebar-tab');
        const closeBtns = document.querySelectorAll('.close-tab');
        
        // Handle sidebar item clicks
        sidebarItems.forEach(item => {
            item.addEventListener('click', () => {
                const tabId = item.getAttribute('data-tab');
                const tab = document.getElementById(tabId);
                
                // Toggle active state for sidebar items
                sidebarItems.forEach(i => i.classList.remove('active'));
                item.classList.add('active');
                
                // Hide all tabs and show the selected one
                sidebarTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
            });
        });
        
        // Handle close button clicks
        closeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // Hide the parent tab
                const tab = btn.closest('.sidebar-tab');
                tab.classList.remove('active');
                
                // Remove active state from sidebar items
                sidebarItems.forEach(item => {
                    if (item.getAttribute('data-tab') === tab.id) {
                        item.classList.remove('active');
                    }
                });
            });
        });
        
        // Handle text preset clicks
        const textPresets = document.querySelectorAll('.text-preset');
        textPresets.forEach(preset => {
            preset.addEventListener('click', () => {
                const text = preset.getAttribute('data-text');
                // Add text to canvas
                textMode.addText(text);
                // Close the tab
                document.getElementById('text-tab').classList.remove('active');
                sidebarItems[0].classList.remove('active');
            });
        });

        // Handle shape item clicks
        const shapeItems = document.querySelectorAll('.shape-item');
        shapeItems.forEach(item => {
            item.addEventListener('click', () => {
                const shape = item.getAttribute('data-shape');
                // Add shape to canvas (to be implemented)
                console.log('Adding shape:', shape);
                // Close the tab
                document.getElementById('shapes-tab').classList.remove('active');
                sidebarItems[1].classList.remove('active');
                
                // Show toast notification
                const toast = document.createElement('toast-notification');
                document.body.appendChild(toast);
                toast.show(`Shape functionality coming soon`);
            });
        });

        // Handle image upload button
        const uploadImageBtn = document.querySelector('#images-tab .control-button');
        if (uploadImageBtn) {
            uploadImageBtn.addEventListener('click', () => {
                // Create a file input element
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);
                
                // Trigger click on the file input
                fileInput.click();
                
                // Handle file selection
                fileInput.addEventListener('change', (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                            const imageUrl = event.target.result;
                            // Load the image into the editor
                            if (textMode && typeof textMode.loadImage === 'function') {
                                textMode.loadImage(imageUrl);
                            }
                            
                            // Close the tab
                            document.getElementById('images-tab').classList.remove('active');
                            sidebarItems[2].classList.remove('active');
                        };
                        reader.readAsDataURL(file);
                    }
                    
                    // Remove the file input from the DOM
                    document.body.removeChild(fileInput);
                });
            });
        }

        // Load inpainting models
        async function loadInpaintingModels() {
            try {
                const response = await fetch('/config/inpaintingModels.json');
                const data = await response.json();
                const modelSelect = document.getElementById('inpaintingModelSelect');
                
                data.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    option.dataset.model = JSON.stringify(model);
                    modelSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading inpainting models:', error);
                const toast = document.createElement('toast-notification');
                document.body.appendChild(toast);
                toast.show('Error loading inpainting models', 'error');
            }
        }

        // Load models when page loads
        loadInpaintingModels();

        // Mode switching buttons
        const inpaintingModeBtn = document.getElementById('inpaintingModeBtn');
        const applyInpaintingBtn = document.getElementById('applyInpaintingBtn');

        inpaintingModeBtn.addEventListener('click', () => {
            disableCurrentMode();
            currentMode = inpaintingMode;
            currentMode.initialize();
            
            // Ensure brush size is set from slider
            const brushSizeSlider = document.getElementById('brushSizeSlider');
            if (brushSizeSlider) {
                const size = parseInt(brushSizeSlider.value);
                currentMode.setBrushSize(size);
                document.getElementById('brushSizeValue').textContent = size;
            }
            
            // Show inpainting tools
            document.getElementById('inpaintingTools').style.display = 'block';
            
            inpaintingModeBtn.classList.add('active');
            inpaintingModeBtn.style.display = 'none';
            applyInpaintingBtn.style.display = 'block';
            const toast = document.createElement('toast-notification');
            document.body.appendChild(toast);
            toast.show('Inpainting mode activated');
        });

        // Brush size control
        const brushSizeSlider = document.getElementById('brushSizeSlider');
        const brushSizeValue = document.getElementById('brushSizeValue');
        
        if (brushSizeSlider) {
            brushSizeSlider.addEventListener('input', (e) => {
                const size = parseInt(e.target.value);
                editor.brushSize = size; // Update editor brush size
                brushSizeValue.textContent = size;
                if (currentMode === paintingMode || currentMode === inpaintingMode) {
                    currentMode.setBrushSize(size);
                }
                console.log('Brush size updated to:', size);
            });
        }

        applyInpaintingBtn.addEventListener('click', async () => {
            const modelSelect = document.getElementById('inpaintingModelSelect');
            const selectedOption = modelSelect.selectedOptions[0];
            const inpaintingPrompt = document.getElementById('inpaintingPrompt');
            
            if (!selectedOption || !selectedOption.value) {
                const toast = document.createElement('toast-notification');
                document.body.appendChild(toast);
                toast.show('Please select an inpainting model', 'error');
                return;
            }

            if (!inpaintingPrompt.value.trim()) {
                const toast = document.createElement('toast-notification');
                document.body.appendChild(toast);
                toast.show('Please enter a prompt for inpainting', 'error');
                return;
            }

            try {
                const modelConfig = JSON.parse(selectedOption.dataset.model);
                const mask = inpaintingMode.getMask();
                
                // Here you would typically send these to your backend:
                // - Original image (canvas.toDataURL())
                // - Mask (mask.toDataURL())
                // - Model config (modelConfig)
                // - Prompt (inpaintingPrompt.value)
                
                const toast = document.createElement('toast-notification');
                document.body.appendChild(toast);
                toast.show('Inpainting request sent!', 'success');
                
                // Reset the UI
                inpaintingModeBtn.style.display = 'block';
                applyInpaintingBtn.style.display = 'none';
                disableCurrentMode();
            } catch (error) {
                console.error('Error applying inpainting:', error);
                const toast = document.createElement('toast-notification');
                document.body.appendChild(toast);
                toast.show('Error applying inpainting', 'error');
            }
        });

        // Helper function to disable current mode
        function disableCurrentMode() {
            if (currentMode) {
                // Remove active states from buttons
                document.querySelectorAll('.control-button').forEach(btn => btn.classList.remove('active'));
                
                // Clear any ongoing operations
                if (currentMode === inpaintingMode) {
                    currentMode.clearMask();
                }
                
                // Reset inpainting UI
                inpaintingModeBtn.style.display = 'block';
                applyInpaintingBtn.style.display = 'none';
            }
        }

        // Initialize with painting mode by default
        currentMode = paintingMode;
        paintingMode.initialize();
    </script>
    <script type="module" src="/js/sticker-editor.js"></script>
    <collection-modal></collection-modal>
</body>
</html>
