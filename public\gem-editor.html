<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Text Effects - Multiple Objects V3 (Fixed)</title>
    <style>
        /* CSS largely the same */
        body { font-family: sans-serif; }
        canvas { border: 1px solid #eee; display: block; margin-bottom: 10px; background-color: #f9f9f9; cursor: default; }
        canvas.dragging { cursor: grabbing; }
        label { display: inline-block; width: 150px; text-align: right; margin-right: 5px; font-size: 12px; vertical-align: middle; } input[type="range"] { vertical-align: middle; width: 150px; } input[type="text"] { vertical-align: middle; } input[type="checkbox"] { vertical-align: middle; } select { vertical-align: middle; font-size: 12px;} input[type="color"] { vertical-align: middle; height: 25px; width: 50px; padding: 0; border: 1px solid #ccc; } button { font-size: 12px; vertical-align: middle; margin-left: 5px;} span.slider-value { display:inline-block; width:40px; text-align:left; font:12px sans-serif; vertical-align: middle; margin-left: 5px;} div.control-group { margin-bottom: 5px; display: flex; align-items: center; } .parameter-control { display: none; }
        .normal .normal-param, .warp .warp-param, .skew .skew-param, .circle .circle-param, .curve .curve-param, .horizontalLines .horizontal-lines-param, .colorCut .color-cut-param, .obliqueLines .oblique-lines-param, .fadingLinesCut .fading-lines-cut-param, .stroke-enabled .stroke-param, .shadow .shadow-param, .block-shadow .block-shadow-param, .line-shadow .line-shadow-param, .detailed-3d .detailed-3d-param { display: flex; flex-direction: column; }
        .parameter-control.normal-param, .parameter-control.warp-param, .parameter-control.skew-param, .parameter-control.circle-param, .parameter-control.curve-param { display: flex; }
        .general-controls { display: flex; flex-direction: column; } .warp .horizontal-skew { display: flex; flex-direction: row; } .skew .horizontal-skew, .skew .vertical-skew { display: flex; flex-direction: row; } .parameter-control h3, .parameter-control h4 { margin-top: 10px; margin-bottom: 5px; font-size: 1em; border-top: 1px solid #ccc; padding-top: 8px;} h4 { font-size: 0.9em; border-top: none; padding-top: 0; margin-top: 8px; margin-left: 10px; } .parameter-control .effect-control > div, .parameter-control .control-group { display: flex; align-items: center; margin-bottom: 5px; flex-direction: row; } .parameter-control .effect-control > div label, .parameter-control .control-group label { width: 150px; text-align: right; margin-right: 10px; font-size: 12px; } .parameter-control .slider-container { flex-grow: 1; display: flex; align-items: center; } .parameter-control input[type="range"] { width: 150px; } .parameter-control .slider-value { width: 40px; text-align: left; } .parameter-control .simplified-color-picker { display: flex; align-items: center; flex-grow: 1; margin-left: 5px;} .parameter-control input[type="color"] { height: 25px; width: 50px; margin-left: 0;} .parameter-control .radio-group { display: flex; flex-direction: column; align-items: flex-start; margin-left: 10px;} .parameter-control .radio-container { display: flex; align-items: center; margin-bottom: 2px;} .parameter-control .radio-container label { width: auto; text-align: left; margin-right: 0;} .parameter-control .radio-container span { width: auto; margin-left: 5px;} .warp-param .shift-center-control { display: none; } .triangle-warp-enabled .shift-center-control { display: flex; } .font-style-controls label { width: auto; margin-left: 15px; margin-right: 3px;}
        .text-actions button { margin-left: 10px; }
        #deleteTextBtn { background-color: #f44336; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; }
        #deleteTextBtn:hover:enabled { background-color: #d32f2f; }
        #deleteTextBtn:disabled { background-color: #cccccc; cursor: not-allowed;}
        #addEditBtn { background-color: #4CAF50; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; min-width: 50px; }
        #addEditBtn:hover { background-color: #45a049;}
    </style>
</head>
<body class="normal">

    <!-- Canvas -->
    <canvas id="demo" width="600" height="300"></canvas>

    <!-- Controls (HTML Unchanged) -->
    <div class="general-controls"> <div class="control-group"> <label>Text:</label> <input id="iText" type="text" value=""> <span class="text-actions"> <button id="addEditBtn">Add</button> <button id="deleteTextBtn" title="Delete Selected Text" disabled>Delete</button> </span> </div> <div class="control-group"> <label>Text Color:</label> <div class="simplified-color-picker"> <input id="iTextColor" type="color" value="#FF0000"> </div> </div> <div class="control-group"> <label>Font:</label> <select id="iFontFamily"> <option value="Arial">Arial</option> <option value="Verdana">Verdana</option> <option value="Georgia">Georgia</option> <option value="Times New Roman">Times New Roman</option> <option value="Courier New">Courier New</option> <option value="Impact">Impact</option> <option value="Comic Sans MS">Comic Sans MS</option> </select> <div class="font-style-controls"> <label for="iBold">Bold:</label><input id="iBold" type="checkbox" checked> <label for="iItalic">Italic:</label><input id="iItalic" type="checkbox"> </div> </div> <div class="control-group"> <label>Font Size:</label> <div class="slider-container"> <input id="iFontSize" type="range" min="20" max="150" value="120" step="1"> <span class="slider-value" id="vFontSize">120</span> </div> </div> <div class="control-group"> <label>Rotation:</label> <div class="slider-container"> <input id="iRotation" type="range" min="-180" max="180" value="0" step="1"> <span class="slider-value" id="vRotation">0°</span> </div> </div> </div>
     <div class="parameter-control normal-param warp-param skew-param circle-param curve-param control-group"> <label>Effect Mode:</label> <select id="effectMode"> <option value="normal">Normal</option> <option value="warp">Warp</option> <option value="skew">Skew Only</option> <option value="circle">Circular</option> <option value="curve">Curved</option> </select> </div> <div class="parameter-control normal-param warp-param skew-param circle-param curve-param control-group"> <label>Decoration:</label> <select id="linesDecoration"> <option value="noDecoration">No Decoration</option> <option value="diagonalLines">Diagonal Lines</option> <option value="horizontalLines">Horizontal Lines</option> <option value="colorCut">Color Cut</option> <option value="obliqueLines">Oblique Lines</option> <option value="fadingLinesCut">Fading Lines Cut</option> </select> </div> <div class="parameter-control normal-param warp-param skew-param circle-param curve-param control-group"> <label for="strokeToggle">Stroke:</label> <select id="strokeToggle"> <option value="noStroke" selected>No Stroke</option> <option value="stroke">Standard Stroke</option> </select> </div> <div class="parameter-control normal-param warp-param skew-param circle-param curve-param control-group"> <label>Shadow:</label> <select id="shadow"> <option value="noShadow" selected>No Shadow</option> <option value="shadow">Standard Shadow</option> <option value="blockShadow">Block Shadow</option> <option value="lineShadow">Line Shadow</option> <option value="detailed3D">Detailed 3D</option> </select> </div>
    <!-- EFFECT SPECIFIC CONTROLS --> <div class="parameter-control horizontal-skew control-group" id="horizontalSkewControl"> <label>Horizontal Skew:</label><div class="slider-container"><input type="range" id="skewSlider" min="-50" max="50" value="0" step="1"><span class="slider-value" id="vSkew">0</span></div> </div> <div class="parameter-control vertical-skew control-group" id="verticalSkewControl"> <label>Vertical Skew:</label><div class="slider-container"><input type="range" id="skewYSlider" min="-50" max="50" value="0" step="1"><span class="slider-value" id="vSkewY">0</span></div> </div> <div class="parameter-control warp-param"> <h3>Warp Settings</h3> <div class="control-group"><label>Curve Amount:</label><div class="slider-container"><input id="iCurve" type="range" min=0 max=200 value=158><span class="slider-value" id="vCurve">158</span></div></div> <div class="control-group"><label>Source Offset Y:</label><div class="slider-container"><input id="iOffset" type="range" min=0 max=100 value=10><span class="slider-value" id="vOffset">10</span></div></div> <div class="control-group"><label>Source Sample Height:</label><div class="slider-container"><input id="iHeight" type="range" min=1 max=100 value=100><span class="slider-value" id="vHeight">100</span></div></div> <div class="control-group"><label>Warp Bottom Position:</label><div class="slider-container"><input id="iBottom" type="range" min=0 max=200 value=150><span class="slider-value" id="vBottom">150</span></div></div> <div class="control-group"> <label>Triangle Warp:</label> <input id="iTriangle" type="checkbox"> </div> <div class="control-group shift-center-control"> <label>Shift Center:</label> <div class="slider-container"> <input id="iShiftCenter" type="range" min="0" max="200" value="100"> <span class="slider-value" id="vShiftCenter">100</span> </div> </div> </div> <div class="parameter-control circle-param"><h3>Circular Text Settings</h3><div class="control-group"><label>Diameter:</label><div class="slider-container"><input id="iDiameter" type="range" min=100 max=600 value=300 step=1><span class="slider-value" id="vDiameter">300</span></div></div><div class="control-group"><label>Kerning:</label><div class="slider-container"><input id="iKerning" type="range" min=-10 max=30 value="0" step=1><span class="slider-value" id="vKerning">0</span></div></div><div class="control-group"><label>Flip Text:</label><input id="iFlip" type="checkbox"></div></div> <div class="parameter-control curve-param"><h3>Curved Text Settings</h3><div class="control-group"><label>Curve Amount:</label><div class="slider-container"><input id="iCurveAmount" type="range" min=0 max=200 value=80 step=1><span class="slider-value" id="vCurveAmount">80</span></div></div><div class="control-group"><label>Letter Spacing:</label><div class="slider-container"><input id="iCurveKerning" type="range" min=-10 max=30 value=0 step=1><span class="slider-value" id="vCurveKerning">0</span></div></div><div class="control-group"><label>Flip Direction:</label><input id="iCurveFlip" type="checkbox"></div></div>
    <!-- DECORATION PARAMETERS --> <div class="parameter-control horizontal-lines-param"><h3>Horizontal Lines Settings</h3><div class="effect-control"><div class="control-group"><label for="hWeight">Weight:</label><div class="slider-container"><input type="range" id="hWeight" min="1" max="20" value="3"><span class="slider-value" id="vHWeight">3</span></div></div><div class="control-group"><label for="hDistance">Distance:</label><div class="slider-container"><input type="range" id="hDistance" min="1" max="50" value="7"><span class="slider-value" id="vHDistance">7</span></div></div><div class="control-group"><label for="hColor">Line Color:</label><div class="simplified-color-picker"><input type="color" id="hColor" value="#0000FF"></div></div></div></div> <div class="parameter-control color-cut-param"><h3>Color Cut Settings</h3><div class="effect-control"><div class="control-group"><label for="ccDistance">Distance (%):</label><div class="slider-container"><input type="range" id="ccDistance" min="1" max="100" value="50"><span class="slider-value" id="vCcDistance">50</span></div></div><div class="control-group fill-direction"><label>Fill Direction:</label><div class="radio-group" style="flex-direction: row;"><label class="radio-container"><input type="radio" name="ccFillDirection" id="ccFillTop" value="top" checked><span>Top</span></label><label class="radio-container" style="margin-left: 10px;"><input type="radio" name="ccFillDirection" id="ccFillBottom" value="bottom"><span>Bottom</span></label></div></div><div class="control-group"><label for="ccColor">Fill Color:</label><div class="simplified-color-picker"><input type="color" id="ccColor" value="#00FF00"></div></div></div></div> <div class="parameter-control oblique-lines-param"><h3>Oblique Lines Settings</h3><div class="effect-control"><div class="control-group"><label for="oWeight">Weight:</label><div class="slider-container"><input type="range" id="oWeight" min="1" max="20" value="4"><span class="slider-value" id="vOWeight">4</span></div></div><div class="control-group"><label for="oDistance">Distance:</label><div class="slider-container"><input type="range" id="oDistance" min="1" max="50" value="3"><span class="slider-value" id="vODistance">3</span></div></div><div class="control-group"><label for="oColor">Line Color:</label><div class="simplified-color-picker"><input type="color" id="oColor" value="#0000FF"></div></div></div></div> <div class="parameter-control fading-lines-cut-param"> <h3>Fading Lines Cut Settings</h3> <div class="effect-control"> <div class="control-group"> <label for="flcDistance">Distance (%):</label> <div class="slider-container"> <input type="range" id="flcDistance" min="1" max="100" value="62"> <span class="slider-value" id="vFlcDistance">62</span> </div> </div> <div class="control-group fill-direction"> <label>Direction:</label> <div class="radio-group" style="flex-direction: row;"> <label class="radio-container"> <input type="radio" name="flcFillDirection" id="flcFillTop" value="top" checked> <span>Solid Top / Lines Bottom</span> </label> <label class="radio-container" style="margin-left: 10px;"> <input type="radio" name="flcFillDirection" id="flcFillBottom" value="bottom"> <span>Lines Top / Solid Bottom</span> </label> </div> </div> <div class="control-group"> <label for="flcColor">Line/Solid Color:</label> <div class="simplified-color-picker"> <input type="color" id="flcColor" value="#cccccc"> </div> </div> <div class="control-group"> <label for="flcMaxWeight">Line Weight:</label> <div class="slider-container"> <input type="range" id="flcMaxWeight" min="1" max="20" value="3"> <span class="slider-value" id="vFlcMaxWeight">3</span> </div> </div> <div class="control-group"> <label for="flcSpacing">Line Spacing:</label> <div class="slider-container"> <input type="range" id="flcSpacing" min="1" max="30" value="10"> <span class="slider-value" id="vFlcSpacing">10</span> </div> </div> </div> </div>
    <!-- STROKE PARAMETERS --> <div class="parameter-control stroke-param"><h3>Standard Stroke Settings</h3><div class="control-group"><label for="strokeWidth">Stroke Width:</label><div class="slider-container"><input type="range" id="strokeWidth" min="0" max="20" value="1"><span class="slider-value" id="vStrokeWidth">1</span></div></div><div class="control-group"><label for="strokeColor">Stroke Color:</label><div class="simplified-color-picker"><input type="color" id="strokeColor" value="#000000"></div></div></div>
    <!-- SHADOW PARAMETERS --> <div class="parameter-control shadow-param"><h3>Standard Shadow Settings</h3><div class="control-group"><label for="shadowColor">Shadow Color:</label><div class="simplified-color-picker"><input type="color" id="shadowColor" value="#000000"></div></div><div class="control-group"><label for="shadowOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="shadowOffsetX" class="slider" min="-20" max="20" value="5" step="1"><span class="slider-value" id="vShadowOffsetX">5</span></div></div><div class="control-group"><label for="shadowOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="shadowOffsetY" class="slider" min="-20" max="20" value="5" step="1"><span class="slider-value" id="vShadowOffsetY">5</span></div></div><div class="control-group"><label for="shadowBlur">Blur:</label><div class="slider-container"><input type="range" id="shadowBlur" class="slider" min="0" max="50" value="10" step="1"><span class="slider-value" id="vShadowBlur">10</span></div></div></div> <div class="parameter-control block-shadow-param"><h3>Block Shadow Settings</h3><div class="control-group"><label for="blockShadowColor">Shadow Color:</label><div class="simplified-color-picker"><input type="color" id="blockShadowColor" value="#000000"></div></div><div class="control-group"><label for="blockShadowOpacity">Opacity:</label><div class="slider-container"><input type="range" id="blockShadowOpacity" class="slider" min="0" max="100" value="100" step="1"><span class="slider-value" id="vBlockShadowOpacity">100%</span></div></div><div class="control-group"><label for="blockShadowOffset">Extrude Distance:</label><div class="slider-container"><input type="range" id="blockShadowOffset" class="slider" min="0" max="100" value="40" step="1"><span class="slider-value" id="vBlockShadowOffset">40</span></div></div><div class="control-group"><label for="blockShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="blockShadowAngle" class="slider" min="-180" max="180" value="-58" step="1"><span class="slider-value" id="vBlockShadowAngle">-58°</span></div></div><div class="control-group"><label for="blockShadowBlur">Blur:</label><div class="slider-container"><input type="range" id="blockShadowBlur" class="slider" min="0" max="50" value="5" step="1"><span class="slider-value" id="vBlockShadowBlur">5</span></div></div></div> <div class="parameter-control line-shadow-param"> <h3>Line Shadow Settings</h3> <div class="control-group"> <label for="lineShadowColor">Shadow Color:</label> <div class="simplified-color-picker"> <input type="color" id="lineShadowColor" value="#AAAAAA"> </div> </div> <div class="control-group"> <label for="lineShadowDistance">Distance:</label> <div class="slider-container"> <input type="range" id="lineShadowDistance" class="slider" min="0" max="50" value="15" step="1"> <span class="slider-value" id="vLineShadowDistance">15</span> </div> </div> <div class="control-group"> <label for="lineShadowAngle">Angle:</label> <div class="slider-container"> <input type="range" id="lineShadowAngle" class="slider" min="-180" max="180" value="-45" step="1"> <span class="slider-value" id="vLineShadowAngle">-45°</span> </div> </div> <div class="control-group"> <label for="lineShadowThickness">Thickness:</label> <div class="slider-container"> <input type="range" id="lineShadowThickness" class="slider" min="1" max="20" value="5" step="1"> <span class="slider-value" id="vLineShadowThickness">5</span> </div> </div> </div> <div class="parameter-control detailed-3d-param"><h3>Detailed 3D Settings</h3><h4>Extrusion Shadow</h4><div class="control-group"><label for="detailed3DPrimaryColor">Color:</label><div class="simplified-color-picker"><input type="color" id="detailed3DPrimaryColor" value="#000000"></div></div><div class="control-group"><label for="detailed3DPrimaryOpacity">Opacity:</label><div class="slider-container"><input type="range" id="detailed3DPrimaryOpacity" class="slider" min="0" max="100" value="100" step="1"><span class="slider-value" id="vDetailed3DPrimaryOpacity">100%</span></div></div><div class="control-group"><label for="detailed3DOffset">Distance:</label><div class="slider-container"><input type="range" id="detailed3DOffset" class="slider" min="0" max="100" value="36" step="1"><span class="slider-value" id="vDetailed3DOffset">36</span></div></div><div class="control-group"><label for="detailed3DAngle">Angle:</label><div class="slider-container"><input type="range" id="detailed3DAngle" class="slider" min="-180" max="180" value="-63" step="1"><span class="slider-value" id="vDetailed3DAngle">-63°</span></div></div><div class="control-group"><label for="detailed3DBlur">Blur:</label><div class="slider-container"><input type="range" id="detailed3DBlur" class="slider" min="0" max="50" value="5" step="1"><span class="slider-value" id="vDetailed3DBlur">5</span></div></div><h4>Front Outline</h4><div class="control-group"><label for="detailed3DSecondaryColor">Color:</label><div class="simplified-color-picker"><input type="color" id="detailed3DSecondaryColor" value="#00FF00"></div></div><div class="control-group"><label for="detailed3DSecondaryOpacity">Opacity:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOpacity" class="slider" min="0" max="100" value="100" step="1"><span class="slider-value" id="vDetailed3DSecondaryOpacity">100%</span></div></div><div class="control-group"><label for="detailed3DSecondaryWidth">Width:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryWidth" class="slider" min="0" max="20" value="0" step="1"><span class="slider-value" id="vDetailed3DSecondaryWidth">0</span></div></div><div class="control-group"><label for="detailed3DSecondaryOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOffsetX" class="slider" min="-30" max="30" value="-5" step="1"><span class="slider-value" id="vDetailed3DSecondaryOffsetX">-5</span></div></div><div class="control-group"><label for="detailed3DSecondaryOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOffsetY" class="slider" min="-30" max="30" value="-5" step="1"><span class="slider-value" id="vDetailed3DSecondaryOffsetY">-5</span></div></div></div>

    <script>
        // --- Setup ---
        const canvas = document.getElementById("demo");
        const ctx = canvas.getContext("2d");
        const w = canvas.width; const h = canvas.height;

        // --- Offscreen Canvases (Shared) ---
        const os = document.createElement("canvas"); os.width = w; os.height = h; const octx = os.getContext("2d");
        const tempWarpCanvas = document.createElement("canvas"); tempWarpCanvas.width = w; tempWarpCanvas.height = h; const tempWarpCtx = tempWarpCanvas.getContext("2d");
        const letterCanvas = document.createElement("canvas");
        letterCanvas.width = 350; // Increased size for padding
        letterCanvas.height = 350; // Increased size for padding
        const letterCtx = letterCanvas.getContext("2d");

        // --- Controls References ---
        const iText = document.getElementById("iText"); const addEditBtn = document.getElementById("addEditBtn"); const deleteTextBtn = document.getElementById("deleteTextBtn"); const iTextColor = document.getElementById("iTextColor"); const iFontFamily = document.getElementById("iFontFamily"); const iBold = document.getElementById("iBold"); const iItalic = document.getElementById("iItalic"); const iFontSize = document.getElementById("iFontSize"); const iRotation = document.getElementById("iRotation"); const effectModeSelect = document.getElementById("effectMode"); const linesDecorationSelect = document.getElementById("linesDecoration"); const strokeToggle = document.getElementById("strokeToggle"); const shadowSelect = document.getElementById("shadow"); const skewSlider = document.getElementById("skewSlider"); const skewYSlider = document.getElementById("skewYSlider"); const iCurve = document.getElementById("iCurve"); const iOffset = document.getElementById("iOffset"); const iHeight = document.getElementById("iHeight"); const iBottom = document.getElementById("iBottom"); const iTriangle = document.getElementById("iTriangle"); const iShiftCenter = document.getElementById("iShiftCenter"); const iDiameter = document.getElementById("iDiameter"); const iKerning = document.getElementById("iKerning"); const iFlip = document.getElementById("iFlip"); const iCurveAmount = document.getElementById("iCurveAmount"); const iCurveKerning = document.getElementById("iCurveKerning"); const iCurveFlip = document.getElementById("iCurveFlip"); const hWeight = document.getElementById("hWeight"); const hDistance = document.getElementById("hDistance"); const hColor = document.getElementById("hColor"); const ccDistance = document.getElementById("ccDistance"); const ccColor = document.getElementById("ccColor"); const ccFillTop = document.getElementById("ccFillTop"); const ccFillBottom = document.getElementById("ccFillBottom"); const oWeight = document.getElementById("oWeight"); const oDistance = document.getElementById("oDistance"); const oColor = document.getElementById("oColor"); const flcDistance = document.getElementById("flcDistance"); const flcFillTop = document.getElementById("flcFillTop"); const flcFillBottom = document.getElementById("flcFillBottom"); const flcColor = document.getElementById("flcColor"); const flcMaxWeight = document.getElementById("flcMaxWeight"); const flcSpacing = document.getElementById("flcSpacing"); const strokeWidthSlider = document.getElementById("strokeWidth"); const strokeColorPicker = document.getElementById("strokeColor"); const shadowColorPicker = document.getElementById("shadowColor"); const shadowOffsetXSlider = document.getElementById("shadowOffsetX"); const shadowOffsetYSlider = document.getElementById("shadowOffsetY"); const shadowBlurSlider = document.getElementById("shadowBlur"); const blockShadowColorPicker = document.getElementById("blockShadowColor"); const blockShadowOpacitySlider = document.getElementById("blockShadowOpacity"); const blockShadowOffsetSlider = document.getElementById("blockShadowOffset"); const blockShadowAngleSlider = document.getElementById("blockShadowAngle"); const blockShadowBlurSlider = document.getElementById("blockShadowBlur"); const lineShadowColorPicker = document.getElementById("lineShadowColor"); const lineShadowDistanceSlider = document.getElementById("lineShadowDistance"); const lineShadowAngleSlider = document.getElementById("lineShadowAngle"); const lineShadowThicknessSlider = document.getElementById("lineShadowThickness"); const detailed3DPrimaryColorPicker = document.getElementById("detailed3DPrimaryColor"); const detailed3DPrimaryOpacitySlider = document.getElementById("detailed3DPrimaryOpacity"); const detailed3DOffsetSlider = document.getElementById("detailed3DOffset"); const detailed3DAngleSlider = document.getElementById("detailed3DAngle"); const detailed3DBlurSlider = document.getElementById("detailed3DBlur"); const detailed3DSecondaryColorPicker = document.getElementById("detailed3DSecondaryColor"); const detailed3DSecondaryOpacitySlider = document.getElementById("detailed3DSecondaryOpacity"); const detailed3DSecondaryWidthSlider = document.getElementById("detailed3DSecondaryWidth"); const detailed3DSecondaryOffsetXSlider = document.getElementById("detailed3DSecondaryOffsetX"); const detailed3DSecondaryOffsetYSlider = document.getElementById("detailed3DSecondaryOffsetY");
        const vFontSize = document.getElementById("vFontSize"); const vRotation = document.getElementById("vRotation"); const vSkew = document.getElementById("vSkew"); const vSkewY = document.getElementById("vSkewY"); const vCurve = document.getElementById("vCurve"); const vOffset = document.getElementById("vOffset"); const vHeight = document.getElementById("vHeight"); const vBottom = document.getElementById("vBottom"); const vShiftCenter = document.getElementById("vShiftCenter"); const vDiameter = document.getElementById("vDiameter"); const vKerning = document.getElementById("vKerning"); const vCurveAmount = document.getElementById("vCurveAmount"); const vCurveKerning = document.getElementById("vCurveKerning"); const vHWeight = document.getElementById("vHWeight"); const vHDistance = document.getElementById("vHDistance"); const vCcDistance = document.getElementById("vCcDistance"); const vOWeight = document.getElementById("vOWeight"); const vODistance = document.getElementById("vODistance"); const vFlcDistance = document.getElementById("vFlcDistance"); const vFlcMaxWeight = document.getElementById("vFlcMaxWeight"); const vFlcSpacing = document.getElementById("vFlcSpacing"); const vStrokeWidth = document.getElementById("vStrokeWidth"); const vShadowOffsetX = document.getElementById("vShadowOffsetX"); const vShadowOffsetY = document.getElementById("vShadowOffsetY"); const vShadowBlur = document.getElementById("vShadowBlur"); const vBlockShadowOpacity = document.getElementById("vBlockShadowOpacity"); const vBlockShadowOffset = document.getElementById("vBlockShadowOffset"); const vBlockShadowAngle = document.getElementById("vBlockShadowAngle"); const vBlockShadowBlur = document.getElementById("vBlockShadowBlur"); const vLineShadowDistance = document.getElementById("vLineShadowDistance"); const vLineShadowAngle = document.getElementById("vLineShadowAngle"); const vLineShadowThickness = document.getElementById("vLineShadowThickness"); const vDetailed3DPrimaryOpacity = document.getElementById("vDetailed3DPrimaryOpacity"); const vDetailed3DOffset = document.getElementById("vDetailed3DOffset"); const vDetailed3DAngle = document.getElementById("vDetailed3DAngle"); const vDetailed3DBlur = document.getElementById("vDetailed3DBlur"); const vDetailed3DSecondaryOpacity = document.getElementById("vDetailed3DSecondaryOpacity"); const vDetailed3DSecondaryWidth = document.getElementById("vDetailed3DSecondaryWidth"); const vDetailed3DSecondaryOffsetX = document.getElementById("vDetailed3DSecondaryOffsetX"); const vDetailed3DSecondaryOffsetY = document.getElementById("vDetailed3DSecondaryOffsetY");

        // --- State & Constants ---
        let textObjects = [];
        let selectedObjectIndex = -1;
        let nextId = 0;
        let isDragging = false;
        let dragStartX, dragStartY;
        let dragInitialObjectX, dragInitialObjectY;
        const selectionBoxPadding = 4;
        const letterSourcePadding = 10; // Padding for capturing letter effects

        // --- Text Object Factory ---
        function createTextObject(options = {}) { const defaults = { id: nextId++, text: "TEXT", x: w / 2, y: h / 2, color: "#FF0000", fontFamily: "Arial", fontSize: 120, bold: true, italic: false, rotation: 0, isSelected: false, effectMode: 'normal', decorationMode: 'noDecoration', strokeMode: 'noStroke', shadowMode: 'noShadow', skewX: 0, skewY: 0, warpCurve: 158, warpOffset: 10, warpHeight: 100, warpBottom: 150, warpTriangle: false, warpShiftCenter: 100, circleDiameter: 300, circleKerning: 0, circleFlip: false, curveAmount: 80, curveKerning: 0, curveFlip: false, hLineWeight: 3, hLineDist: 7, hLineColor: "#0000FF", ccDist: 50, ccColor: "#00FF00", ccFillDir: "top", oLineWeight: 4, oLineDist: 3, oLineColor: "#0000FF", flcDist: 62, flcDir: 'top', flcColor: '#cccccc', flcWeight: 3, flcSpacing: 10, strokeWidth: 1, strokeColor: '#000000', shadowColor: '#000000', shadowOffsetX: 5, shadowOffsetY: 5, shadowBlur: 10, blockShadowColor: '#000000', blockShadowOpacity: 100, blockShadowOffset: 40, blockShadowAngle: -58, blockShadowBlur: 5, lineShadowColor: '#AAAAAA', lineShadowDist: 15, lineShadowAngle: -45, lineShadowThickness: 5, d3dPrimaryColor: '#000000', d3dPrimaryOpacity: 100, d3dOffset: 36, d3dAngle: -63, d3dBlur: 5, d3dSecondaryColor: '#00FF00', d3dSecondaryOpacity: 100, d3dSecondaryWidth: 0, d3dSecondaryOffsetX: -5, d3dSecondaryOffsetY: -5 }; return { ...defaults, ...options }; }

        // --- Helpers ---
        function hexToRgba(hex, alpha = 1) { let r = 0, g = 0, b = 0; if (hex.length === 4) { r = parseInt(hex[1] + hex[1], 16); g = parseInt(hex[2] + hex[2], 16); b = parseInt(hex[3] + hex[3], 16); } else if (hex.length === 7) { r = parseInt(hex[1] + hex[2], 16); g = parseInt(hex[3] + hex[4], 16); b = parseInt(hex[5] + hex[6], 16); } if (isNaN(r) || isNaN(g) || isNaN(b)) { console.warn(`Invalid hex: ${hex}`); return 'rgba(0,0,0,0)'; } return `rgba(${r},${g},${b},${alpha})`; }
        function calculateOffset(distance, angleDegrees) { const angleRadians = angleDegrees * (Math.PI / 180); return { x: distance * Math.cos(angleRadians), y: distance * Math.sin(angleRadians) }; }
        function getCanvasCoordinates(event) { const rect = canvas.getBoundingClientRect(); let clientX, clientY; if (event.touches && event.touches.length > 0) { clientX = event.touches[0].clientX; clientY = event.touches[0].clientY; } else { clientX = event.clientX; clientY = event.clientY; } return { x: clientX - rect.left, y: clientY - rect.top }; }

        // --- Font and Bounds ---
        function setTextContextOn(targetCtx, textObj) { const fontStyle = textObj.italic ? "italic" : "normal"; const fontWeight = textObj.bold ? "bold" : "normal"; targetCtx.font = `${fontStyle} ${fontWeight} ${textObj.fontSize}px "${textObj.fontFamily}"`; targetCtx.textAlign = "center"; targetCtx.textBaseline = "alphabetic"; }
        function calculateObjectBounds(textObj) { if (!textObj || !textObj.text) return { x: 0, y: 0, width: 0, height: 0, cx: 0, cy: 0, ascent: 0, descent: 0, baselineY: 0 }; ctx.save(); setTextContextOn(ctx, textObj); const metrics = ctx.measureText(textObj.text.toUpperCase()); const ascent = metrics.actualBoundingBoxAscent || textObj.fontSize * 0.8; const descent = metrics.actualBoundingBoxDescent || textObj.fontSize * 0.2; const width = metrics.width; const height = ascent + descent; ctx.restore(); const middleY = textObj.y; const baselineY = middleY + height / 2 - descent; const bounds = { x: textObj.x - width / 2, y: middleY - height / 2, width: width, height: height, cx: textObj.x, cy: middleY, ascent: ascent, descent: descent, baselineY: baselineY }; return bounds; }
        function getRotatedBoundingBox(bounds, angleDeg) { const cx = bounds.cx; const cy = bounds.cy; const w = bounds.width; const h = bounds.height; const x = bounds.x; const y = bounds.y; if (w === 0 || h === 0) return { x: cx, y: cy, width: 0, height: 0 }; const angleRad = angleDeg * Math.PI / 180; const cos = Math.cos(angleRad); const sin = Math.sin(angleRad); const corners = [ { x: x, y: y }, { x: x + w, y: y }, { x: x + w, y: y + h }, { x: x, y: y + h } ]; let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity; corners.forEach(corner => { const translatedX = corner.x - cx; const translatedY = corner.y - cy; const rotatedX = translatedX * cos - translatedY * sin; const rotatedY = translatedX * sin + translatedY * cos; const finalX = rotatedX + cx; const finalY = rotatedY + cy; minX = Math.min(minX, finalX); minY = Math.min(minY, finalY); maxX = Math.max(maxX, finalX); maxY = Math.max(maxY, finalY); }); return { x: minX, y: minY, width: maxX - minX, height: maxY - minY }; }

        // --- UI Binding ---
        function updateUIFromSelectedObject() { const selectedObject = selectedObjectIndex !== -1 ? textObjects[selectedObjectIndex] : null; if (selectedObject) { iText.value = selectedObject.text; iTextColor.value = selectedObject.color; iFontFamily.value = selectedObject.fontFamily; iBold.checked = selectedObject.bold; iItalic.checked = selectedObject.italic; iFontSize.value = selectedObject.fontSize; vFontSize.textContent = selectedObject.fontSize; iRotation.value = selectedObject.rotation; vRotation.textContent = selectedObject.rotation + '°'; effectModeSelect.value = selectedObject.effectMode; linesDecorationSelect.value = selectedObject.decorationMode; strokeToggle.value = selectedObject.strokeMode; shadowSelect.value = selectedObject.shadowMode; skewSlider.value = selectedObject.skewX; vSkew.textContent = selectedObject.skewX; skewYSlider.value = selectedObject.skewY; vSkewY.textContent = selectedObject.skewY; iCurve.value = selectedObject.warpCurve; vCurve.textContent = selectedObject.warpCurve; iOffset.value = selectedObject.warpOffset; vOffset.textContent = selectedObject.warpOffset; iHeight.value = selectedObject.warpHeight; vHeight.textContent = selectedObject.warpHeight; iBottom.value = selectedObject.warpBottom; vBottom.textContent = selectedObject.warpBottom; iTriangle.checked = selectedObject.warpTriangle; iShiftCenter.value = selectedObject.warpShiftCenter; vShiftCenter.textContent = selectedObject.warpShiftCenter; iDiameter.value = selectedObject.circleDiameter; vDiameter.textContent = selectedObject.circleDiameter; iKerning.value = selectedObject.circleKerning; vKerning.textContent = selectedObject.circleKerning; iFlip.checked = selectedObject.circleFlip; iCurveAmount.value = selectedObject.curveAmount; vCurveAmount.textContent = selectedObject.curveAmount; iCurveKerning.value = selectedObject.curveKerning; vCurveKerning.textContent = selectedObject.curveKerning; iCurveFlip.checked = selectedObject.curveFlip; hWeight.value = selectedObject.hLineWeight; vHWeight.textContent = selectedObject.hLineWeight; hDistance.value = selectedObject.hLineDist; vHDistance.textContent = selectedObject.hLineDist; hColor.value = selectedObject.hLineColor; ccDistance.value = selectedObject.ccDist; vCcDistance.textContent = selectedObject.ccDist + '%'; ccColor.value = selectedObject.ccColor; selectedObject.ccFillDir === 'top' ? ccFillTop.checked = true : ccFillBottom.checked = true; oWeight.value = selectedObject.oLineWeight; vOWeight.textContent = selectedObject.oLineWeight; oDistance.value = selectedObject.oLineDist; vODistance.textContent = selectedObject.oLineDist; oColor.value = selectedObject.oLineColor; flcDistance.value = selectedObject.flcDist; vFlcDistance.textContent = selectedObject.flcDist + '%'; flcColor.value = selectedObject.flcColor; flcMaxWeight.value = selectedObject.flcWeight; vFlcMaxWeight.textContent = selectedObject.flcWeight; flcSpacing.value = selectedObject.flcSpacing; vFlcSpacing.textContent = selectedObject.flcSpacing; selectedObject.flcDir === 'top' ? flcFillTop.checked = true : flcFillBottom.checked = true; strokeWidthSlider.value = selectedObject.strokeWidth; vStrokeWidth.textContent = selectedObject.strokeWidth; strokeColorPicker.value = selectedObject.strokeColor; shadowColorPicker.value = selectedObject.shadowColor; shadowOffsetXSlider.value = selectedObject.shadowOffsetX; vShadowOffsetX.textContent = selectedObject.shadowOffsetX; shadowOffsetYSlider.value = selectedObject.shadowOffsetY; vShadowOffsetY.textContent = selectedObject.shadowOffsetY; shadowBlurSlider.value = selectedObject.shadowBlur; vShadowBlur.textContent = selectedObject.shadowBlur; blockShadowColorPicker.value = selectedObject.blockShadowColor; blockShadowOpacitySlider.value = selectedObject.blockShadowOpacity; vBlockShadowOpacity.textContent = selectedObject.blockShadowOpacity + '%'; blockShadowOffsetSlider.value = selectedObject.blockShadowOffset; vBlockShadowOffset.textContent = selectedObject.blockShadowOffset; blockShadowAngleSlider.value = selectedObject.blockShadowAngle; vBlockShadowAngle.textContent = selectedObject.blockShadowAngle + '°'; blockShadowBlurSlider.value = selectedObject.blockShadowBlur; vBlockShadowBlur.textContent = selectedObject.blockShadowBlur; lineShadowColorPicker.value = selectedObject.lineShadowColor; lineShadowDistanceSlider.value = selectedObject.lineShadowDist; vLineShadowDistance.textContent = selectedObject.lineShadowDist; lineShadowAngleSlider.value = selectedObject.lineShadowAngle; vLineShadowAngle.textContent = selectedObject.lineShadowAngle + '°'; lineShadowThicknessSlider.value = selectedObject.lineShadowThickness; vLineShadowThickness.textContent = selectedObject.lineShadowThickness; detailed3DPrimaryColorPicker.value = selectedObject.d3dPrimaryColor; detailed3DPrimaryOpacitySlider.value = selectedObject.d3dPrimaryOpacity; vDetailed3DPrimaryOpacity.textContent = selectedObject.d3dPrimaryOpacity + '%'; detailed3DOffsetSlider.value = selectedObject.d3dOffset; vDetailed3DOffset.textContent = selectedObject.d3dOffset; detailed3DAngleSlider.value = selectedObject.d3dAngle; vDetailed3DAngle.textContent = selectedObject.d3dAngle + '°'; detailed3DBlurSlider.value = selectedObject.d3dBlur; vDetailed3DBlur.textContent = selectedObject.d3dBlur; detailed3DSecondaryColorPicker.value = selectedObject.d3dSecondaryColor; detailed3DSecondaryOpacitySlider.value = selectedObject.d3dSecondaryOpacity; vDetailed3DSecondaryOpacity.textContent = selectedObject.d3dSecondaryOpacity + '%'; detailed3DSecondaryWidthSlider.value = selectedObject.d3dSecondaryWidth; vDetailed3DSecondaryWidth.textContent = selectedObject.d3dSecondaryWidth; detailed3DSecondaryOffsetXSlider.value = selectedObject.d3dSecondaryOffsetX; vDetailed3DSecondaryOffsetX.textContent = selectedObject.d3dSecondaryOffsetX; detailed3DSecondaryOffsetYSlider.value = selectedObject.d3dSecondaryOffsetY; vDetailed3DSecondaryOffsetY.textContent = selectedObject.d3dSecondaryOffsetY; addEditBtn.textContent = 'Edit'; deleteTextBtn.disabled = false; iText.disabled = false; updateBodyClass(selectedObject); } else { iText.value = ''; vFontSize.textContent = iFontSize.value; vRotation.textContent = iRotation.value + '°'; vSkew.textContent = skewSlider.value; vSkewY.textContent = skewYSlider.value; /* ... reset other V spans ... */ addEditBtn.textContent = 'Add'; deleteTextBtn.disabled = true; iText.disabled = false; document.body.className = 'normal'; } }
        function updateSelectedObjectFromUI(property, value) { if (selectedObjectIndex === -1) return; const selectedObject = textObjects[selectedObjectIndex]; if (selectedObject.hasOwnProperty(property)) { selectedObject[property] = value; switch (property) { case 'fontSize': vFontSize.textContent = value; break; case 'rotation': vRotation.textContent = value + '°'; break; case 'skewX': vSkew.textContent = value; break; case 'skewY': vSkewY.textContent = value; break; case 'warpCurve': vCurve.textContent = value; break; case 'warpOffset': vOffset.textContent = value; break; case 'warpHeight': vHeight.textContent = value; break; case 'warpBottom': vBottom.textContent = value; break; case 'warpShiftCenter': vShiftCenter.textContent = value; break; case 'circleDiameter': vDiameter.textContent = value; break; case 'circleKerning': vKerning.textContent = value; break; case 'curveAmount': vCurveAmount.textContent = value; break; case 'curveKerning': vCurveKerning.textContent = value; break; case 'hLineWeight': vHWeight.textContent = value; break; case 'hLineDist': vHDistance.textContent = value; break; case 'ccDist': vCcDistance.textContent = value + '%'; break; case 'oLineWeight': vOWeight.textContent = value; break; case 'oLineDist': vODistance.textContent = value; break; case 'flcDist': vFlcDistance.textContent = value + '%'; break; case 'flcWeight': vFlcMaxWeight.textContent = value; break; case 'flcSpacing': vFlcSpacing.textContent = value; break; case 'strokeWidth': vStrokeWidth.textContent = value; break; case 'shadowOffsetX': vShadowOffsetX.textContent = value; break; case 'shadowOffsetY': vShadowOffsetY.textContent = value; break; case 'shadowBlur': vShadowBlur.textContent = value; break; case 'blockShadowOpacity': vBlockShadowOpacity.textContent = value + '%'; break; case 'blockShadowOffset': vBlockShadowOffset.textContent = value; break; case 'blockShadowAngle': vBlockShadowAngle.textContent = value + '°'; break; case 'blockShadowBlur': vBlockShadowBlur.textContent = value; break; case 'lineShadowDist': vLineShadowDistance.textContent = value; break; case 'lineShadowAngle': vLineShadowAngle.textContent = value + '°'; break; case 'lineShadowThickness': vLineShadowThickness.textContent = value; break; case 'd3dPrimaryOpacity': vDetailed3DPrimaryOpacity.textContent = value + '%'; break; case 'd3dOffset': vDetailed3DOffset.textContent = value; break; case 'd3dAngle': vDetailed3DAngle.textContent = value + '°'; break; case 'd3dBlur': vDetailed3DBlur.textContent = value; break; case 'd3dSecondaryOpacity': vDetailed3DSecondaryOpacity.textContent = value + '%'; break; case 'd3dSecondaryWidth': vDetailed3DSecondaryWidth.textContent = value; break; case 'd3dSecondaryOffsetX': vDetailed3DSecondaryOffsetX.textContent = value; break; case 'd3dSecondaryOffsetY': vDetailed3DSecondaryOffsetY.textContent = value; break; } if (property === 'effectMode' || property === 'decorationMode' || property === 'strokeMode' || property === 'shadowMode' || property === 'warpTriangle') { updateBodyClass(selectedObject); } update(); } else { console.warn(`Property "${property}" not found on selected object.`); } }
        function updateBodyClass(textObj) { const effectMode = textObj.effectMode; const decorationMode = textObj.decorationMode; const shadowMode = textObj.shadowMode; const strokeMode = textObj.strokeMode; const isTriangleWarp = textObj.warpTriangle; let bodyClass = effectMode; if (decorationMode === 'horizontalLines') bodyClass += ' horizontalLines'; else if (decorationMode === 'colorCut') bodyClass += ' colorCut'; else if (decorationMode === 'obliqueLines') bodyClass += ' obliqueLines'; else if (decorationMode === 'fadingLinesCut') bodyClass += ' fadingLinesCut'; if (strokeMode === 'stroke') bodyClass += ' stroke-enabled'; if (shadowMode === 'shadow') bodyClass += ' shadow'; else if (shadowMode === 'blockShadow') bodyClass += ' block-shadow'; else if (shadowMode === 'lineShadow') bodyClass += ' line-shadow'; else if (shadowMode === 'detailed3D') bodyClass += ' detailed-3d'; if (effectMode === 'warp') { bodyClass += ' horizontal-skew'; if(isTriangleWarp) { bodyClass += ' triangle-warp-enabled'; } } if (effectMode === 'skew') { bodyClass += ' horizontal-skew vertical-skew'; } document.body.className = bodyClass.trim(); }

        // --- Shadow/Decoration Helpers ---
        function applyBlockShadow(targetCtx, textObj, x, y) { const color = textObj.blockShadowColor; const opacity = textObj.blockShadowOpacity / 100; const offset = textObj.blockShadowOffset; const angleDeg = textObj.blockShadowAngle; const blur = textObj.blockShadowBlur; const offsetCoords = calculateOffset(offset, angleDeg); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.fillStyle = hexToRgba(color, opacity); if (blur > 0) { targetCtx.shadowColor = hexToRgba(color, opacity * 0.8); targetCtx.shadowBlur = blur; targetCtx.shadowOffsetX = 0; targetCtx.shadowOffsetY = 0; } const steps = Math.max(10, Math.floor(offset / 1.5)); for (let i = steps; i >= 1; i--) { const progress = i / steps; const currentX = x + offsetCoords.x * progress; const currentY = y + offsetCoords.y * progress; if (blur > 5 && i < steps) { targetCtx.shadowColor = 'transparent'; } targetCtx.fillText((textObj.text || '').toUpperCase(), currentX, currentY); if (blur > 5 && i < steps) { targetCtx.shadowColor = hexToRgba(color, opacity * 0.8); } } targetCtx.restore(); }
        function applyLineShadow(targetCtx, textObj, x, y) { const color = textObj.lineShadowColor; const distance = textObj.lineShadowDist; const angleDeg = textObj.lineShadowAngle; const thickness = Math.max(1, textObj.lineShadowThickness); const fullOffset = calculateOffset(distance, angleDeg); const cutterDistance = Math.max(0, distance - thickness); const cutterOffset = calculateOffset(cutterDistance, angleDeg); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.fillStyle = color; targetCtx.fillText((textObj.text || '').toUpperCase(), x + fullOffset.x, y + fullOffset.y); targetCtx.globalCompositeOperation = 'destination-out'; targetCtx.fillStyle = 'black'; targetCtx.fillText((textObj.text || '').toUpperCase(), x + cutterOffset.x, y + cutterOffset.y); targetCtx.restore(); }
        function applyDetailed3D_ExtrusionOnly(targetCtx, textObj, x, y) { const primaryColorRgba = hexToRgba(textObj.d3dPrimaryColor, textObj.d3dPrimaryOpacity / 100); const offset = textObj.d3dOffset; const angle = textObj.d3dAngle; const blur = textObj.d3dBlur; targetCtx.save(); setTextContextOn(targetCtx, textObj); const totalOffset = calculateOffset(offset, angle); const steps = Math.max(30, Math.floor(offset)); for (let i = steps; i >= 1; i--) { const progress = i / steps; const currentOffset = { x: totalOffset.x * progress, y: totalOffset.y * progress }; targetCtx.fillStyle = primaryColorRgba; targetCtx.fillText((textObj.text || '').toUpperCase(), x + currentOffset.x, y + currentOffset.y); } if (blur > 0) { targetCtx.save(); targetCtx.shadowColor = primaryColorRgba; targetCtx.shadowBlur = blur; targetCtx.shadowOffsetX = 0; targetCtx.shadowOffsetY = 0; targetCtx.fillStyle = primaryColorRgba; targetCtx.fillText((textObj.text || '').toUpperCase(), x + totalOffset.x, y + totalOffset.y); targetCtx.restore(); } targetCtx.restore(); }
        function applyDetailed3D_FrontOutline(targetCtx, textObj, x, y) { if (textObj.d3dSecondaryWidth <= 0) return; const secondaryColorRgba = hexToRgba(textObj.d3dSecondaryColor, textObj.d3dSecondaryOpacity / 100); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.lineWidth = textObj.d3dSecondaryWidth; targetCtx.strokeStyle = secondaryColorRgba; targetCtx.lineJoin = 'round'; targetCtx.strokeText((textObj.text || '').toUpperCase(), x + textObj.d3dSecondaryOffsetX, y + textObj.d3dSecondaryOffsetY); targetCtx.restore(); }

        // --- Master Styling Functions ---
        function renderStyledObjectToOffscreen(obj, targetCtx, targetCanvasWidth, targetCanvasHeight) { targetCtx.clearRect(0, 0, targetCanvasWidth, targetCanvasHeight); targetCtx.save(); const centerX = targetCanvasWidth / 2; const centerY = targetCanvasHeight / 2; setTextContextOn(targetCtx, obj); const text = obj.text.toUpperCase(); const metrics = targetCtx.measureText(text); const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8; const descent = metrics.actualBoundingBoxDescent || obj.fontSize * 0.2; const textHeight = ascent + descent; const textWidth = metrics.width; const drawYBaseline = centerY + textHeight / 2 - descent; let mainFillStyle = obj.color; if (obj.decorationMode === "fadingLinesCut") { const distancePercent = obj.flcDist / 100; const direction = obj.flcDir; const lineColor = obj.flcColor; const lineWeight = obj.flcWeight; const lineSpacing = obj.flcSpacing; const baseTextColor = obj.color; const patternCanvas = document.createElement("canvas"); const patternWidth = 10; const patternHeight = Math.max(20, Math.ceil(textHeight) * 2); patternCanvas.width = patternWidth; patternCanvas.height = patternHeight; const pCtx = patternCanvas.getContext("2d"); const cutLineY = patternHeight * distancePercent; pCtx.fillStyle = baseTextColor; pCtx.fillRect(0, 0, patternWidth, patternHeight); pCtx.fillStyle = lineColor; for (let y = 0; y < patternHeight; y += lineSpacing) { const lineCenterY = y + lineSpacing / 2; let drawLine = false; if (direction === 'top') { if (lineCenterY > cutLineY) drawLine = true; } else { if (lineCenterY < cutLineY) drawLine = true; } if (drawLine) { const lineDrawY = lineCenterY - lineWeight / 2; pCtx.fillRect(0, lineDrawY, patternWidth, lineWeight); } } pCtx.fillStyle = baseTextColor; if (direction === 'top') { pCtx.fillRect(0, 0, patternWidth, cutLineY); } else { pCtx.fillRect(0, cutLineY, patternWidth, patternHeight - cutLineY); } try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); const topEdgeOffsetOnCanvas = drawYBaseline - ascent; patternTransform.translateSelf(0, topEdgeOffsetOnCanvas); mainFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating FLC pattern:", e); mainFillStyle = obj.color; } } else if (obj.decorationMode === "horizontalLines") { const weight = obj.hLineWeight; const distance = obj.hLineDist; const color = obj.hLineColor; const baseColor = obj.color; const totalHeight = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = 10; patternCanvas.height = totalHeight; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, 10, totalHeight); pCtx.fillStyle = color; pCtx.fillRect(0, 0, 10, weight); try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); } catch (e) { mainFillStyle = obj.color; } } else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const color = obj.ccColor; const baseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = drawYBaseline - ascent; const gradEndY = drawYBaseline + descent; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), baseColor); gradient.addColorStop(1, baseColor); } else { gradient.addColorStop(0, baseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), baseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const color = obj.oLineColor; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i + size + distance, size + distance); } pCtx.stroke(); try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); } catch (e) { mainFillStyle = obj.color; } } if (obj.shadowMode === "blockShadow") { applyBlockShadow(targetCtx, obj, centerX, drawYBaseline); } else if (obj.shadowMode === "lineShadow") { applyLineShadow(targetCtx, obj, centerX, drawYBaseline); } else if (obj.shadowMode === "detailed3D") { applyDetailed3D_ExtrusionOnly(targetCtx, obj, centerX, drawYBaseline); } targetCtx.save(); if (obj.shadowMode === "shadow") { targetCtx.shadowColor = obj.shadowColor; targetCtx.shadowOffsetX = obj.shadowOffsetX; targetCtx.shadowOffsetY = obj.shadowOffsetY; targetCtx.shadowBlur = obj.shadowBlur; } targetCtx.fillStyle = mainFillStyle; targetCtx.fillText(text, centerX, drawYBaseline); targetCtx.restore(); if (obj.strokeMode === 'stroke' && obj.strokeWidth > 0) { targetCtx.save(); setTextContextOn(targetCtx, obj); targetCtx.strokeStyle = obj.strokeColor; targetCtx.lineWidth = obj.strokeWidth; targetCtx.lineJoin = 'round'; targetCtx.strokeText(text, centerX, drawYBaseline); targetCtx.restore(); } if (obj.shadowMode === "detailed3D") { applyDetailed3D_FrontOutline(targetCtx, obj, centerX, drawYBaseline); } targetCtx.restore(); }
        function renderSingleStyledLetter(obj, letter, targetCtx, targetCanvasWidth, targetCanvasHeight) { targetCtx.clearRect(0, 0, targetCanvasWidth, targetCanvasHeight); targetCtx.save(); const centerX = targetCanvasWidth / 2; const centerY = targetCanvasHeight / 2; setTextContextOn(targetCtx, obj); const metrics = targetCtx.measureText(letter); const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8; const descent = metrics.actualBoundingBoxDescent || obj.fontSize * 0.2; const textHeight = ascent + descent; const drawYBaseline = centerY + textHeight / 2 - descent; let letterFillStyle = obj.color; if (obj.decorationMode === "fadingLinesCut") { const distancePercent = obj.flcDist / 100; const direction = obj.flcDir; const lineColor = obj.flcColor; const lineWeight = obj.flcWeight; const lineSpacing = obj.flcSpacing; const baseTextColor = obj.color; const patternCanvas = document.createElement("canvas"); const patternWidth = 10; const patternHeight = Math.max(20, Math.ceil(textHeight) * 2); patternCanvas.width = patternWidth; patternCanvas.height = patternHeight; const pCtx = patternCanvas.getContext("2d"); const cutLineY = patternHeight * distancePercent; pCtx.fillStyle = baseTextColor; pCtx.fillRect(0, 0, patternWidth, patternHeight); pCtx.fillStyle = lineColor; for (let y = 0; y < patternHeight; y += lineSpacing) { const lineCenterY = y + lineSpacing / 2; let drawLine = false; if (direction === 'top') { if (lineCenterY > cutLineY) drawLine = true; } else { if (lineCenterY < cutLineY) drawLine = true; } if (drawLine) { const lineDrawY = lineCenterY - lineWeight / 2; pCtx.fillRect(0, lineDrawY, patternWidth, lineWeight); } } pCtx.fillStyle = baseTextColor; if (direction === 'top') { pCtx.fillRect(0, 0, patternWidth, cutLineY); } else { pCtx.fillRect(0, cutLineY, patternWidth, patternHeight - cutLineY); } try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); const topEdgeOffsetOnCanvas = drawYBaseline - ascent; patternTransform.translateSelf(0, topEdgeOffsetOnCanvas); letterFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating FLC pattern for letter:", e); letterFillStyle = obj.color; } } else if (obj.decorationMode === "horizontalLines") { const weight = obj.hLineWeight; const distance = obj.hLineDist; const color = obj.hLineColor; const baseColor = obj.color; const totalHeight = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = 10; patternCanvas.height = totalHeight; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, 10, totalHeight); pCtx.fillStyle = color; pCtx.fillRect(0, 0, 10, weight); try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); } catch (e) { letterFillStyle = obj.color; } } else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const color = obj.ccColor; const baseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = drawYBaseline - ascent; const gradEndY = drawYBaseline + descent; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), baseColor); gradient.addColorStop(1, baseColor); } else { gradient.addColorStop(0, baseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), baseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const color = obj.oLineColor; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i + size + distance, size + distance); } pCtx.stroke(); try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); } catch (e) { letterFillStyle = obj.color; } } const letterObj = { ...obj, text: letter }; if (obj.shadowMode === "blockShadow") applyBlockShadow(targetCtx, letterObj, centerX, drawYBaseline); if (obj.shadowMode === "lineShadow") applyLineShadow(targetCtx, letterObj, centerX, drawYBaseline); if (obj.shadowMode === "detailed3D") applyDetailed3D_ExtrusionOnly(targetCtx, letterObj, centerX, drawYBaseline); targetCtx.save(); if (obj.shadowMode === "shadow") { targetCtx.shadowColor = obj.shadowColor; targetCtx.shadowOffsetX = obj.shadowOffsetX; targetCtx.shadowOffsetY = obj.shadowOffsetY; targetCtx.shadowBlur = obj.shadowBlur; } targetCtx.fillStyle = letterFillStyle; targetCtx.fillText(letter, centerX, drawYBaseline); targetCtx.restore(); if (obj.strokeMode === 'stroke' && obj.strokeWidth > 0) { targetCtx.save(); targetCtx.strokeStyle = obj.strokeColor; targetCtx.lineWidth = obj.strokeWidth; targetCtx.lineJoin = 'round'; targetCtx.strokeText(letter, centerX, drawYBaseline); targetCtx.restore(); } if (obj.shadowMode === "detailed3D") applyDetailed3D_FrontOutline(targetCtx, letterObj, centerX, drawYBaseline); targetCtx.restore(); return { width: metrics.width, height: textHeight, ascent: ascent, descent: descent, baselineY: drawYBaseline, centerX: centerX, centerY: centerY, drawY: centerY - ascent }; }

        // --- Effect Rendering Logic ---
        function drawNormalOrSkewObject(obj) { renderStyledObjectToOffscreen(obj, octx, os.width, os.height); ctx.drawImage(os, -os.width / 2, -os.height / 2); }
        function drawWarpedObject(obj) { renderStyledObjectToOffscreen(obj, octx, os.width, os.height); tempWarpCtx.clearRect(0, 0, w, h); tempWarpCtx.save(); setTextContextOn(octx, obj); const metrics = octx.measureText(obj.text.toUpperCase()); const textAscent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8; const textDescent = metrics.actualBoundingBoxDescent || obj.fontSize * 0.2; const textHeight = textAscent + textDescent; const drawYBaselineOffscreen = os.height / 2 + textHeight / 2 - textDescent; const curve = obj.warpCurve; const sourceOffsetY = obj.warpOffset; const sourceSampleHeight = obj.warpHeight; const bottom = obj.warpBottom; const isTri = obj.warpTriangle; const shiftCenterValue = obj.warpShiftCenter; const angleSteps = Math.PI / w; const peak = w * (shiftCenterValue / 200.0); for (let i = 0; i < w; i++) { let destHeight; if (isTri) { const distFromPeak = Math.abs(i - peak); const maxDist = Math.max(peak, w - peak); const factor = (maxDist > 0) ? (distFromPeak / maxDist) : 0; destHeight = bottom - (curve * factor); } else { destHeight = bottom - curve * Math.sin(i * angleSteps); } destHeight = Math.max(1, destHeight); const destY = h * 0.5 - destHeight / 2; try { const sy = drawYBaselineOffscreen - textAscent + sourceOffsetY; tempWarpCtx.drawImage(os, i, sy, 1, sourceSampleHeight, i, destY, 1, destHeight); } catch (e) { /* ignore */ } } tempWarpCtx.restore(); ctx.drawImage(tempWarpCanvas, -w / 2, -h / 2); }
        function drawCircularObject(obj) { const diameter = obj.circleDiameter; const kerning = obj.circleKerning; const flipped = obj.circleFlip; const text = obj.text.toUpperCase(); const radius = diameter / 2; setTextContextOn(ctx, obj); ctx.textAlign = 'center'; ctx.textBaseline = 'alphabetic'; const contentArr = text.split(''); const letterAngles = []; let totalAngle = 0; contentArr.forEach((letter) => { const letterWidth = ctx.measureText(letter).width + kerning; const letterAngle = radius > 0 ? (letterWidth / radius) * (180 / Math.PI) : 0; letterAngles.push(letterAngle); totalAngle += letterAngle; }); let currentAngleRad = (-totalAngle / 2) * Math.PI / 180; for (let i = 0; i < contentArr.length; i++) { const letter = contentArr[i]; const letterAngleDeg = letterAngles[i]; const letterAngleRad = letterAngleDeg * Math.PI / 180; const halfAngleRad = letterAngleRad / 2; currentAngleRad += halfAngleRad; const letterInfo = renderSingleStyledLetter(obj, letter, letterCtx, letterCanvas.width, letterCanvas.height); const angleToDraw = flipped ? currentAngleRad + Math.PI : currentAngleRad; const x = radius * Math.cos(angleToDraw); const y = radius * Math.sin(angleToDraw); ctx.save(); ctx.translate(x, y); let rot = angleToDraw + Math.PI / 2; if (flipped) { rot += Math.PI; } ctx.rotate(rot); try { const sourceW = letterInfo.width + letterSourcePadding * 2; const sourceH = letterInfo.height + letterSourcePadding * 2; const sourceX = letterInfo.centerX - sourceW / 2; const sourceY = letterInfo.centerY - sourceH / 2; const destX = -sourceW / 2; const destY = -letterInfo.ascent - letterSourcePadding; ctx.drawImage(letterCanvas, sourceX, sourceY, sourceW, sourceH, destX, destY, sourceW, sourceH); } catch(e) { console.error("DrawImage error in circle:", e); } ctx.restore(); currentAngleRad += halfAngleRad; } }
        function drawCurvedObject(obj) { const curveAmount = obj.curveAmount; const kerning = obj.curveKerning; const flip = obj.curveFlip; const text = obj.text.toUpperCase(); const fontSize = obj.fontSize; setTextContextOn(ctx, obj); ctx.textAlign = 'center'; ctx.textBaseline = 'alphabetic'; const direction = flip ? -1 : 1; const curveRadiusBase = Math.max(1, curveAmount / 50); const curveRadius = (w * 3) / curveRadiusBase; const curveStrength = Math.max(0.01, curveAmount / 100); const chars = text.split(''); let totalWidth = 0; const charWidths = chars.map(char => { const width = ctx.measureText(char).width + kerning; totalWidth += width; return width; }); let currentX = -totalWidth / 2; const baselineY = 0; for (let i = 0; i < chars.length; i++) { const letter = chars[i]; const displayWidth = charWidths[i]; const letterInfo = renderSingleStyledLetter(obj, letter, letterCtx, letterCanvas.width, letterCanvas.height); const charCenterX = currentX + displayWidth / 2; const percentAcross = totalWidth > 0 ? charCenterX / (totalWidth / 2) : 0; const angleOffset = percentAcross * Math.PI * curveStrength * 0.5; const yOffset = -direction * curveRadius * (1 - Math.cos(angleOffset)); const xPos = charCenterX; const rot = direction * angleOffset; ctx.save(); ctx.translate(xPos, baselineY + yOffset); ctx.rotate(rot); try { const sourceW = letterInfo.width + letterSourcePadding * 2; const sourceH = letterInfo.height + letterSourcePadding * 2; const sourceX = letterInfo.centerX - sourceW / 2; const sourceY = letterInfo.centerY - sourceH / 2; const destX = -sourceW / 2; const destY = -letterInfo.ascent - letterSourcePadding; ctx.drawImage(letterCanvas, sourceX, sourceY, sourceW, sourceH, destX, destY, sourceW, sourceH); } catch(e) { console.error("DrawImage error in curve:", e); } ctx.restore(); currentX += displayWidth; } }

        // --- Main Drawing Logic Per Object ---
        function drawTextObject(obj) { if (!obj || !obj.text) return; ctx.save(); ctx.translate(obj.x, obj.y); ctx.rotate(obj.rotation * Math.PI / 180); if (obj.effectMode === 'skew') { const skewXRad = obj.skewX / 100; const skewYRad = obj.skewY / 100; ctx.transform(1, skewYRad, skewXRad, 1, 0, 0); } switch (obj.effectMode) { case 'normal': case 'skew': drawNormalOrSkewObject(obj); break; case 'warp': drawWarpedObject(obj); break; case 'circle': drawCircularObject(obj); break; case 'curve': drawCurvedObject(obj); break; default: setTextContextOn(ctx, obj); ctx.fillStyle = obj.color; ctx.fillText(obj.text.toUpperCase() + ' (Unknown Effect)', 0, 0); } ctx.restore(); }
        function drawSelectionBox(obj) { if (!obj || !obj.isSelected) return; const bounds = calculateObjectBounds(obj); if (bounds.width === 0) return; const rotatedBounds = getRotatedBoundingBox(bounds, obj.rotation); const paddedX = rotatedBounds.x - selectionBoxPadding; const paddedY = rotatedBounds.y - selectionBoxPadding; const paddedWidth = rotatedBounds.width + selectionBoxPadding * 2; const paddedHeight = rotatedBounds.height + selectionBoxPadding * 2; ctx.save(); ctx.strokeStyle = 'rgba(0, 100, 255, 0.8)'; ctx.lineWidth = 1; ctx.setLineDash([4, 4]); ctx.strokeRect(paddedX, paddedY, paddedWidth, paddedHeight); ctx.restore(); }

        // --- Main Update/Render Function ---
        function update() { ctx.clearRect(0, 0, w, h); ctx.fillStyle = '#f9f9f9'; ctx.fillRect(0, 0, w, h); textObjects.forEach(obj => { drawTextObject(obj); }); if (selectedObjectIndex !== -1) { drawSelectionBox(textObjects[selectedObjectIndex]); } }

        // --- Event Handlers & Listeners ---
        function handleAddObject() { const text = iText.value.trim(); if (!text) { alert("Please enter text before adding."); return; } const newObjOptions = { text: text, x: w / 2 + (textObjects.length * 10), y: h / 2 + (textObjects.length * 10), color: iTextColor.value, fontFamily: iFontFamily.value, fontSize: parseInt(iFontSize.value, 10), bold: iBold.checked, italic: iItalic.checked, rotation: parseInt(iRotation.value, 10), effectMode: effectModeSelect.value, decorationMode: linesDecorationSelect.value, strokeMode: strokeToggle.value, shadowMode: shadowSelect.value, skewX: parseInt(skewSlider.value, 10), skewY: parseInt(skewYSlider.value, 10), warpCurve: parseInt(iCurve.value, 10), warpOffset: parseInt(iOffset.value, 10), warpHeight: parseInt(iHeight.value, 10), warpBottom: parseInt(iBottom.value, 10), warpTriangle: iTriangle.checked, warpShiftCenter: parseInt(iShiftCenter.value, 10), circleDiameter: parseInt(iDiameter.value, 10), circleKerning: parseInt(iKerning.value, 10), circleFlip: iFlip.checked, curveAmount: parseInt(iCurveAmount.value, 10), curveKerning: parseInt(iCurveKerning.value, 10), curveFlip: iCurveFlip.checked, hLineWeight: parseInt(hWeight.value, 10), hLineDist: parseInt(hDistance.value, 10), hLineColor: hColor.value, ccDist: parseInt(ccDistance.value, 10), ccColor: ccColor.value, ccFillDir: ccFillTop.checked ? 'top' : 'bottom', oLineWeight: parseInt(oWeight.value, 10), oLineDist: parseInt(oDistance.value, 10), oLineColor: oColor.value, flcDist: parseInt(flcDistance.value, 10), flcColor: flcColor.value, flcWeight: parseInt(flcMaxWeight.value, 10), flcSpacing: parseInt(flcSpacing.value, 10), flcDir: flcFillTop.checked ? 'top' : 'bottom', strokeWidth: parseInt(strokeWidthSlider.value, 10), strokeColor: strokeColorPicker.value, shadowColor: shadowColorPicker.value, shadowOffsetX: parseInt(shadowOffsetXSlider.value, 10), shadowOffsetY: parseInt(shadowOffsetYSlider.value, 10), shadowBlur: parseInt(shadowBlurSlider.value, 10), blockShadowColor: blockShadowColorPicker.value, blockShadowOpacity: parseInt(blockShadowOpacitySlider.value, 10), blockShadowOffset: parseInt(blockShadowOffsetSlider.value, 10), blockShadowAngle: parseInt(blockShadowAngleSlider.value, 10), blockShadowBlur: parseInt(blockShadowBlurSlider.value, 10), lineShadowColor: lineShadowColorPicker.value, lineShadowDist: parseInt(lineShadowDistanceSlider.value, 10), lineShadowAngle: parseInt(lineShadowAngleSlider.value, 10), lineShadowThickness: parseInt(lineShadowThicknessSlider.value, 10), d3dPrimaryColor: detailed3DPrimaryColorPicker.value, d3dPrimaryOpacity: parseInt(detailed3DPrimaryOpacitySlider.value, 10), d3dOffset: parseInt(detailed3DOffsetSlider.value, 10), d3dAngle: parseInt(detailed3DAngleSlider.value, 10), d3dBlur: parseInt(detailed3DBlurSlider.value, 10), d3dSecondaryColor: detailed3DSecondaryColorPicker.value, d3dSecondaryOpacity: parseInt(detailed3DSecondaryOpacitySlider.value, 10), d3dSecondaryWidth: parseInt(detailed3DSecondaryWidthSlider.value, 10), d3dSecondaryOffsetX: parseInt(detailed3DSecondaryOffsetXSlider.value, 10), d3dSecondaryOffsetY: parseInt(detailed3DSecondaryOffsetYSlider.value, 10) }; const newObj = createTextObject(newObjOptions); if (selectedObjectIndex !== -1) { textObjects[selectedObjectIndex].isSelected = false; } textObjects.push(newObj); selectedObjectIndex = textObjects.length - 1; newObj.isSelected = true; updateUIFromSelectedObject(); update(); }
        function handleDeleteObject() { if (selectedObjectIndex !== -1) { textObjects.splice(selectedObjectIndex, 1); selectedObjectIndex = -1; updateUIFromSelectedObject(); update(); } }
        function startDrag(e) { const coords = getCanvasCoordinates(e); let hitIndex = -1; for (let i = textObjects.length - 1; i >= 0; i--) { const obj = textObjects[i]; const bounds = calculateObjectBounds(obj); ctx.save(); ctx.translate(obj.x, obj.y); ctx.rotate(obj.rotation * Math.PI / 180); const localX = coords.x - obj.x; const localY = coords.y - obj.y; const angleRad = -obj.rotation * Math.PI / 180; const rotatedX = localX * Math.cos(angleRad) - localY * Math.sin(angleRad); const rotatedY = localX * Math.sin(angleRad) + localY * Math.cos(angleRad); const hit = rotatedX >= -bounds.width / 2 && rotatedX <= bounds.width / 2 && rotatedY >= -bounds.height / 2 && rotatedY <= bounds.height / 2; ctx.restore(); if (hit) { hitIndex = i; break; } } if (hitIndex !== -1) { isDragging = true; if (selectedObjectIndex !== hitIndex) { if (selectedObjectIndex !== -1) { textObjects[selectedObjectIndex].isSelected = false; } selectedObjectIndex = hitIndex; textObjects[selectedObjectIndex].isSelected = true; updateUIFromSelectedObject(); } dragStartX = coords.x; dragStartY = coords.y; dragInitialObjectX = textObjects[selectedObjectIndex].x; dragInitialObjectY = textObjects[selectedObjectIndex].y; canvas.classList.add('dragging'); if (e.type === 'touchstart') e.preventDefault(); update(); } }
        function drag(e) { if (!isDragging || selectedObjectIndex === -1) return; if (e.type === 'touchmove') e.preventDefault(); const coords = getCanvasCoordinates(e); const deltaX = coords.x - dragStartX; const deltaY = coords.y - dragStartY; textObjects[selectedObjectIndex].x = dragInitialObjectX + deltaX; textObjects[selectedObjectIndex].y = dragInitialObjectY + deltaY; update(); }
        function endDrag() { if (isDragging) { isDragging = false; canvas.classList.remove('dragging'); } }
        function handleClick(e) { if (isDragging) return; const coords = getCanvasCoordinates(e); let hitIndex = -1; for (let i = textObjects.length - 1; i >= 0; i--) { const obj = textObjects[i]; const bounds = calculateObjectBounds(obj); ctx.save(); ctx.translate(obj.x, obj.y); ctx.rotate(obj.rotation * Math.PI / 180); const localX = coords.x - obj.x; const localY = coords.y - obj.y; const angleRad = -obj.rotation * Math.PI / 180; const rotatedX = localX * Math.cos(angleRad) - localY * Math.sin(angleRad); const rotatedY = localX * Math.sin(angleRad) + localY * Math.cos(angleRad); const hit = rotatedX >= -bounds.width / 2 && rotatedX <= bounds.width / 2 && rotatedY >= -bounds.height / 2 && rotatedY <= bounds.height / 2; ctx.restore(); if (hit) { hitIndex = i; break; } } let needsUpdate = false; if (hitIndex !== -1) { if (selectedObjectIndex !== hitIndex) { if (selectedObjectIndex !== -1) { textObjects[selectedObjectIndex].isSelected = false; } selectedObjectIndex = hitIndex; textObjects[selectedObjectIndex].isSelected = true; needsUpdate = true; } } else { if (selectedObjectIndex !== -1) { textObjects[selectedObjectIndex].isSelected = false; selectedObjectIndex = -1; needsUpdate = true; } } if (needsUpdate) { updateUIFromSelectedObject(); update(); } }
        // Attach Listeners
        iText.oninput = () => { if (selectedObjectIndex !== -1) { updateSelectedObjectFromUI('text', iText.value); } }; addEditBtn.onclick = () => { if (selectedObjectIndex !== -1) { iText.focus(); } else { handleAddObject(); } }; deleteTextBtn.onclick = handleDeleteObject; iTextColor.oninput = (e) => updateSelectedObjectFromUI('color', e.target.value); iFontFamily.onchange = (e) => updateSelectedObjectFromUI('fontFamily', e.target.value); iBold.onchange = (e) => updateSelectedObjectFromUI('bold', e.target.checked); iItalic.onchange = (e) => updateSelectedObjectFromUI('italic', e.target.checked); iFontSize.oninput = (e) => updateSelectedObjectFromUI('fontSize', parseInt(e.target.value, 10)); iRotation.oninput = (e) => updateSelectedObjectFromUI('rotation', parseInt(e.target.value, 10)); effectModeSelect.onchange = (e) => updateSelectedObjectFromUI('effectMode', e.target.value); linesDecorationSelect.onchange = (e) => updateSelectedObjectFromUI('decorationMode', e.target.value); strokeToggle.onchange = (e) => updateSelectedObjectFromUI('strokeMode', e.target.value); shadowSelect.onchange = (e) => updateSelectedObjectFromUI('shadowMode', e.target.value);
        skewSlider.oninput = (e) => updateSelectedObjectFromUI('skewX', parseInt(e.target.value, 10)); skewYSlider.oninput = (e) => updateSelectedObjectFromUI('skewY', parseInt(e.target.value, 10));
        iCurve.oninput = (e) => updateSelectedObjectFromUI('warpCurve', parseInt(e.target.value, 10)); iOffset.oninput = (e) => updateSelectedObjectFromUI('warpOffset', parseInt(e.target.value, 10)); iHeight.oninput = (e) => updateSelectedObjectFromUI('warpHeight', parseInt(e.target.value, 10)); iBottom.oninput = (e) => updateSelectedObjectFromUI('warpBottom', parseInt(e.target.value, 10)); iTriangle.onchange = (e) => updateSelectedObjectFromUI('warpTriangle', e.target.checked); iShiftCenter.oninput = (e) => updateSelectedObjectFromUI('warpShiftCenter', parseInt(e.target.value, 10));
        iDiameter.oninput = (e) => updateSelectedObjectFromUI('circleDiameter', parseInt(e.target.value, 10)); iKerning.oninput = (e) => updateSelectedObjectFromUI('circleKerning', parseInt(e.target.value, 10)); iFlip.onchange = (e) => updateSelectedObjectFromUI('circleFlip', e.target.checked);
        iCurveAmount.oninput = (e) => updateSelectedObjectFromUI('curveAmount', parseInt(e.target.value, 10)); iCurveKerning.oninput = (e) => updateSelectedObjectFromUI('curveKerning', parseInt(e.target.value, 10)); iCurveFlip.onchange = (e) => updateSelectedObjectFromUI('curveFlip', e.target.checked);
        hWeight.oninput = (e) => updateSelectedObjectFromUI('hLineWeight', parseInt(e.target.value, 10)); hDistance.oninput = (e) => updateSelectedObjectFromUI('hLineDist', parseInt(e.target.value, 10)); hColor.oninput = (e) => updateSelectedObjectFromUI('hLineColor', e.target.value);
        ccDistance.oninput = (e) => updateSelectedObjectFromUI('ccDist', parseInt(e.target.value, 10)); ccColor.oninput = (e) => updateSelectedObjectFromUI('ccColor', e.target.value); ccFillTop.onchange = () => updateSelectedObjectFromUI('ccFillDir', 'top'); ccFillBottom.onchange = () => updateSelectedObjectFromUI('ccFillDir', 'bottom');
        oWeight.oninput = (e) => updateSelectedObjectFromUI('oLineWeight', parseInt(e.target.value, 10)); oDistance.oninput = (e) => updateSelectedObjectFromUI('oLineDist', parseInt(e.target.value, 10)); oColor.oninput = (e) => updateSelectedObjectFromUI('oLineColor', e.target.value);
        flcDistance.oninput = (e) => updateSelectedObjectFromUI('flcDist', parseInt(e.target.value, 10)); flcColor.oninput = (e) => updateSelectedObjectFromUI('flcColor', e.target.value); flcMaxWeight.oninput = (e) => updateSelectedObjectFromUI('flcWeight', parseInt(e.target.value, 10)); flcSpacing.oninput = (e) => updateSelectedObjectFromUI('flcSpacing', parseInt(e.target.value, 10)); flcFillTop.onchange = () => updateSelectedObjectFromUI('flcDir', 'top'); flcFillBottom.onchange = () => updateSelectedObjectFromUI('flcDir', 'bottom');
        strokeWidthSlider.oninput = (e) => updateSelectedObjectFromUI('strokeWidth', parseInt(e.target.value, 10)); strokeColorPicker.oninput = (e) => updateSelectedObjectFromUI('strokeColor', e.target.value);
        shadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('shadowColor', e.target.value); shadowOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetX', parseInt(e.target.value, 10)); shadowOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetY', parseInt(e.target.value, 10)); shadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('shadowBlur', parseInt(e.target.value, 10));
        blockShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('blockShadowColor', e.target.value); blockShadowOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowOpacity', parseInt(e.target.value, 10)); blockShadowOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowOffset', parseInt(e.target.value, 10)); blockShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowAngle', parseInt(e.target.value, 10)); blockShadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowBlur', parseInt(e.target.value, 10));
        lineShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('lineShadowColor', e.target.value); lineShadowDistanceSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowDist', parseInt(e.target.value, 10)); lineShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowAngle', parseInt(e.target.value, 10)); lineShadowThicknessSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowThickness', parseInt(e.target.value, 10));
        detailed3DPrimaryColorPicker.oninput = (e) => updateSelectedObjectFromUI('d3dPrimaryColor', e.target.value); detailed3DPrimaryOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('d3dPrimaryOpacity', parseInt(e.target.value, 10)); detailed3DOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('d3dOffset', parseInt(e.target.value, 10)); detailed3DAngleSlider.oninput = (e) => updateSelectedObjectFromUI('d3dAngle', parseInt(e.target.value, 10)); detailed3DBlurSlider.oninput = (e) => updateSelectedObjectFromUI('d3dBlur', parseInt(e.target.value, 10));
        detailed3DSecondaryColorPicker.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryColor', e.target.value); detailed3DSecondaryOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOpacity', parseInt(e.target.value, 10)); detailed3DSecondaryWidthSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryWidth', parseInt(e.target.value, 10)); detailed3DSecondaryOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOffsetX', parseInt(e.target.value, 10)); detailed3DSecondaryOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOffsetY', parseInt(e.target.value, 10));
        canvas.addEventListener('mousedown', startDrag); canvas.addEventListener('touchstart', startDrag, { passive: false }); window.addEventListener('mousemove', drag); window.addEventListener('touchmove', drag, { passive: false }); window.addEventListener('mouseup', endDrag); window.addEventListener('touchend', endDrag); canvas.addEventListener('click', handleClick);

        // --- Initial State ---
        function initialize() { const initialObject = createTextObject({ text: "TEXT", isSelected: true }); textObjects.push(initialObject); selectedObjectIndex = 0; updateUIFromSelectedObject(); update(); }
        initialize();

    </script>
</body>
</html>
