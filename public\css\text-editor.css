/* Text Editor CSS Styles */

/* Control Groups */
.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    margin-bottom: 5px;
    color: #ddd;
    font-size: 14px;
}

/* Text Input */
.text-input {
    width: 100%;
    background: #333;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 8px;
    color: #fff;
    font-size: 14px;
    resize: vertical;
}

/* Font Select */
.font-select {
    width: 100%;
    background: #333;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 8px;
    color: #fff;
    font-size: 14px;
}

/* Slider Container */
.slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.slider {
    flex: 1;
    -webkit-appearance: none;
    appearance: none;
    height: 8px;
    background: #444;
    border-radius: 4px;
    outline: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #0066ff;
    border-radius: 50%;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #0066ff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Toggle Buttons */
.toggle-buttons {
    display: flex;
    gap: 5px;
}

/* Color Inputs */
input[type="color"] {
    -webkit-appearance: none;
    appearance: none;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 0;
    border: none;
}

input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: 4px;
}

input[type="color"]::-moz-color-swatch {
    border: none;
    border-radius: 4px;
}

/* Active States */
.action-btn.active {
    background: #0066ff;
    color: white;
}

/* Text Canvas */
#text-canvas {
    z-index: 10;
}
