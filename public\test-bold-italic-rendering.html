<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bold/Italic Rendering Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .controls {
            margin: 10px 0;
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .controls select, .controls input, .controls button {
            padding: 5px;
        }
        .test-canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
            background: white;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .comparison-item h4 {
            margin-top: 0;
        }
        .font-info {
            background: #e9ecef;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 11px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔤 Bold/Italic Rendering Test</h1>
        <p>Testing if Bold and Italic checkboxes actually make text appear bold/italic in canvas</p>
        
        <div class="test-section">
            <h2>🔧 System Status</h2>
            <div id="systemStatus" class="status">Checking system...</div>
            <div id="fontLoadingStatus" class="font-info"></div>
        </div>
        
        <div class="test-section">
            <h2>🎛️ Font Test Controls</h2>
            <div class="controls">
                <label>Font: 
                    <select id="fontSelect">
                        <option value="Arial">Arial</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Impact">Impact (no Bold/Italic)</option>
                    </select>
                </label>
                <label>
                    <input type="checkbox" id="boldCheck"> Bold
                </label>
                <label>
                    <input type="checkbox" id="italicCheck"> Italic
                </label>
                <button id="testBtn">Test Rendering</button>
            </div>
            <div id="controlStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>🎨 Canvas Rendering Test</h2>
            <canvas id="testCanvas" class="test-canvas" width="600" height="200"></canvas>
            <div id="renderingStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Comparison: CSS vs Font Files</h2>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>CSS Bold/Italic (Old Method)</h4>
                    <canvas id="cssCanvas" width="300" height="100"></canvas>
                    <div class="font-info">Uses font-weight: bold; font-style: italic;</div>
                </div>
                <div class="comparison-item">
                    <h4>Font Files (New Method)</h4>
                    <canvas id="fontFileCanvas" width="300" height="100"></canvas>
                    <div class="font-info">Uses actual Bold.ttf, Italic.ttf files</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Automated Tests</h2>
            <div id="testResults" class="font-info">Running tests...</div>
        </div>
    </div>

    <script src="/js/font-variant-detector.js"></script>
    <script>
        let detector = null;
        
        async function initTest() {
            const systemStatus = document.getElementById('systemStatus');
            const fontLoadingStatus = document.getElementById('fontLoadingStatus');
            
            try {
                systemStatus.textContent = 'Initializing font variant detector...';
                systemStatus.className = 'status warning';
                
                detector = window.fontVariantDetector;
                if (!detector) {
                    throw new Error('Font variant detector not found');
                }
                
                await detector.initialize();
                
                systemStatus.textContent = '✅ Font variant detection system active!';
                systemStatus.className = 'status success';
                
                fontLoadingStatus.textContent = `Loaded ${detector.fontCache.size} font variants`;
                
                setupControls();
                runAutomatedTests();
                
            } catch (error) {
                systemStatus.textContent = `❌ Error: ${error.message}`;
                systemStatus.className = 'status error';
                console.error('Test initialization failed:', error);
            }
        }
        
        function setupControls() {
            const fontSelect = document.getElementById('fontSelect');
            const boldCheck = document.getElementById('boldCheck');
            const italicCheck = document.getElementById('italicCheck');
            const testBtn = document.getElementById('testBtn');
            
            function updateControls() {
                const fontFamily = fontSelect.value;
                
                if (detector && detector.initialized) {
                    detector.updateVariantControls(fontFamily);
                    
                    const boldAvailable = detector.isVariantAvailable(fontFamily, 'bold');
                    const italicAvailable = detector.isVariantAvailable(fontFamily, 'italic');
                    
                    const controlStatus = document.getElementById('controlStatus');
                    controlStatus.textContent = `${fontFamily}: Bold ${boldAvailable ? '✅' : '❌'}, Italic ${italicAvailable ? '✅' : '❌'}`;
                    controlStatus.className = 'status ' + (boldAvailable || italicAvailable ? 'success' : 'warning');
                }
            }
            
            fontSelect.addEventListener('change', updateControls);
            boldCheck.addEventListener('change', testRendering);
            italicCheck.addEventListener('change', testRendering);
            testBtn.addEventListener('click', testRendering);
            
            updateControls();
            testRendering();
        }
        
        function testRendering() {
            const fontSelect = document.getElementById('fontSelect');
            const boldCheck = document.getElementById('boldCheck');
            const italicCheck = document.getElementById('italicCheck');
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            const renderingStatus = document.getElementById('renderingStatus');
            
            const fontFamily = fontSelect.value;
            const bold = boldCheck.checked;
            const italic = italicCheck.checked;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Test text
            const testText = 'DESIGN EDITOR TEST';
            
            try {
                // Get the loaded font family name
                const loadedFontFamily = detector.getLoadedFontFamily(fontFamily, bold, italic);
                
                // Set font properties
                ctx.font = `normal normal 48px "${loadedFontFamily}"`;
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                // Draw text
                ctx.fillText(testText, canvas.width / 2, canvas.height / 2);
                
                // Status
                let variant = 'regular';
                if (bold && italic) variant = 'bold italic';
                else if (bold) variant = 'bold';
                else if (italic) variant = 'italic';
                
                if (loadedFontFamily !== fontFamily) {
                    renderingStatus.textContent = `✅ Using loaded font variant: ${loadedFontFamily} (${variant})`;
                    renderingStatus.className = 'status success';
                } else {
                    renderingStatus.textContent = `⚠️ Using regular font: ${fontFamily} (${variant} variant may not be loaded yet)`;
                    renderingStatus.className = 'status warning';
                }
                
                // Update comparison canvases
                updateComparison(fontFamily, bold, italic, testText);
                
            } catch (error) {
                renderingStatus.textContent = `❌ Rendering error: ${error.message}`;
                renderingStatus.className = 'status error';
            }
        }
        
        function updateComparison(fontFamily, bold, italic, text) {
            // CSS method (old)
            const cssCanvas = document.getElementById('cssCanvas');
            const cssCtx = cssCanvas.getContext('2d');
            cssCtx.clearRect(0, 0, cssCanvas.width, cssCanvas.height);
            
            const fontWeight = bold ? 'bold' : 'normal';
            const fontStyle = italic ? 'italic' : 'normal';
            cssCtx.font = `${fontStyle} ${fontWeight} 24px "${fontFamily}"`;
            cssCtx.fillStyle = '#333';
            cssCtx.textAlign = 'center';
            cssCtx.textBaseline = 'middle';
            cssCtx.fillText(text, cssCanvas.width / 2, cssCanvas.height / 2);
            
            // Font file method (new)
            const fontFileCanvas = document.getElementById('fontFileCanvas');
            const fontFileCtx = fontFileCanvas.getContext('2d');
            fontFileCtx.clearRect(0, 0, fontFileCanvas.width, fontFileCanvas.height);
            
            const loadedFontFamily = detector.getLoadedFontFamily(fontFamily, bold, italic);
            fontFileCtx.font = `normal normal 24px "${loadedFontFamily}"`;
            fontFileCtx.fillStyle = '#333';
            fontFileCtx.textAlign = 'center';
            fontFileCtx.textBaseline = 'middle';
            fontFileCtx.fillText(text, fontFileCanvas.width / 2, fontFileCanvas.height / 2);
        }
        
        function runAutomatedTests() {
            const testResults = document.getElementById('testResults');
            let results = 'AUTOMATED TEST RESULTS:\n\n';
            
            // Test 1: Font variant availability
            const arialBold = detector.isVariantAvailable('Arial', 'bold');
            const impactBold = detector.isVariantAvailable('Impact', 'bold');
            results += `✅ Arial Bold available: ${arialBold}\n`;
            results += `✅ Impact Bold unavailable: ${!impactBold}\n`;
            
            // Test 2: Font loading
            const arialBoldLoaded = detector.getLoadedFontFamily('Arial', true, false);
            const arialRegularLoaded = detector.getLoadedFontFamily('Arial', false, false);
            results += `✅ Arial Bold loaded as: ${arialBoldLoaded}\n`;
            results += `✅ Arial Regular loaded as: ${arialRegularLoaded}\n`;
            
            // Test 3: Cache status
            results += `✅ Font cache size: ${detector.fontCache.size} variants\n`;
            
            testResults.textContent = results;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
