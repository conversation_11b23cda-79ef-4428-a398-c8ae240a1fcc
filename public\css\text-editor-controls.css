/* Tool Panel Styles */
.tools-panel {
    width: 300px;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
}

.tool-section {
    margin-bottom: 20px;
}

.tool-section h3 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: #fff;
}

/* Button Styles */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.control-button {
    background: #4CAF50;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-button:hover {
    background: #333;
}

.control-button.primary {
    background: #0066ff;
}

.control-button.primary:hover {
    background: #0052cc;
}

.control-button.small {
    padding: 8px;
    width: 36px;
    height: 36px;
    justify-content: center;
}

.control-button.small.active {
    background: #0066ff;
}

.control-button.danger {
    background: #dc3545;
}

.control-button.danger:hover {
    background: #bd2130;
}

/* Form Controls */
.font-select {
    width: 100%;
    padding: 8px;
    background: #2a2a2a;
    border: 1px solid #333;
    color: white;
    border-radius: 4px;
    margin-bottom: 10px;
}

.font-select option {
    padding: 8px;
    font-size: 16px;
    background: #2a2a2a;
    color: white;
}

/* Color Controls */
.color-picker {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.color-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
}

.color-option.active {
    border-color: #fff;
}

/* Mode Controls */
.mode-controls {
    width: 100%;
}

/* Toast Notifications */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 1000;
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.toast.show {
    opacity: 1;
}

.toast.error {
    background: rgba(220, 53, 69, 0.9);
}

.toast.success {
    background: rgba(40, 167, 69, 0.9);
}

.toast.info {
    background: rgba(0, 123, 255, 0.9);
}

/* Direction Options */
.direction-options {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.direction-options label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 14px;
    color: #fff;
}

.direction-options input[type="radio"] {
    margin: 0;
    cursor: pointer;
}

.direction-options input[type="radio"]:disabled + span {
    color: #666;
    cursor: not-allowed;
}
