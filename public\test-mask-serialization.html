<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mask Serialization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🎭 Mask Serialization Test</h1>
    
    <div class="test-container">
        <h2>Test Mask Property Preservation</h2>
        <p>This test verifies that masking properties are correctly preserved during serialization.</p>
        
        <button onclick="runMaskSerializationTest()">Run Mask Serialization Test</button>
        <button onclick="testBothCleaningFunctions()">Test Both Cleaning Functions</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>Instructions for Manual Testing</h2>
        <div class="test-section">
            <h3>1. Create Masked Image</h3>
            <ol>
                <li>Open the design editor</li>
                <li>Add an image and assign template ID "i01"</li>
                <li>Add a shape from Elements sidebar</li>
                <li>Select image → click "Mask with Shape" → click on shape</li>
                <li>Click "🎭 Validate Masks" to verify relationship</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>2. Save Template</h3>
            <ol>
                <li>Click "Save as Inspiration" button</li>
                <li>Check console logs for masking property preservation</li>
                <li>Look for "🎭 Preserved isMasked=true" messages</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>3. Test Generation Workflow</h3>
            <ol>
                <li>Go to generate-from-inspiration.html</li>
                <li>Select the saved template</li>
                <li>Verify mask validation status is shown</li>
                <li>Generate new image and verify mask is preserved</li>
            </ol>
        </div>
    </div>

    <script>
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            console.log(`[MaskTest] ${type.toUpperCase()}: ${message}`);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        function runMaskSerializationTest() {
            clearResults();
            logResult('🎭 Starting mask serialization test...', 'info');
            
            // Create test objects with masking properties
            const testMaskedImage = {
                id: 1,
                type: 'image',
                imageUrl: '/test/image.jpg',
                x: 100,
                y: 100,
                isMasked: true,
                maskShapeId: 2,
                isVisible: true,
                templateId: 'i01'
            };
            
            const testMaskShape = {
                id: 2,
                type: 'image',
                imageUrl: '/test/shape.svg',
                x: 110,
                y: 110,
                isMaskShape: true,
                isVisible: false
            };
            
            logResult('Created test objects with masking properties', 'success');
            
            // Test if cleaning functions exist
            if (typeof window.cleanObjectForSerialization === 'function') {
                logResult('Found cleanObjectForSerialization function', 'success');
                
                // Test masked image
                const cleanedMaskedImage = window.cleanObjectForSerialization(testMaskedImage);
                logResult(`Cleaned masked image: isMasked=${cleanedMaskedImage.isMasked}, maskShapeId=${cleanedMaskedImage.maskShapeId}`, 
                    cleanedMaskedImage.isMasked === true ? 'success' : 'error');
                
                // Test mask shape
                const cleanedMaskShape = window.cleanObjectForSerialization(testMaskShape);
                logResult(`Cleaned mask shape: isMaskShape=${cleanedMaskShape.isMaskShape}, isVisible=${cleanedMaskShape.isVisible}`, 
                    cleanedMaskShape.isMaskShape === true ? 'success' : 'error');
                
            } else {
                logResult('cleanObjectForSerialization function not found - open design editor first', 'error');
            }
        }

        function testBothCleaningFunctions() {
            clearResults();
            logResult('🔍 Testing both cleaning functions...', 'info');
            
            const testObject = {
                id: 1,
                type: 'image',
                imageUrl: '/test/image.jpg',
                isMasked: true,
                maskShapeId: 2,
                isMaskShape: false,
                isVisible: true,
                templateId: 'i01'
            };
            
            // Test design-editor.js function
            if (typeof window.cleanObjectForSerialization === 'function') {
                const result1 = window.cleanObjectForSerialization(testObject);
                logResult(`Design Editor Function: isMasked=${result1.isMasked}, maskShapeId=${result1.maskShapeId}`, 
                    result1.isMasked === true ? 'success' : 'error');
            } else {
                logResult('Design editor cleaning function not available', 'warning');
            }
            
            // Check if we can access the left-menu function
            // Note: This might not be directly accessible, but we can check the logs
            logResult('Check browser console for left-menu.js cleaning function logs when saving', 'info');
            logResult('Look for "[SaveProject] 🎭 Preserved isMasked=true" messages', 'info');
        }

        // Auto-run basic test if we're in the design editor context
        if (window.location.pathname.includes('design-editor')) {
            setTimeout(() => {
                logResult('Auto-running test in design editor context...', 'info');
                runMaskSerializationTest();
            }, 1000);
        }
    </script>
</body>
</html>
