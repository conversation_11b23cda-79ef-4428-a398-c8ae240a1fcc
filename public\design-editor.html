<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design Canvas Studio - Image & Text</title>
    <link rel="stylesheet" href="/style-design-editor.css">
    <link href="https://cdn.jsdelivr.net/npm/@simonwep/pickr@1.9.1/dist/themes/nano.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"> <!-- Added Font Awesome -->
    <link rel="stylesheet" href="/css/artboard-edit.css"> <!-- Artboard Edit Mode Styles -->
    <link rel="stylesheet" href="/css/left-menu.css"> <!-- Left Menu Styles -->
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;1,400&family=Arial&family=Verdana&family=Georgia&family=Times+New+Roman&family=Courier+New&family=Impact&family=Comic+Sans+MS:wght@400;700&display=swap" rel="stylesheet">
</head>
<body class="normal">
    <div class="studio-container">
        <div id="topbar"></div>

        <main class="main-content">
            <!-- Left Menu -->
            <div class="left-menu">
                <div class="left-menu-item" data-sidebar="menu-sidebar">
                    <img src="/images/ion-menu-outline-icon.svg" alt="Menu">
                </div>
                <div class="left-menu-item" data-sidebar="text-sidebar">
                    <img src="/images/ph-text-t-light-icon.svg" alt="Text">
                </div>
                <div class="left-menu-item" data-sidebar="elements-sidebar">
                    <img src="/images/ph-shapes-light-icon.svg" alt="Elements">
                </div>
                <div class="left-menu-item" data-sidebar="images-sidebar">
                    <img src="/images/ph-images-square-light-icon.svg" alt="Images">
                </div>
            </div>

            <!-- Left Sidebars -->
            <div class="left-sidebar" id="menu-sidebar">
                <div class="left-sidebar-header">
                    <h3>Menu</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <div class="section-title">Project Options</div>
                    <ul class="menu-items">
                        <li class="menu-item">New Project</li>
                        <li class="menu-item">My Projects</li>
                        <li class="menu-item" id="saveProjectBtn">Save Project</li>
                        <li class="menu-item" id="updateProjectBtn" style="display: none;">Save Project</li>
                        <li class="menu-item" id="saveAsProjectBtn" style="display: none;">📋 Save As New Project</li>
                        <li class="menu-item">Duplicate Project</li>
                    </ul>
                </div>
            </div>

            <div class="left-sidebar" id="text-sidebar">
                <div class="left-sidebar-header">
                    <h3>Text Library</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <div class="section-title">Saved Text Styles</div>
                    <div class="text-styles-grid" id="text-styles-grid">
                        <!-- Text styles will be loaded dynamically here -->
                        <div class="loading-message">Loading text styles...</div>
                    </div>
                </div>
            </div>

            <div class="left-sidebar" id="elements-sidebar">
                <div class="left-sidebar-header">
                    <h3>Elements</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <!-- Abstract Shapes Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="abstract">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Abstract</span>
                        </div>
                        <div class="accordion-content" id="abstract-content">
                            <div class="element-grid" id="abstract-grid">
                                <!-- Abstract shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Geometric Shapes Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="geometric">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Geometric</span>
                        </div>
                        <div class="accordion-content" id="geometric-content">
                            <div class="element-grid" id="geometric-grid">
                                <!-- Geometric shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Hand Drawn Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="hand-drawn">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Hand Drawn</span>
                        </div>
                        <div class="accordion-content" id="hand-drawn-content">
                            <div class="element-grid" id="hand-drawn-grid">
                                <!-- Hand drawn shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Ink Brush Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="ink">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Ink</span>
                        </div>
                        <div class="accordion-content" id="ink-content">
                            <div class="element-grid" id="ink-grid">
                                <!-- Ink brush shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Grunge Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="grunge">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Grunge</span>
                        </div>
                        <div class="accordion-content" id="grunge-content">
                            <div class="element-grid" id="grunge-grid">
                                <!-- Grunge shapes will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="left-sidebar" id="images-sidebar">
                <div class="left-sidebar-header">
                    <h3>Images</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <div class="section-title">Stock Images</div>
                    <div class="image-grid">
                        <!-- Stock images will be loaded dynamically here -->
                    </div>
                </div>
            </div>
            <div class="canvas-area" id="canvas-area">
                <canvas id="demo" width="2048" height="2048"></canvas>
                 <div class="canvas-controls">
                      <button id="zoomOutBtn" title="Zoom Out">-</button>
                      <span class="zoom-level" id="zoomLevel">100%</span>
                      <button id="zoomInBtn" title="Zoom In">+</button>
                      <div class="separator"></div> <!-- Separator -->
                      <!-- Div for Pickr initialization -->
                      <div id="canvasBgColorPicker" title="Change Background Color"></div>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="toggleArtboardBtn" title="Toggle Artboard">Artboard</button>
                      <button id="addToCollectionBtn" title="Add Artboard to Collection">Add to Collection</button>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="moveForwardBtn" title="Move Forward" disabled>
                          <img src="/images/radix-icons-stack-icon.svg" alt="Forward" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                      <button id="moveBackwardBtn" title="Move Backward" disabled>
                          <img src="/images/radix-icons-stack-icon.svg" alt="Backward" style="width: 16px; height: 16px; vertical-align: middle; transform: rotate(180deg);">
                      </button>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="copyElementBtn" title="Copy Selected Element" disabled>
                          <img src="/images/icons/ph-copy-light-icon.svg" alt="Copy" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                      <button id="pasteElementBtn" title="Paste Element" disabled>
                          <img src="/images/icons/ph-clipboard-light-icon.svg" alt="Paste" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="undoBtn" title="Undo" disabled>
                          <img src="/images/icons/redo.svg" alt="Undo" style="width: 16px; height: 16px; vertical-align: middle; transform: scaleX(-1);">
                      </button>
                      <button id="redoBtn" title="Redo" disabled>
                          <img src="/images/icons/redo.svg" alt="Redo" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                 </div>
            </div>

            <aside class="sidebar">
                <div class="sidebar-tabs">
                    <button class="sidebar-tab active" data-tab="text-tab-content">Text</button>
                    <button class="sidebar-tab" data-tab="image-tab-content">Image</button>
                    <button class="sidebar-tab" data-tab="admin-tab-content">Admin</button> <!-- Added Admin Tab -->
                </div>

                <!-- TEXT TAB CONTENT -->
                <div class="sidebar-content active" id="text-tab-content">
                    <div class="action-buttons">
                        <button id="addEditTextBtn" class="add-btn">Add</button>
                        <button id="deleteTextBtn" class="delete-btn" title="Delete Selected Text" disabled>Delete</button>
                    </div>
                    <div class="text-properties-header">Text Properties</div>
                    <div class="text-property-tabs">
                        <button class="property-tab active" data-panel="basic-panel">Basic</button>
                        <button class="property-tab" data-panel="distort-panel">Distort</button>
                        <button class="property-tab" data-panel="shadow-panel">Shadow</button>
                        <button class="property-tab" data-panel="decor-panel">Decor</button>
                    </div>
                     <!-- TEXT Panels Wrapper -->
                     <div id="text-controls">
                        <!-- BASIC -->
                        <div class="property-panel active basic-panel">
                            <div class="control-group">
                                <label for="iText">Text:</label>
                                <input id="iText" type="text" value="" disabled>
                            </div>
                            <div class="control-group">
                                <label for="iTextColor">Text Color:</label>
                                <div class="simplified-color-picker">
                                    <input id="iTextColor" type="color" value="#FF0000" disabled>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iTextColorIntensity">Color Intensity:</label>
                                <select id="iTextColorIntensity" disabled style="width: 100%; padding: 6px; border: 1px solid #d1d5db; border-radius: 4px; background-color: #fff;">
                                    <option value="no-change">No Change (keep current color)</option>
                                    <option value="light">Dark (Darkest colors from palette)</option>
                                    <option value="medium">Medium (Medium colors from palette)</option>
                                    <option value="dark">Light (Lightest color from palette)</option>
                                </select>
                                <small style="color: #666; font-size: 11px; display: block; margin-top: 3px;">
                                    Choose how this text should use colors from the selected palette
                                </small>
                                <!-- Debug display for persistent Color Intensity -->
                                <div style="margin-top: 8px; padding: 8px; background-color: #f0f9ff; border: 2px solid #3b82f6; border-radius: 6px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);">
                                    <span style="font-size: 12px; color: #1e40af; font-weight: bold;">💾 Debug newColorIntensity: </span>
                                    <span id="debugNewColorIntensity" style="font-size: 12px; color: #1e40af; font-weight: bold;">N/A</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iFontFamily">Font:</label>
                                <select id="iFontFamily" disabled>
                                    <option value="Poppins">Poppins</option>
                                    <option value="Arial">Arial</option>
                                    <option value="Verdana">Verdana</option>
                                    <option value="Georgia">Georgia</option>
                                    <option value="Times New Roman">Times New Roman</option>
                                    <option value="Courier New">Courier New</option>
                                    <option value="Impact">Impact</option>
                                    <option value="Comic Sans MS">Comic Sans MS</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label>Style:</label>
                                <div class="font-style-controls">
                                    <label for="iBold">B</label><input id="iBold" type="checkbox" disabled>
                                    <label for="iItalic">I</label><input id="iItalic" type="checkbox" disabled>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iFontSize">Font Size:</label>
                                <div class="slider-container">
                                    <input id="iFontSize" type="range" min="10" max="500" value="100" step="1" disabled>
                                    <span class="slider-value" id="vFontSize">100px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iTextRotation">Rotation:</label>
                                <div class="slider-container">
                                    <input id="iTextRotation" type="range" min="-180" max="180" value="0" step="1" disabled>
                                    <span class="slider-value" id="vTextRotation">0°</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iLetterSpacing">Letter Spacing:</label>
                                <div class="slider-container">
                                    <input id="iLetterSpacing" type="range" min="-20" max="100" value="0" step="1" disabled>
                                    <span class="slider-value" id="vLetterSpacing">0px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iOpacity">Opacity:</label>
                                <div class="slider-container">
                                    <input id="iOpacity" type="range" min="1" max="100" value="100" step="1" disabled>
                                    <span class="slider-value" id="vOpacity">100%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="strokeToggle">Stroke:</label>
                                <select id="strokeToggle" disabled>
                                    <option value="noStroke" selected>No Stroke</option>
                                    <option value="stroke">Standard</option>
                                </select>
                            </div>
                            <div class="parameter-control stroke-param">
                                <h4>Standard Stroke</h4>
                                <div class="control-group">
                                    <label for="strokeWidth">Width:</label>
                                    <div class="slider-container">
                                        <input type="range" id="strokeWidth" min="0" max="30" value="1" disabled>
                                        <span class="slider-value" id="vStrokeWidth">1px</span>
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label for="strokeColor">Color:</label>
                                    <div class="simplified-color-picker">
                                        <input type="color" id="strokeColor" value="#000000" disabled>
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label for="strokeOpacity">Opacity:</label>
                                    <div class="slider-container">
                                        <input type="range" id="strokeOpacity" min="0" max="100" value="100" step="1" disabled>
                                        <span class="slider-value" id="vStrokeOpacity">100%</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Template ID for text replacement -->
                            <div class="control-group" style="border-top: 2px solid #e74c3c; margin-top: 15px; padding-top: 15px;">
                                <label for="textTemplateId" style="color: #e74c3c; font-weight: bold;">Template ID:</label>
                                <select id="textTemplateId" style="border: 2px solid #e74c3c; background-color: #fff5f5;" disabled>
                                    <option value="">Not Replaceable</option>
                                    <option value="t01">t01 - Text 1</option>
                                    <option value="t02">t02 - Text 2</option>
                                    <option value="t03">t03 - Text 3</option>
                                    <option value="t04">t04 - Text 4</option>
                                    <option value="t05">t05 - Text 5</option>
                                    <option value="t06">t06 - Text 6</option>
                                    <option value="t07">t07 - Text 7</option>
                                    <option value="t08">t08 - Text 8</option>
                                    <option value="t09">t09 - Text 9</option>
                                    <option value="t10">t10 - Text 10</option>
                                </select>
                                <small style="color: #7f8c8d; font-size: 11px; display: block; margin-top: 5px;">
                                    Select an ID to make this text replaceable in templates. Leave as "Not Replaceable" to keep text fixed.
                                </small>
                            </div>
                            <!-- Debug display for persistent Template ID -->
                                <div style="margin-top: 8px; padding: 8px; background-color: #fef2f2; border: 2px solid #e74c3c; border-radius: 6px; box-shadow: 0 2px 4px rgba(231, 76, 60, 0.1);">
                                    <span style="font-size: 12px; color: #dc2626; font-weight: bold;">🔗 Debug newTemplateId: </span>
                                    <span id="debugNewTemplateId" style="font-size: 12px; color: #dc2626; font-weight: bold;">N/A</span>
                                </div>
                        </div>

                        <!-- DISTORT -->
                        <div class="property-panel distort-panel"> <div class="control-group"> <label for="effectMode">Effect:</label> <select id="effectMode" disabled> <option value="normal">Normal</option> <option value="skew">Skew</option> <option value="warp">Warp</option> <option value="curve">Curved</option> <option value="circle">Circular</option> <option value="mesh">Mesh Warp</option> <option value="grid-distort">Grid Distort</option> </select> </div> <div class="parameter-control horizontal-skew control-group" id="horizontalSkewControl"> <label>Skew X:</label> <div class="slider-container"><input type="range" id="skewSlider" min="-50" max="50" value="0" step="1" disabled><span class="slider-value" id="vSkew">0</span></div> </div> <div class="parameter-control vertical-skew control-group" id="verticalSkewControl"> <label>Skew Y:</label> <div class="slider-container"><input type="range" id="skewYSlider" min="-50" max="50" value="0" step="1" disabled><span class="slider-value" id="vSkewY">0</span></div> </div> <div class="parameter-control mesh-param"> <h4>Mesh Settings</h4> <div class="control-group"><label>Columns:</label><div class="slider-container"><input id="iMeshCols" type="range" min="3" max="9" value="5" step="1" disabled><span class="slider-value" id="vMeshCols">5</span></div></div> <div class="control-group"><label>Rows:</label><div class="slider-container"><input id="iMeshRows" type="range" min="2" max="5" value="3" step="1" disabled><span class="slider-value" id="vMeshRows">3</span></div></div> <div class="control-group"><button id="resetMeshBtn" disabled>Reset Points</button></div> </div> <div class="parameter-control warp-param"> <h4>Warp Settings</h4> <div class="control-group"><label>Curve:</label><div class="slider-container"><input id="iCurve" type="range" min=-500 max=500 value=100 disabled><span class="slider-value" id="vCurve">100</span></div></div> <div class="control-group"><label>Offset Y:</label><div class="slider-container"><input id="iOffset" type="range" min=-500 max=500 value=10 disabled><span class="slider-value" id="vOffset">10</span></div></div> <div class="control-group"><label>Height:</label><div class="slider-container"><input id="iHeight" type="range" min=-500 max=500 value=100 disabled><span class="slider-value" id="vHeight">100</span></div></div> <div class="control-group"><label>Bottom:</label><div class="slider-container"><input id="iBottom" type="range" min=-500 max=500 value=150 disabled><span class="slider-value" id="vBottom">150</span></div></div> <div class="control-group"><label>Triangle:</label> <input id="iTriangle" type="checkbox" disabled></div> <div class="control-group shift-center-control"><label>Shift Center:</label><div class="slider-container"><input id="iShiftCenter" type="range" min="0" max="200" value="100" disabled><span class="slider-value" id="vShiftCenter">100</span></div></div> </div> <div class="parameter-control circle-param"> <h4>Circular Settings</h4> <div class="control-group"><label>Diameter:</label><div class="slider-container"><input id="iDiameter" type="range" min=100 max=1500 value=600 step=1 disabled><span class="slider-value" id="vDiameter">600px</span></div></div> <div class="control-group"><label>Kerning:</label><div class="slider-container"><input id="iKerning" type="range" min=-20 max=50 value="0" step=1 disabled><span class="slider-value" id="vKerning">0px</span></div></div> <div class="control-group"><label>Rotation:</label><div class="slider-container"><input id="iCircleRotation" type="range" min="-180" max="180" value="0" step="1" disabled><span class="slider-value" id="vCircleRotation">0°</span></div></div> <div class="control-group"><label>Flip:</label><input id="iFlip" type="checkbox" disabled></div> </div> <div class="parameter-control curve-param"> <h4>Curved Settings</h4> <div class="control-group"><label>Curve:</label><div class="slider-container"><input id="iCurveAmount" type="range" min=-100 max=100 value=40 step=1 disabled><span class="slider-value" id="vCurveAmount">40</span></div></div> <div class="control-group"><label>Spacing:</label><div class="slider-container"><input id="iCurveKerning" type="range" min=-20 max=50 value=0 step=1 disabled><span class="slider-value" id="vCurveKerning">0px</span></div></div> <div class="control-group"><label>Flip:</label><input id="iCurveFlip" type="checkbox" disabled></div> </div>
<div class="parameter-control grid-distort-param"> <h4>Grid Distort Settings</h4> <div class="control-group"><label>Columns:</label><div class="slider-container"><input id="iGridDistortCols" type="range" min="1" max="5" value="2" step="1" disabled><span class="slider-value" id="vGridDistortCols">2</span></div></div> <div class="control-group"><label>Rows:</label><div class="slider-container"><input id="iGridDistortRows" type="range" min="1" max="5" value="1" step="1" disabled><span class="slider-value" id="vGridDistortRows">1</span></div></div> <div class="control-group"><label>Padding:</label><div class="slider-container"><input id="iGridDistortPadding" type="range" min="20" max="200" value="80" step="1" disabled><span class="slider-value" id="vGridDistortPadding">80px</span></div></div> <div class="control-group"><label>Intensity:</label><div class="slider-container"><input id="iGridDistortIntensity" type="range" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vGridDistortIntensity">100%</span></div></div> <div class="control-group"><label>Direction:</label><div class="direction-options"><label><input type="radio" name="gridDistortDirection" id="gridDistortDirectionBoth" value="both" disabled> Both</label><label><input type="radio" name="gridDistortDirection" id="gridDistortDirectionVertical" value="vertical" checked disabled> Vertical Only</label></div></div> <div class="control-group"><button id="resetGridDistortBtn" disabled>Reset Grid</button></div> <div class="control-group"><button id="toggleGridDistortBtn" disabled>Show Grid</button></div> </div> </div>
                        <!-- SHADOW --> <div class="property-panel shadow-panel"> <div class="control-group"> <label for="shadow">Shadow:</label> <select id="shadow" disabled> <option value="noShadow" selected>No Shadow</option> <option value="shadow">Standard</option> <option value="blockShadow">Block</option> <option value="perspectiveShadow">Perspective Shadow</option> <option value="lineShadow">Line</option> <option value="detailed3D">Detailed 3D</option> </select> </div> <div class="parameter-control shadow-param"> <h4>Standard Shadow</h4> <div class="control-group"><label for="shadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="shadowColor" value="#000000" disabled></div></div> <div class="control-group"><label for="shadowOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="shadowOffsetX" class="slider" min="-50" max="50" value="5" step="1" disabled><span class="slider-value" id="vShadowOffsetX">5px</span></div></div> <div class="control-group"><label for="shadowOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="shadowOffsetY" class="slider" min="-50" max="50" value="5" step="1" disabled><span class="slider-value" id="vShadowOffsetY">5px</span></div></div> <div class="control-group"><label for="shadowBlur">Blur:</label><div class="slider-container"><input type="range" id="shadowBlur" class="slider" min="0" max="50" value="10" step="1" disabled><span class="slider-value" id="vShadowBlur">10px</span></div></div> </div> <div class="parameter-control block-shadow-param"> <h4>Block Shadow</h4> <div class="control-group"><label for="blockShadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="blockShadowColor" value="#000000" disabled></div></div> <div class="control-group"><label for="blockShadowOpacity">Opacity:</label><div class="slider-container"><input type="range" id="blockShadowOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vBlockShadowOpacity">100%</span></div></div> <div class="control-group"><label for="blockShadowOffset">Distance:</label><div class="slider-container"><input type="range" id="blockShadowOffset" class="slider" min="0" max="200" value="40" step="1" disabled><span class="slider-value" id="vBlockShadowOffset">40px</span></div></div> <div class="control-group"><label for="blockShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="blockShadowAngle" class="slider" min="-180" max="180" value="-58" step="1" disabled><span class="slider-value" id="vBlockShadowAngle">-58°</span></div></div> <div class="control-group"><label for="blockShadowBlur">Blur:</label><div class="slider-container"><input type="range" id="blockShadowBlur" class="slider" min="0" max="50" value="5" step="1" disabled><span class="slider-value" id="vBlockShadowBlur">5px</span></div></div> <div class="control-group"><label for="blockShadowPerspective">Perspective:</label><div class="switch-container"><label class="switch-label"><input type="checkbox" id="blockShadowPerspective" disabled><span class="switch-custom"></span>Enable Perspective</label></div></div>
<div class="control-group perspective-control" style="display: none; margin-top: 10px; padding: 8px; background-color: #f0f9ff; border-left: 3px solid #3b82f6; border-radius: 4px;"><label for="blockShadowPerspectiveIntensity">Perspective Intensity:</label><div class="slider-container"><input type="range" id="blockShadowPerspectiveIntensity" class="slider" min="1" max="100" value="50" step="1" disabled><span class="slider-value" id="vBlockShadowPerspectiveIntensity">50%</span></div><div class="control-hint">Higher values make distant shadows smaller, 1% = almost no perspective</div></div> </div>

<div class="parameter-control perspective-shadow-param"> <h4>Perspective Shadow</h4> <div class="control-group"><label for="perspectiveShadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="perspectiveShadowColor" value="#333333" disabled></div></div> <div class="control-group"><label for="perspectiveShadowOpacity">Opacity:</label><div class="slider-container"><input type="range" id="perspectiveShadowOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOpacity">100%</span></div></div> <div class="control-group"><label for="perspectiveShadowOffset">Distance:</label><div class="slider-container"><input type="range" id="perspectiveShadowOffset" class="slider" min="0" max="20" value="6" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOffset">6px</span></div></div> <div class="control-group"><label for="perspectiveShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="perspectiveShadowAngle" class="slider" min="-180" max="180" value="105" step="1" disabled><span class="slider-value" id="vPerspectiveShadowAngle">105°</span></div></div> <div class="control-group"><label for="perspectiveShadowBlur">Blur:</label><div class="slider-container"><input type="range" id="perspectiveShadowBlur" class="slider" min="0" max="50" value="2" step="1" disabled><span class="slider-value" id="vPerspectiveShadowBlur">2px</span></div></div> <div class="control-group"><label for="perspectiveShadowIntensity">Perspective Intensity:</label><div class="slider-container"><input type="range" id="perspectiveShadowIntensity" class="slider" min="1" max="100" value="16" step="1" disabled><span class="slider-value" id="vPerspectiveShadowIntensity">16%</span></div><div class="control-hint">Higher values make distant shadows smaller, 1% = almost no perspective</div></div>
<h5>Front Outline</h5>
<div class="control-group"><label for="perspectiveShadowOutlineColor">Color:</label><div class="simplified-color-picker"><input type="color" id="perspectiveShadowOutlineColor" value="#d1d5db" disabled></div></div>
<div class="control-group"><label for="perspectiveShadowOutlineOpacity">Opacity:</label><div class="slider-container"><input type="range" id="perspectiveShadowOutlineOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOutlineOpacity">100%</span></div></div>
<div class="control-group"><label for="perspectiveShadowOutlineWidth">Width:</label><div class="slider-container"><input type="range" id="perspectiveShadowOutlineWidth" class="slider" min="0" max="30" value="3" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOutlineWidth">3px</span></div></div>
<div class="control-group"><label for="perspectiveShadowOutlineOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="perspectiveShadowOutlineOffsetX" class="slider" min="-50" max="50" value="2" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOutlineOffsetX">2px</span></div></div>
<div class="control-group"><label for="perspectiveShadowOutlineOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="perspectiveShadowOutlineOffsetY" class="slider" min="-50" max="50" value="-3" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOutlineOffsetY">-3px</span></div></div>
</div> <div class="parameter-control line-shadow-param"> <h4>Line Shadow</h4> <div class="control-group"><label for="lineShadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="lineShadowColor" value="#AAAAAA" disabled></div></div> <div class="control-group"><label for="lineShadowDistance">Distance:</label><div class="slider-container"><input type="range" id="lineShadowDistance" class="slider" min="0" max="100" value="15" step="1" disabled><span class="slider-value" id="vLineShadowDistance">15px</span></div></div> <div class="control-group"><label for="lineShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="lineShadowAngle" class="slider" min="-180" max="180" value="-45" step="1" disabled><span class="slider-value" id="vLineShadowAngle">-45°</span></div></div> <div class="control-group"><label for="lineShadowThickness">Thickness:</label><div class="slider-container"><input type="range" id="lineShadowThickness" class="slider" min="1" max="30" value="5" step="1" disabled><span class="slider-value" id="vLineShadowThickness">5px</span></div></div> </div> <div class="parameter-control detailed-3d-param"> <h4>Detailed 3D</h4> <h5>Extrusion</h5> <div class="control-group"><label for="detailed3DPrimaryColor">Color:</label><div class="simplified-color-picker"><input type="color" id="detailed3DPrimaryColor" value="#000000" disabled></div></div> <div class="control-group"><label for="detailed3DPrimaryOpacity">Opacity:</label><div class="slider-container"><input type="range" id="detailed3DPrimaryOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vDetailed3DPrimaryOpacity">100%</span></div></div> <div class="control-group"><label for="detailed3DOffset">Distance:</label><div class="slider-container"><input type="range" id="detailed3DOffset" class="slider" min="0" max="200" value="36" step="1" disabled><span class="slider-value" id="vDetailed3DOffset">36px</span></div></div> <div class="control-group"><label for="detailed3DAngle">Angle:</label><div class="slider-container"><input type="range" id="detailed3DAngle" class="slider" min="-180" max="180" value="-63" step="1" disabled><span class="slider-value" id="vDetailed3DAngle">-63°</span></div></div> <div class="control-group"><label for="detailed3DBlur">Blur:</label><div class="slider-container"><input type="range" id="detailed3DBlur" class="slider" min="0" max="50" value="5" step="1" disabled><span class="slider-value" id="vDetailed3DBlur">5px</span></div></div> <h5>Front Outline</h5> <div class="control-group"><label for="detailed3DSecondaryColor">Color:</label><div class="simplified-color-picker"><input type="color" id="detailed3DSecondaryColor" value="#00FF00" disabled></div></div> <div class="control-group"><label for="detailed3DSecondaryOpacity">Opacity:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOpacity">100%</span></div></div> <div class="control-group"><label for="detailed3DSecondaryWidth">Width:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryWidth" class="slider" min="0" max="30" value="4" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryWidth">4px</span></div></div> <div class="control-group"><label for="detailed3DSecondaryOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOffsetX" class="slider" min="-50" max="50" value="-5" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOffsetX">-5px</span></div></div> <div class="control-group"><label for="detailed3DSecondaryOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOffsetY" class="slider" min="-50" max="50" value="-5" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOffsetY">-5px</span></div></div> </div> </div>
                        <!-- DECOR --> <div class="property-panel decor-panel"> <div class="control-group"> <label for="linesDecoration">Fill Decor:</label> <select id="linesDecoration" disabled> <option value="noDecoration">None</option> <option value="horizontalLines">Horizontal Lines</option> <option value="colorCut">Color Cut</option> <option value="obliqueLines">Oblique Lines</option> <option value="fadingLinesCut">Fading Lines</option> <option value="diagonalLines">Diagonal Lines</option> </select> </div> <div class="parameter-control horizontal-lines-param"> <h4>Horizontal Lines</h4> <div class="control-group"><label for="hWeight">Weight:</label><div class="slider-container"><input type="range" id="hWeight" min="1" max="30" value="3" disabled><span class="slider-value" id="vHWeight">3px</span></div></div> <div class="control-group"><label for="hDistance">Distance:</label><div class="slider-container"><input type="range" id="hDistance" min="1" max="50" value="7" disabled><span class="slider-value" id="vHDistance">7px</span></div></div> <div class="control-group"><label for="hColor">Line Color:</label><div class="simplified-color-picker"><input type="color" id="hColor" value="#0000FF" disabled></div></div> </div> <div class="parameter-control color-cut-param"> <h4>Color Cut</h4> <div class="control-group"><label for="ccDistance">Cut (%):</label><div class="slider-container"><input type="range" id="ccDistance" min="1" max="100" value="50" disabled><span class="slider-value" id="vCcDistance">50%</span></div></div> <div class="control-group fill-direction"><label>Direction:</label><div class="radio-group"><label class="radio-container"><input type="radio" name="ccFillDirection" id="ccFillTop" value="top" checked disabled><span>Top</span></label><label class="radio-container"><input type="radio" name="ccFillDirection" id="ccFillBottom" value="bottom" disabled><span>Bottom</span></label></div></div> <div class="control-group"><label for="ccColor">Cut Color:</label><div class="simplified-color-picker"><input type="color" id="ccColor" value="#00FF00" disabled></div></div> </div> <div class="parameter-control oblique-lines-param"> <h4>Oblique Lines</h4> <div class="control-group"><label for="oWeight">Weight:</label><div class="slider-container"><input type="range" id="oWeight" min="1" max="30" value="4" disabled><span class="slider-value" id="vOWeight">4px</span></div></div> <div class="control-group"><label for="oDistance">Distance:</label><div class="slider-container"><input type="range" id="oDistance" min="1" max="50" value="3" disabled><span class="slider-value" id="vODistance">3px</span></div></div> <div class="control-group"><label for="oColor">Line Color:</label><div class="simplified-color-picker"><input type="color" id="oColor" value="#0000FF" disabled></div></div> </div> <div class="parameter-control fading-lines-cut-param"> <h4>Fading Lines</h4> <div class="control-group"><label for="flcDistance">Cut (%):</label><div class="slider-container"><input type="range" id="flcDistance" min="1" max="100" value="62" disabled><span class="slider-value" id="vFlcDistance">62%</span></div></div> <div class="control-group fill-direction"><label>Direction:</label><div class="radio-group"><label class="radio-container"><input type="radio" name="flcFillDirection" id="flcFillTop" value="top" checked disabled><span>Solid Top</span></label><label class="radio-container"><input type="radio" name="flcFillDirection" id="flcFillBottom" value="bottom" disabled><span>Solid Bottom</span></label></div></div> <div class="control-group"><label for="flcColor">Line/Fill:</label><div class="simplified-color-picker"><input type="color" id="flcColor" value="#cccccc" disabled></div></div> <div class="control-group"><label for="flcMaxWeight">Weight:</label><div class="slider-container"><input type="range" id="flcMaxWeight" min="1" max="30" value="3" disabled><span class="slider-value" id="vFlcMaxWeight">3px</span></div></div> <div class="control-group"><label for="flcSpacing">Spacing:</label><div class="slider-container"><input type="range" id="flcSpacing" min="1" max="40" value="10" disabled><span class="slider-value" id="vFlcSpacing">10px</span></div></div> </div> </div>
                     </div> <!-- End Text Controls Wrapper -->
                </div><!-- End Text Tab -->

                <!-- IMAGE TAB CONTENT -->
                <div class="sidebar-content" id="image-tab-content">
                     <div class="action-buttons">
                        <input type="file" id="image-file-input" accept="image/*">
                        <button id="addImageBtn" class="add-btn">Add Image</button>
                        <button id="deleteImageBtn" class="delete-btn" title="Delete Selected Image" disabled>Delete</button>
                    </div>
                    <div class="image-properties-header">Image Properties</div>
                    <div id="image-controls">
                        <div class="control-group">
                            <label for="iImageSize">Size:</label>
                            <div class="slider-container"> <input id="iImageSize" type="range" min="0.1" max="5" value="1" step="0.05"> <span class="slider-value" id="vImageSize">100%</span> </div>
                        </div>
                        <div class="control-group">
                            <label for="iImageRotation">Rotation:</label>
                            <div class="slider-container"> <input id="iImageRotation" type="range" min="-180" max="180" value="0" step="1"> <span class="slider-value" id="vImageRotation">0°</span> </div>
                        </div>
                        <div class="control-group">
                            <label for="iImageOpacity">Opacity:</label>
                            <div class="slider-container">
                                <input type="range" id="iImageOpacity" min="0" max="100" value="100" step="1">
                                <span class="slider-value" id="vImageOpacity">100%</span>
                            </div>
                        </div>
                        <div class="control-group" id="imageColorGroup" style="display: none;">
                            <label for="iImageColor">Color:</label>
                            <div class="simplified-color-picker"><input type="color" id="iImageColor" value="#000000"></div>
                        </div>
                        <div class="control-group" id="imageColorIntensityGroup" style="display: none;">
                            <label for="iImageColorIntensity">Color Intensity:</label>
                            <select id="iImageColorIntensity" style="width: 100%; padding: 6px; border: 1px solid #d1d5db; border-radius: 4px; background-color: #fff;">
                                <option value="no-change">No Change (keep current color)</option>
                                <option value="light">Dark (Darkest colors from palette)</option>
                                <option value="medium">Medium (Medium colors from palette)</option>
                                <option value="dark">Light (Lightest colors from palette)</option>
                            </select>
                            <small style="color: #666; font-size: 11px; display: block; margin-top: 3px;">
                                Choose how this shape should use colors from the selected palette
                            </small>
                            <!-- Debug display for persistent Color Intensity (Images) -->
                            <div style="margin-top: 8px; padding: 8px; background-color: #f0f9ff; border: 2px solid #3b82f6; border-radius: 6px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);">
                                <span style="font-size: 12px; color: #1e40af; font-weight: bold;">💾 Debug newColorIntensity: </span>
                                <span id="debugImageNewColorIntensity" style="font-size: 12px; color: #1e40af; font-weight: bold;">N/A</span>
                            </div>
                        </div>

                        <!-- Image Stroke Controls -->
                        <div class="control-group">
                            <label for="iImageStroke">Stroke:</label>
                            <select id="iImageStroke">
                                <option value="none" selected>No Stroke</option>
                                <option value="standard">Standard</option>
                            </select>
                        </div>

                        <!-- Standard Stroke Controls -->
                        <div class="parameter-control image-stroke-param" style="display: none;">
                            <h4>Standard Stroke</h4>
                            <div class="control-group">
                                <label for="iImageStrokeWidth">Width:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageStrokeWidth" min="1" max="50" value="3" step="1">
                                    <span class="slider-value" id="vImageStrokeWidth">3px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageStrokeColor">Color:</label>
                                <div class="simplified-color-picker">
                                    <input type="color" id="iImageStrokeColor" value="#000000">
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageStrokeOpacity">Opacity:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageStrokeOpacity" min="0" max="100" value="100" step="1">
                                    <span class="slider-value" id="vImageStrokeOpacity">100%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Image Shadow Controls -->
                        <div class="control-group">
                            <label for="iImageShadow">Shadow:</label>
                            <select id="iImageShadow">
                                <option value="none" selected>No Shadow</option>
                                <option value="standard">Standard</option>
                            </select>
                        </div>

                        <!-- Standard Shadow Controls -->
                        <div class="parameter-control image-shadow-param" style="display: none;">
                            <h4>Standard Shadow</h4>
                            <div class="control-group">
                                <label for="iImageShadowColor">Color:</label>
                                <div class="simplified-color-picker">
                                    <input type="color" id="iImageShadowColor" value="#000000">
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageShadowOpacity">Opacity:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageShadowOpacity" min="0" max="100" value="100" step="1">
                                    <span class="slider-value" id="vImageShadowOpacity">100%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageShadowOffsetX">Offset X:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageShadowOffsetX" min="-50" max="50" value="5" step="1">
                                    <span class="slider-value" id="vImageShadowOffsetX">5px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageShadowOffsetY">Offset Y:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageShadowOffsetY" min="-50" max="50" value="5" step="1">
                                    <span class="slider-value" id="vImageShadowOffsetY">5px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageShadowBlur">Blur:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageShadowBlur" min="0" max="50" value="10" step="1">
                                    <span class="slider-value" id="vImageShadowBlur">10px</span>
                                </div>
                            </div>
                        </div>

                        <!-- Add Remove Background Button -->
                        <button id="removeBgBtn" class="action-btn" style="display: none; margin-top: 10px; background-color: #6366f1; color: white; width: 100%; padding: 10px;">Remove Background</button>

                        <!-- Image Masking Controls -->
                        <div class="control-group" id="imageMaskingGroup" style="display: none;">
                            <button id="maskImageBtn" class="action-btn" style="background-color: #10b981; color: white; width: 100%; padding: 10px; margin-top: 10px;">Mask with Shape</button>
                            <button id="unmaskImageBtn" class="action-btn" style="display: none; background-color: #ef4444; color: white; width: 100%; padding: 10px; margin-top: 5px;">Remove Mask</button>
                        </div>

                        <!-- Template ID for image replacement -->
                        <div class="control-group" style="border-top: 2px solid #e74c3c; margin-top: 15px; padding-top: 15px;">
                            <label for="imageTemplateId" style="color: #e74c3c; font-weight: bold;">Template ID:</label>
                            <select id="imageTemplateId" style="border: 2px solid #e74c3c; background-color: #fff5f5;" disabled>
                                <option value="">Not Replaceable</option>
                                <option value="i01">i01 - Main Image</option>
                                <option value="i02">i02 - Image 2</option>
                                <option value="i03">i03 - Image 3</option>
                                <option value="i04">i04 - Image 4</option>
                                <option value="i05">i05 - Image 5</option>
                                <optgroup label="🎭 Mask Shapes">
                                    <option value="mask01">mask01 - Mask Shape 1</option>
                                    <option value="mask02">mask02 - Mask Shape 2</option>
                                    <option value="mask03">mask03 - Mask Shape 3</option>
                                </optgroup>
                            </select>
                            <small style="color: #7f8c8d; font-size: 11px; display: block; margin-top: 5px;">
                                Select an ID to make this image replaceable in templates. i01 is the main image that gets replaced with generated content.
                            </small>
                        </div>
                        <!-- Debug display for persistent Template ID (Images) -->
                            <div style="margin-top: 5px; padding: 5px; background-color: #fef2f2; border: 1px solid #e74c3c; border-radius: 4px;">
                                <span style="font-size: 10px; color: #dc2626; font-weight: bold;">Debug newTemplateId: </span>
                                <span id="debugImageNewTemplateId" style="font-size: 10px; color: #dc2626;">N/A</span>
                            </div>
                    </div>
                     <p id="no-image-selected-msg">No image selected.</p>
                </div><!-- End Image Tab -->

                <!-- ADMIN TAB CONTENT -->
                <div class="sidebar-content" id="admin-tab-content">
                    <div class="admin-properties-header" style="font-size: 1.1em; font-weight: 600; margin-bottom: 15px; color: #1e293b; border-bottom: 1px solid #e2e8f0; padding-bottom: 8px;">Inspiration Details</div>
                    <div id="admin-controls" style="display: flex; flex-direction: column; gap: 12px;">
                        <div class="control-group">
                            <label for="adminImageUrl" style="flex-basis: 80px; text-align: left;">Image URL:</label>
                            <input id="adminImageUrl" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>
                        <div class="control-group">
                            <label for="adminModel" style="flex-basis: 80px; text-align: left;">Model:</label>
                            <input id="adminModel" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>
                         <div class="control-group">
                            <label for="adminPalette" style="flex-basis: 80px; text-align: left;">Palette:</label>
                            <input id="adminPalette" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>

                        <div class="control-group" style="flex-direction: column; align-items: flex-start;">
                            <label for="adminPrompt" style="flex-basis: auto; text-align: left; margin-bottom: 5px;">Prompt:</label>
                            <textarea id="adminPrompt" rows="8" readonly style="background-color: #e9ecef; width: 100%; border: 1px solid #d1d5db; border-radius: 6px; padding: 6px 10px; font-size: 0.9em; resize: vertical;"></textarea>
                        </div>
                        <input type="hidden" id="adminInspirationId"> <!-- Hidden field for ID -->

                        <!-- Template Save Buttons - Show different buttons based on whether editing existing template -->
                        <div id="templateSaveButtons">
                            <button id="saveTemplateBtn" class="add-btn" style="margin-top: 15px;">Save as Inspiration</button> <!-- Default for new templates -->
                            <button id="updateTemplateBtn" class="add-btn" style="margin-top: 15px; display: none; background-color: #10b981;">💾 Save</button> <!-- For existing templates -->
                            <button id="saveAsTemplateBtn" class="add-btn" style="margin-top: 10px; display: none;">📋 Save As New</button> <!-- For existing templates -->
                        </div>

                        <button id="saveTextStyleBtn" class="add-btn" style="margin-top: 10px;">Save Text Style</button> <!-- New Text Style button -->
                         <button id="validateMasksBtn" class="add-btn" style="margin-top: 10px; background-color: #6366f1;">🎭 Validate Masks</button> <!-- Debug button for mask validation -->
                    </div>
                </div><!-- End Admin Tab -->

            </aside>
        </main>
    </div>

    <!-- Color input replaced with text input for Coloris -->
    <!-- OpenType.js for Grid Distort effect -->
    <script src="https://cdn.jsdelivr.net/npm/opentype.js@1.3.4/dist/opentype.min.js"></script>

    <script src="/js/font-variant-detector.js"></script>
    <script src="/js/gradient-color-picker.js"></script>
    <script src="/js/decoration-module.js"></script>
    <script type="module">
        // Load color palette functions and make them globally available
        import { getPaletteById, getTextColorForPalette } from '/js/data/colorPalettes.js';
        import { setupProjectModal } from '/js/components/ProjectModal.js?v=1749502000';

        window.getPaletteById = getPaletteById;
        window.getTextColorForPalette = getTextColorForPalette;

        // Setup project modal functionality
        setupProjectModal();
    </script>
    <script src="/js/design-editor.js?v=1750011500"></script>
    <script src="/js/mesh-warp-implementation.js?v=1750012011"></script>
    <script src="/js/left-menu.js?v=1750012000"></script>
    <script src="/js/shapes-api.js"></script>
    <script src="/js/elements-accordion.js"></script>
    <script src="/js/images-loader.js"></script>
    <script type="module" src="/js/components/Topbar.js"></script>
    <script type="module" src="/js/components/CollectionModal.js"></script>
    <script type="module">
       import { createTopbar } from '/js/components/Topbar.js';

       document.addEventListener('DOMContentLoaded', async () => {
           const topbar = document.getElementById('topbar');
           if (topbar) {
               await createTopbar(topbar);
           }

           // Initialize font variant detection system
           if (window.fontVariantDetector && window.fontMap) {
               console.log('🔤 Initializing font variant detection system...');
               try {
                   window.fontVariantDetector.initialize(window.fontMap);
                   console.log('🔤 Font variant detection system initialized successfully');
               } catch (error) {
                   console.error('🔤 Failed to initialize font variant detection system:', error);
               }
           } else {
               console.warn('🔤 FontVariantDetector or fontMap not available');
           }


       });
    </script>
<script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr@1.9.1/dist/pickr.min.js"></script>
<collection-modal></collection-modal>
<project-modal></project-modal>
<toast-notification></toast-notification>
</body>
</html>
