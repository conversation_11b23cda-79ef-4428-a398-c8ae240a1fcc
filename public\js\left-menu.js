// Helper function to clean objects for serialization (same as template saving)
function cleanObjectForSerialization(obj) {
    // Create a clean copy without circular references
    const cleanObj = {};

    // Copy basic properties
    const basicProps = ['id', 'type', 'text', 'x', 'y', 'fontSize', 'fontFamily', 'color', 'bold', 'italic',
                       'rotation', 'scale', 'opacity', 'letterSpacing', 'effectMode', 'skewX', 'skewY',
                       'layerOrder', 'zIndex', 'templateId', 'generationId', 'isFromGeneration'];

    basicProps.forEach(prop => {
        if (obj.hasOwnProperty(prop)) {
            cleanObj[prop] = obj[prop];
        }
    });

    // Copy image-specific properties
    if (obj.type === 'image') {
        if (obj.imageUrl) cleanObj.imageUrl = obj.imageUrl;
        if (obj.src) cleanObj.src = obj.src;
        if (obj.width !== undefined) cleanObj.width = obj.width;
        if (obj.height !== undefined) cleanObj.height = obj.height;

        // 🎭 PRESERVE MASKING PROPERTIES FOR IMAGES
        if (obj.isMasked !== undefined) {
            cleanObj.isMasked = obj.isMasked;
            console.log(`[SaveProject] 🎭 Preserved isMasked=${obj.isMasked} for object ${obj.id}`);
        }
        if (obj.maskShapeId !== undefined) {
            cleanObj.maskShapeId = obj.maskShapeId;
            console.log(`[SaveProject] 🎭 Preserved maskShapeId=${obj.maskShapeId} for object ${obj.id}`);
        }
        if (obj.isMaskShape !== undefined) {
            cleanObj.isMaskShape = obj.isMaskShape;
            console.log(`[SaveProject] 🎭 Preserved isMaskShape=${obj.isMaskShape} for object ${obj.id}`);
        }
        if (obj.isVisible !== undefined) {
            cleanObj.isVisible = obj.isVisible;
            console.log(`[SaveProject] 🎭 Preserved isVisible=${obj.isVisible} for object ${obj.id}`);
        }

        // Additional image properties
        if (obj.originalWidth !== undefined) cleanObj.originalWidth = obj.originalWidth;
        if (obj.originalHeight !== undefined) cleanObj.originalHeight = obj.originalHeight;
        if (obj.templateId !== undefined) cleanObj.templateId = obj.templateId;
        if (obj.generationId !== undefined) cleanObj.generationId = obj.generationId;
        if (obj.isFromGeneration !== undefined) cleanObj.isFromGeneration = obj.isFromGeneration;
    }

    // Copy shape-specific properties
    if (obj.type === 'shape' || obj.objectType || obj.type === 'image') {
        console.log(`[SaveProject] 🎨 SHAPE DEBUG - Object ${obj.id}:`, obj.type, obj.imageUrl || obj.text);
        console.log(`[SaveProject] 🎨 SHAPE PROPERTIES:`, {
            fill: obj.fill,
            stroke: obj.stroke,
            strokeWidth: obj.strokeWidth,
            svgColor: obj.svgColor,
            svgGradient: obj.svgGradient,
            gradientFill: obj.gradientFill
        });

        // Shape/SVG color properties
        if (obj.fill !== undefined) cleanObj.fill = obj.fill;
        if (obj.stroke !== undefined) cleanObj.stroke = obj.stroke;
        if (obj.strokeWidth !== undefined) cleanObj.strokeWidth = obj.strokeWidth;
        if (obj.gradientFill !== undefined) cleanObj.gradientFill = obj.gradientFill;
        if (obj.svgColor !== undefined) cleanObj.svgColor = obj.svgColor;
        if (obj.svgGradient !== undefined) cleanObj.svgGradient = obj.svgGradient;

        // Shape type and dimensions
        if (obj.objectType) cleanObj.objectType = obj.objectType;
        if (obj.width !== undefined) cleanObj.width = obj.width;
        if (obj.height !== undefined) cleanObj.height = obj.height;
        if (obj.radius !== undefined) cleanObj.radius = obj.radius;
        if (obj.rx !== undefined) cleanObj.rx = obj.rx;
        if (obj.ry !== undefined) cleanObj.ry = obj.ry;

        // Shape positioning
        if (obj.originX !== undefined) cleanObj.originX = obj.originX;
        if (obj.originY !== undefined) cleanObj.originY = obj.originY;
        if (obj.left !== undefined) cleanObj.left = obj.left;
        if (obj.top !== undefined) cleanObj.top = obj.top;
    }

    // Copy text-specific properties
    if (obj.type === 'text') {
        if (obj.width !== undefined) cleanObj.width = obj.width;
        if (obj.height !== undefined) cleanObj.height = obj.height;
        if (obj.textAlign) cleanObj.textAlign = obj.textAlign;
        if (obj.gradient) cleanObj.gradient = obj.gradient;

        // Copy all effect properties
        const effectProps = ['warpCurve', 'warpOffset', 'warpHeight', 'warpBottom', 'warpTriangle', 'warpShiftCenter',
                            'circleDiameter', 'circleKerning', 'circleFlip', 'shadowMode', 'shadowColor', 'shadowOffsetX',
                            'shadowOffsetY', 'shadowBlur', 'blockShadowColor', 'blockShadowOpacity', 'blockShadowOffset',
                            'blockShadowAngle', 'blockShadowBlur', 'lineShadowColor', 'lineShadowDist', 'lineShadowAngle',
                            'lineShadowThickness', 'd3dPrimaryColor', 'd3dPrimaryOpacity', 'd3dOffset', 'd3dAngle', 'd3dBlur',
                            'd3dSecondaryColor', 'd3dSecondaryOpacity', 'd3dSecondaryWidth', 'd3dSecondaryOffsetX', 'd3dSecondaryOffsetY',
                            'strokeMode', 'strokeWidth', 'strokeColor', 'decorationMode', 'hLineWeight', 'hLineDist', 'hLineColor',
                            'hLineCoverage', 'ccDist', 'ccColor', 'ccFillDir', 'ccCoverage', 'oLineWeight', 'oLineDist', 'oLineColor',
                            'oCoverage', 'flcDist', 'flcColor', 'flcWeight', 'flcSpacing', 'flcDir', 'flcCoverage', 'gridPadding'];

        effectProps.forEach(prop => {
            if (obj[prop] !== undefined) {
                cleanObj[prop] = obj[prop];
            }
        });

        // 🔧 GRID DISTORT SAVE - Save Grid Distort data separately from Mesh Warp
        if (obj.effectMode === 'grid-distort') {
            console.log(`[LeftMenuGridDistortSave] 🔧 Saving Grid Distort data for text "${obj.text}"`);

            // Save Grid Distort specific properties
            if (obj.gridDistortCols !== undefined) cleanObj.gridDistortCols = obj.gridDistortCols;
            if (obj.gridDistortRows !== undefined) cleanObj.gridDistortRows = obj.gridDistortRows;
            if (obj.gridDistortPadding !== undefined) cleanObj.gridDistortPadding = obj.gridDistortPadding;
            if (obj.gridDistortIntensity !== undefined) cleanObj.gridDistortIntensity = obj.gridDistortIntensity;
            if (obj.gridDistortVerticalOnly !== undefined) cleanObj.gridDistortVerticalOnly = obj.gridDistortVerticalOnly;

            // Save the complete Grid Distort object with control points
            if (obj.gridDistort) {
                cleanObj.gridDistortData = {
                    gridCols: obj.gridDistort.gridCols || 3,
                    gridRows: obj.gridDistort.gridRows || 2,
                    gridPadding: obj.gridDistort.gridPadding || 120,
                    intensity: obj.gridDistort.intensity || 1,
                    showGrid: obj.gridDistort.showGrid || false,
                    lastText: obj.gridDistort.lastText || obj.text,
                    lastFontSize: obj.gridDistort.lastFontSize || obj.fontSize,
                    verticalOnly: obj.gridDistort.verticalOnly || false,

                    // Critical: Save the control points that define the distortion
                    controlPoints: obj.gridDistort.controlPoints ?
                        obj.gridDistort.controlPoints.map(row =>
                            row.map(point => ({ x: point.x, y: point.y }))
                        ) : [],

                    // Critical: Save the relative control points for preserving distortion
                    relativeControlPoints: obj.gridDistort.relativeControlPoints ?
                        obj.gridDistort.relativeControlPoints.map(row =>
                            row.map(point => ({ x: point.x, y: point.y }))
                        ) : [],

                    // Save grid bounds for proper restoration
                    gridBounds: obj.gridDistort.gridBounds ? {
                        width: obj.gridDistort.gridBounds.width,
                        height: obj.gridDistort.gridBounds.height,
                        padding: obj.gridDistort.gridBounds.padding
                    } : null
                };

                console.log(`[LeftMenuGridDistortSave] 🔧 ✅ Successfully created gridDistortData:`, {
                    controlPointsCount: cleanObj.gridDistortData.controlPoints.length,
                    relativeControlPointsCount: cleanObj.gridDistortData.relativeControlPoints.length,
                    hasGridBounds: !!cleanObj.gridDistortData.gridBounds,
                    gridCols: cleanObj.gridDistortData.gridCols,
                    gridRows: cleanObj.gridDistortData.gridRows
                });
            } else {
                console.log(`[LeftMenuGridDistortSave] 🔧 ❌ No gridDistort object found for text "${obj.text}"`);
            }

            // Do NOT save meshWarp data for Grid Distort objects
            console.log(`[LeftMenuGridDistortSave] 🔧 Skipping meshWarp data for Grid Distort object`);

        } else if (obj.effectMode === 'mesh') {
            // Handle Mesh Warp properties ONLY for mesh effect mode
            console.log(`[LeftMenuMeshWarpSave] 🔧 Saving Mesh Warp data for text "${obj.text}"`);

            if (obj.meshWarp) {
                cleanObj.meshWarp = {
                    controlPoints: obj.meshWarp.controlPoints ? obj.meshWarp.controlPoints.map(p => ({ ...p })) : [],
                    initialControlPoints: obj.meshWarp.initialControlPoints ? obj.meshWarp.initialControlPoints.map(p => ({ ...p })) : [],
                    relativeControlPoints: obj.meshWarp.relativeControlPoints ? obj.meshWarp.relativeControlPoints.map(p => ({ ...p })) : [],
                    hasCustomDistortion: obj.meshWarp.hasCustomDistortion || false,
                    showGrid: obj.meshWarp.showGrid !== undefined ? obj.meshWarp.showGrid : true,
                    gridRect: obj.meshWarp.gridRect ? { ...obj.meshWarp.gridRect } : null,
                    initialized: obj.meshWarp.initialized || false
                };
                console.log(`[LeftMenuMeshWarpSave] 🔧 Saved Mesh Warp data:`, {
                    effectMode: obj.effectMode,
                    controlPointsCount: cleanObj.meshWarp.controlPoints.length,
                    hasCustomDistortion: cleanObj.meshWarp.hasCustomDistortion
                });
            } else {
                console.log(`[LeftMenuMeshWarpSave] 🔧 No meshWarp object found for text "${obj.text}"`);
            }

            // Do NOT save gridDistortData for Mesh Warp objects
            console.log(`[LeftMenuMeshWarpSave] 🔧 Skipping gridDistortData for Mesh Warp object`);

        } else {
            // For other effect modes (none, curve, skew, etc.), don't save either effect data
            console.log(`[LeftMenuCleanObject] Text "${obj.text}" has effectMode "${obj.effectMode}" - not saving Grid Distort or Mesh Warp data`);
        }

        // Handle other effect properties
        if (obj.curveAmount !== undefined) cleanObj.curveAmount = obj.curveAmount;
        if (obj.curveKerning !== undefined) cleanObj.curveKerning = obj.curveKerning;
        if (obj.curveFlip !== undefined) cleanObj.curveFlip = obj.curveFlip;
        if (obj.diameter !== undefined) cleanObj.diameter = obj.diameter;
        if (obj.kerning !== undefined) cleanObj.kerning = obj.kerning;
        if (obj.flip !== undefined) cleanObj.flip = obj.flip;
    }

    // Remove any circular references or handlers
    delete cleanObj._meshWarpHandler;
    delete cleanObj.isSelected;
    delete cleanObj.image; // Remove actual Image object
    delete cleanObj.maskShape; // Remove runtime mask shape reference (but keep maskShapeId)

    return cleanObj;
}

// Left Menu and Sidebar Functionality
document.addEventListener('DOMContentLoaded', () => {
    // Get all menu items and sidebars
    const menuItems = document.querySelectorAll('.left-menu-item');
    const sidebars = document.querySelectorAll('.left-sidebar');
    const closeButtons = document.querySelectorAll('.left-sidebar-close');

    // Function to close all sidebars
    const closeAllSidebars = () => {
        console.log('🎨 closeAllSidebars called, isOpeningForRestyle:', window.isOpeningForRestyle);

        // If we're opening for restyle, completely skip closing sidebars
        if (window.isOpeningForRestyle) {
            console.log('🎨 closeAllSidebars - SKIPPING because opening for restyle');
            return;
        }

        console.log('🎨 closeAllSidebars - Call stack:', new Error().stack);

        // Check if AI Generator sidebar is being closed and reset it to normal mode
        const aiGeneratorSidebar = document.getElementById('ai-generator-sidebar');
        console.log('🎨 closeAllSidebars - AI Generator state:', {
            exists: !!aiGeneratorSidebar,
            hasActive: aiGeneratorSidebar?.classList.contains('active'),
            isOpeningForRestyle: window.isOpeningForRestyle
        });

        if (aiGeneratorSidebar && aiGeneratorSidebar.classList.contains('active')) {
            console.log('🎨 closeAllSidebars - Resetting AI Generator to normal mode');
            // Reset AI Generator to normal mode when closing
            if (window.resetAIGeneratorToNormalMode) {
                window.resetAIGeneratorToNormalMode();
            }
        }

        console.log('🎨 closeAllSidebars - Removing active classes from all sidebars and menu items');
        sidebars.forEach(sidebar => {
            sidebar.classList.remove('active');
        });
        menuItems.forEach(item => {
            item.classList.remove('active');
        });
        console.log('🎨 closeAllSidebars completed');
    };

    // Make closeAllSidebars globally accessible
    window.closeAllSidebars = closeAllSidebars;

    // Add click event to menu items
    menuItems.forEach(item => {
        item.addEventListener('click', () => {
            const sidebarId = item.getAttribute('data-sidebar');
            const sidebar = document.getElementById(sidebarId);

            // If the sidebar is already active, close it
            if (sidebar.classList.contains('active')) {
                closeAllSidebars();
            } else {
                // Close all sidebars first
                closeAllSidebars();

                // Open the clicked sidebar
                sidebar.classList.add('active');
                item.classList.add('active');
            }
        });
    });

    // Add click event to close buttons
    closeButtons.forEach(button => {
        button.addEventListener('click', closeAllSidebars);
    });

    // Close sidebars when clicking outside
    document.addEventListener('click', (event) => {
        // Check if the click is outside the menu and sidebars
        const isOutsideMenu = !event.target.closest('.left-menu');
        const isOutsideSidebar = !event.target.closest('.left-sidebar');

        if (isOutsideMenu && isOutsideSidebar) {
            closeAllSidebars();
        }
    });

    // Handle menu item actions
    const menuActionItems = document.querySelectorAll('.menu-items .menu-item');
    console.log('[LeftMenu] 🔍 Found menu action items:', menuActionItems.length);

    menuActionItems.forEach((item, index) => {
        console.log(`[LeftMenu] 🔍 Menu item ${index}: "${item.textContent.trim()}"`);

        item.addEventListener('click', (event) => {
            console.log('[LeftMenu] 🚨 CLICK DETECTED ON MENU ITEM!');
            const action = item.textContent.trim();
            console.log(`[LeftMenu] 🎯 MENU ITEM CLICKED: "${action}"`);
            console.log(`[LeftMenu] 🎯 EVENT DETAILS:`, event);
            console.log(`[LeftMenu] 🎯 ITEM ELEMENT:`, item);

            // Prevent event bubbling to avoid sidebar closing
            event.stopPropagation();

            // Check for dynamic save button text (starts with Save and contains project name)
            if (action.startsWith('Save ') && action.includes('"')) {
                console.log('[LeftMenu] 💾 CALLING handleSaveProject() for existing project');
                // Prevent sidebar from closing during save
                event.preventDefault();
                // Trigger save project functionality
                handleSaveProject();
                return;
            }

            switch (action) {
                case 'New Project':
                    console.log('[LeftMenu] ➡️ Redirecting to new project');
                    // Redirect to design editor for new project
                    window.location.href = '/design-editor.html';
                    break;
                case 'My Projects':
                    console.log('[LeftMenu] ➡️ Redirecting to my projects');
                    // Redirect to my projects page
                    window.location.href = '/my-projects.html';
                    break;
                case 'Save Project':
                case '💾 Save Project':
                    console.log('[LeftMenu] 💾 CALLING handleSaveProject()');
                    // Prevent sidebar from closing during save
                    event.preventDefault();
                    // Trigger save project functionality
                    handleSaveProject();
                    break;
                case '📋 Save As New Project':
                    console.log('[LeftMenu] 📋 CALLING handleSaveProject() for Save As');
                    // Prevent sidebar from closing during save
                    event.preventDefault();
                    // Clear any existing project info to force "Save As"
                    window.currentProjectId = null;
                    window.currentProjectTitle = null;
                    window.currentProjectFolderId = null;
                    // Trigger save project functionality
                    handleSaveProject();
                    break;
                case 'Duplicate Project':
                    console.log('[LeftMenu] 📋 Calling handleDuplicateProject()');
                    // Trigger duplicate project functionality
                    handleDuplicateProject();
                    break;
                default:
                    console.log(`[LeftMenu] ❓ Unknown action: "${action}"`);
            }
        });
    });

    // Load text styles when text sidebar is opened
    const textSidebar = document.getElementById('text-sidebar');
    if (textSidebar) {
        const textMenuItem = document.querySelector('[data-sidebar="text-sidebar"]');
        if (textMenuItem) {
            textMenuItem.addEventListener('click', loadTextStyles);
        }
    }

    // Load text styles function
    async function loadTextStyles() {
        const textStylesGrid = document.getElementById('text-styles-grid');
        if (!textStylesGrid) return;

        // Show loading message
        textStylesGrid.innerHTML = '<div class="loading-message">Loading text styles...</div>';

        try {
            const response = await fetch('/api/text-styles/library', { credentials: 'include' });
            if (!response.ok) {
                throw new Error(`Failed to load text styles: ${response.statusText}`);
            }
            const textStyles = await response.json();

            textStylesGrid.innerHTML = '';

            if (textStyles.length === 0) {
                textStylesGrid.innerHTML = '<div class="no-styles-message">No text styles in library yet.</div>';
                return;
            }

            textStyles.forEach(textStyle => {
                const styleElement = document.createElement('div');
                styleElement.className = 'text-style-item';
                styleElement.innerHTML = `
                    <img src="${textStyle.previewImageUrl}" alt="${textStyle.name}" class="text-style-thumbnail" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="text-style-fallback" style="display:none;">No Preview</div>
                    <div class="text-style-name">${textStyle.name}</div>
                `;

                styleElement.addEventListener('click', () => loadTextStyleToCanvas(textStyle));
                textStylesGrid.appendChild(styleElement);
            });

        } catch (error) {
            console.error('Error loading text styles:', error);
            textStylesGrid.innerHTML = '<div class="error-message">Error loading text styles. Please try again.</div>';
        }
    }

    // Load text style to canvas function
    async function loadTextStyleToCanvas(textStyle) {
        try {
            console.log('[LoadTextStyle] Loading text style:', textStyle.name);
            console.log('[LoadTextStyle] Text style data:', textStyle);

            if (!textStyle.canvasObjects || textStyle.canvasObjects.length === 0) {
                console.warn('[LoadTextStyle] No canvas objects in text style');
                if (window.showToast) {
                    window.showToast('No objects found in text style', 'warning');
                }
                return;
            }

            // Get current canvas center for positioning
            const canvasCenter = {
                x: window.canvas ? window.canvas.width / 2 : 1024,
                y: window.canvas ? window.canvas.height / 2 : 1024
            };

            console.log('[LoadTextStyle] Canvas center:', canvasCenter);

            // Calculate offset to center the text style
            const styleCenter = {
                x: textStyle.artboard.width / 2,
                y: textStyle.artboard.height / 2
            };

            const offset = {
                x: canvasCenter.x - styleCenter.x,
                y: canvasCenter.y - styleCenter.y
            };

            console.log('[LoadTextStyle] Style center:', styleCenter);
            console.log('[LoadTextStyle] Offset:', offset);

            // Process each object from the text style
            const objectPromises = textStyle.canvasObjects.map(async (obj) => {
                console.log('[LoadTextStyle] Processing object:', obj.type, obj.text || obj.imageUrl);

                const newObj = JSON.parse(JSON.stringify(obj)); // Deep copy

                // Position relative to canvas center
                newObj.x = obj.x + offset.x;
                newObj.y = obj.y + offset.y;

                // Assign new ID - access the global nextId variable properly
                if (typeof window.nextId !== 'undefined') {
                    newObj.id = window.nextId++;
                } else {
                    newObj.id = Date.now() + Math.random();
                }

                // Ensure object is not selected initially
                newObj.isSelected = false;

                // Handle different object types
                if (newObj.type === 'text') {
                    console.log('[LoadTextStyle] Adding text object:', newObj.text);

                    // Ensure all required properties exist with proper defaults
                    const textDefaults = {
                        id: newObj.id,
                        type: 'text',
                        text: newObj.text || "TEXT",
                        x: newObj.x || 0,
                        y: newObj.y || 0,
                        color: newObj.color || "#3b82f6",
                        gradient: newObj.gradient || null,
                        fontFamily: newObj.fontFamily || "Poppins",
                        fontSize: newObj.fontSize || 150,
                        bold: newObj.bold !== undefined ? newObj.bold : true,
                        italic: newObj.italic !== undefined ? newObj.italic : false,
                        rotation: newObj.rotation || 0,
                        letterSpacing: newObj.letterSpacing || 0,
                        opacity: newObj.opacity !== undefined ? newObj.opacity : 100,
                        isSelected: false,
                        effectMode: newObj.effectMode || 'normal',
                        decorationMode: newObj.decorationMode || 'noDecoration',
                        strokeMode: newObj.strokeMode || 'noStroke',
                        strokeOpacity: newObj.strokeOpacity !== undefined ? newObj.strokeOpacity : 100,
                        shadowMode: newObj.shadowMode || 'noShadow',
                        skewX: newObj.skewX || 0,
                        skewY: newObj.skewY || 0,
                        scale: newObj.scale || 1.0,
                        // Copy all other properties from newObj
                        ...newObj
                    };

                    // Ensure numeric properties are numbers
                    const numericProps = ['x', 'y', 'fontSize', 'rotation', 'scale', 'opacity', 'letterSpacing', 'skewX', 'skewY'];
                    numericProps.forEach(prop => {
                        if (textDefaults[prop] !== undefined) {
                            const value = parseFloat(textDefaults[prop]);
                            textDefaults[prop] = isNaN(value) ? 0 : value;
                        }
                    });

                    console.log('[LoadTextStyle] Text object with defaults:', textDefaults);

                    // Special handling for mesh warp restoration
                    if (textDefaults.effectMode === 'mesh' && textDefaults.meshWarp && textDefaults.meshWarp.initialized) {
                        console.log('[LoadTextStyle] Restoring mesh warp distortion:', textDefaults.meshWarp);

                        // Ensure mesh warp data is properly structured
                        if (!textDefaults.meshWarp.controlPoints) {
                            textDefaults.meshWarp.controlPoints = [];
                        }
                        if (!textDefaults.meshWarp.initialControlPoints) {
                            textDefaults.meshWarp.initialControlPoints = [];
                        }
                        if (!textDefaults.meshWarp.relativeControlPoints) {
                            textDefaults.meshWarp.relativeControlPoints = [];
                        }

                        // Mark that this mesh warp has custom distortion to preserve it
                        textDefaults.meshWarp.hasCustomDistortion = true;
                        textDefaults.meshWarp.showGrid = false; // Hide grid by default when loading

                        console.log('[LoadTextStyle] Mesh warp data prepared for restoration');
                    }

                    // Text objects can be added directly
                    if (window.canvasObjects && Array.isArray(window.canvasObjects)) {
                        window.canvasObjects.push(textDefaults);
                        console.log('[LoadTextStyle] Text object added to canvas');

                        // If this text has mesh warp with custom distortion, create handler immediately
                        if (textDefaults.effectMode === 'mesh' && textDefaults.meshWarp &&
                            textDefaults.meshWarp.hasCustomDistortion && textDefaults.meshWarp.relativeControlPoints.length > 0) {

                            console.log('[LoadTextStyle] Creating mesh warp handler immediately for text with custom distortion');

                            // Create mesh warp handler immediately
                            if (typeof window.MeshWarpHandler !== 'undefined') {
                                try {
                                    // Prevent the mesh handler constructor from calling update()
                                    window.skipMeshUpdate = true;

                                    const meshHandler = new window.MeshWarpHandler(
                                        document.getElementById('demo'),
                                        textDefaults
                                    );
                                    textDefaults._meshWarpHandler = meshHandler;

                                    // Set as active mesh warp handler
                                    window.activeMeshWarpHandler = meshHandler;

                                    // Re-enable updates
                                    window.skipMeshUpdate = false;

                                    console.log('[LoadTextStyle] Mesh warp handler created and assigned to text object');
                                    console.log('[LoadTextStyle] Handler control points:', meshHandler.controlPoints.length);
                                    console.log('[LoadTextStyle] Handler has custom distortion:', meshHandler.hasCustomDistortion);

                                    // Select the text object to activate the mesh warp handler
                                    const objectIndex = window.canvasObjects.length - 1;
                                    if (typeof window.selectObject === 'function') {
                                        window.selectObject(objectIndex);
                                        console.log('[LoadTextStyle] Selected text object to activate mesh warp handler');
                                    } else if (typeof window.selectedObjectIndex !== 'undefined') {
                                        window.selectedObjectIndex = objectIndex;
                                        console.log('[LoadTextStyle] Set selectedObjectIndex to activate mesh warp handler');
                                    }
                                } catch (handlerError) {
                                    console.error('[LoadTextStyle] Error creating mesh warp handler:', handlerError);
                                    // Make sure to re-enable updates even if there's an error
                                    window.skipMeshUpdate = false;
                                }
                            } else {
                                console.warn('[LoadTextStyle] MeshWarpHandler class not available');
                            }
                        }
                    }
                } else if (newObj.type === 'image') {
                    console.log('[LoadTextStyle] Loading image object:', newObj.imageUrl);
                    // For images, we need to reload the image object
                    return new Promise((resolve) => {
                        const img = new Image();
                        img.onload = () => {
                            newObj.image = img;
                            newObj.originalWidth = img.width;
                            newObj.originalHeight = img.height;

                            if (window.canvasObjects && Array.isArray(window.canvasObjects)) {
                                window.canvasObjects.push(newObj);
                                console.log('[LoadTextStyle] Image object added to canvas');
                            }
                            resolve();
                        };
                        img.onerror = () => {
                            console.error('[LoadTextStyle] Failed to load image:', newObj.imageUrl);
                            resolve(); // Continue even if image fails
                        };
                        img.src = newObj.imageUrl;
                    });
                }
            });

            // Wait for all objects to be processed (especially images)
            await Promise.all(objectPromises.filter(p => p instanceof Promise));

            console.log('[LoadTextStyle] All objects processed, updating canvas');
            console.log('[LoadTextStyle] Canvas objects count:', window.canvasObjects ? window.canvasObjects.length : 'undefined');

            // Sync global references
            if (typeof window.syncGlobalReferences === 'function') {
                window.syncGlobalReferences();
            }

            // Auto-activate mesh warp handlers for immediate visual feedback (same as templates)
            setTimeout(() => {
                let meshTextFound = false;
                window.canvasObjects.forEach((obj, index) => {
                    if (obj.type === 'text' && obj.effectMode === 'mesh' && obj._meshWarpHandler && !meshTextFound) {
                        console.log('[LoadTextStyle] Auto-activating mesh warp for immediate display:', obj.text);

                        // Temporarily select the object to activate mesh warp
                        const previousSelection = window.selectedObjectIndex;
                        window.selectedObjectIndex = index;
                        obj.isSelected = true;

                        // Activate the mesh warp handler
                        window.activeMeshWarpHandler = obj._meshWarpHandler;
                        window.activeMeshWarpHandler.selectedTextObject = obj;

                        // Force a redraw to show the distortion
                        if (typeof window.update === 'function') {
                            window.update();
                        }

                        // After a brief moment, keep the object selected for editing (unlike templates)
                        setTimeout(() => {
                            // Keep the mesh object selected for immediate editing
                            console.log('[LoadTextStyle] Keeping mesh text selected for editing');

                            // Show the mesh grid for editing
                            if (obj._meshWarpHandler) {
                                obj._meshWarpHandler.showGrid = true;
                                console.log('[LoadTextStyle] Enabled mesh grid for editing');
                            }

                            // Update UI to reflect the selected object
                            if (typeof window.updateUIFromSelectedObject === 'function') {
                                window.updateUIFromSelectedObject();
                            }

                            // Force another update to show the grid
                            if (typeof window.update === 'function') {
                                window.update();
                            }
                        }, 100); // Brief delay to ensure mesh warp is activated

                        meshTextFound = true; // Only auto-activate the first mesh text found
                    }
                });

                if (!meshTextFound) {
                    console.log('[LoadTextStyle] No mesh warp text objects found for auto-activation');
                }
            }, 200); // Small delay to ensure everything is fully loaded

            // Update canvas if update function exists
            if (window.update) {
                console.log('[LoadTextStyle] Calling update function');
                window.update();
            } else {
                console.warn('[LoadTextStyle] Update function not found');
            }

            // Show success message
            if (window.showToast) {
                window.showToast(`Text style "${textStyle.name}" added to canvas`, 'success');
            } else {
                console.log(`Text style "${textStyle.name}" added to canvas`);
            }

            // Close the sidebar after adding
            closeAllSidebars();

        } catch (error) {
            console.error('[LoadTextStyle] Error loading text style to canvas:', error);
            if (window.showToast) {
                window.showToast(`Error loading text style: ${error.message}`, 'error');
            } else {
                alert(`Error loading text style: ${error.message}`);
            }
        }
    }

    // Handle element item clicks - REMOVED
    // Element clicks are now handled by elements-accordion.js
    // This prevents conflicts with the proper shape loading functionality

    // Image item clicks are now handled by the dynamic images-loader.js
    // This ensures compatibility with dynamically loaded stock images

    // Initialize AI Generator when sidebar is opened
    const aiGeneratorSidebar = document.getElementById('ai-generator-sidebar');
    if (aiGeneratorSidebar) {
        const aiGeneratorMenuItem = document.querySelector('[data-sidebar="ai-generator-sidebar"]');
        if (aiGeneratorMenuItem) {
            aiGeneratorMenuItem.addEventListener('click', initializeAIGeneratorSidebar);
        }
    }

    // Initialize AI Generator Sidebar
    function initializeAIGeneratorSidebar() {
        console.log('[AIGenerator] Initializing AI Generator sidebar');

        // Load templates if not already loaded
        if (!document.querySelector('.ai-template-item')) {
            loadAITemplates();
        }

        // Initialize event listeners if not already done
        if (!window.aiGeneratorInitialized) {
            setupAIGeneratorEventListeners();
            window.aiGeneratorInitialized = true;
        }
    }

    // Make initializeAIGeneratorSidebar globally accessible
    window.initializeAIGeneratorSidebar = initializeAIGeneratorSidebar;

    // Load AI templates
    async function loadAITemplates() {
        const aiTemplateGrid = document.getElementById('aiTemplateGrid');
        if (!aiTemplateGrid) return;

        try {
            const response = await fetch('/api/templates');
            if (!response.ok) {
                throw new Error('Failed to load templates');
            }
            const templates = await response.json();

            if (templates.length === 0) {
                aiTemplateGrid.innerHTML = '<div class="empty-message">No templates available</div>';
                return;
            }

            aiTemplateGrid.innerHTML = templates.map(template => {
                const hasThumbnail = template.thumbnailUrl && template.thumbnailUrl.trim() !== '';
                return `
                    <div class="template-item ai-template-item" data-template-id="${template._id}" onclick="selectAITemplate(this)">
                        ${hasThumbnail ?
                            `<img src="${template.thumbnailUrl}" alt="${template.name}" class="template-preview">` :
                            `<div class="template-preview" style="background: #f3f4f6; display: flex; align-items: center; justify-content: center; color: #6b7280; font-size: 0.7rem;">No Preview</div>`
                        }
                        <p class="template-name">${template.name}</p>
                    </div>
                `;
            }).join('');

            // Initialize custom scrollbar
            initializeAICustomScrollbar();

        } catch (error) {
            console.error('Error loading AI templates:', error);
            if (aiTemplateGrid) {
                aiTemplateGrid.innerHTML = '<div class="error-message">Error loading templates</div>';
            }
        }
    }

    // Initialize custom scrollbar for AI templates
    function initializeAICustomScrollbar() {
        const aiTemplateGrid = document.getElementById('aiTemplateGrid');
        const aiCustomScrollbar = document.getElementById('aiCustomScrollbar');
        const aiCustomScrollbarThumb = document.getElementById('aiCustomScrollbarThumb');

        if (!aiTemplateGrid || !aiCustomScrollbar || !aiCustomScrollbarThumb) {
            return;
        }

        function updateScrollbar() {
            const scrollHeight = aiTemplateGrid.scrollHeight;
            const clientHeight = aiTemplateGrid.clientHeight;
            const scrollTop = aiTemplateGrid.scrollTop;

            if (scrollHeight <= clientHeight) {
                aiCustomScrollbar.classList.add('hidden');
                return;
            }

            aiCustomScrollbar.classList.remove('hidden');
            const thumbHeight = Math.max(20, (clientHeight / scrollHeight) * clientHeight);
            const thumbTop = (scrollTop / (scrollHeight - clientHeight)) * (clientHeight - thumbHeight);

            aiCustomScrollbarThumb.style.height = thumbHeight + 'px';
            aiCustomScrollbarThumb.style.top = thumbTop + 'px';
        }

        aiTemplateGrid.addEventListener('scroll', updateScrollbar);
        updateScrollbar();
    }

    // Setup AI Generator event listeners
    function setupAIGeneratorEventListeners() {
        const aiGenerateBtn = document.getElementById('aiGenerateBtn');
        const aiObjectInput = document.getElementById('aiObjectInput');
        const aiColorPaletteSelector = document.getElementById('aiColorPaletteSelector');

        let selectedTemplate = null;
        let selectedPalette = null;

        // Handle palette selection
        if (aiColorPaletteSelector) {
            aiColorPaletteSelector.addEventListener('paletteChange', (event) => {
                selectedPalette = event.detail.palette;
                console.log('[AIGenerator] 🎨 Palette selected:', {
                    id: selectedPalette?.id,
                    name: selectedPalette?.name,
                    description: selectedPalette?.description,
                    fullObject: selectedPalette
                });
            });
        }

        // Handle generate button click
        if (aiGenerateBtn) {
            aiGenerateBtn.addEventListener('click', async () => {
                const objectText = aiObjectInput?.value?.trim();

                if (!objectText) {
                    if (window.showToast) {
                        window.showToast('Please enter an object to generate', 'warning');
                    } else {
                        alert('Please enter an object to generate');
                    }
                    return;
                }

                try {
                    // Show loading state
                    const aiLoadingContainer = document.getElementById('aiLoadingContainer');
                    if (aiLoadingContainer) {
                        aiLoadingContainer.style.display = 'block';
                    }
                    aiGenerateBtn.disabled = true;
                    aiGenerateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';

                    // Get selected template
                    const selectedTemplateElement = document.querySelector('.ai-template-item.selected');
                    selectedTemplate = selectedTemplateElement ? selectedTemplateElement.dataset.templateId : null;

                    // Generate image
                    const generationResult = await generateAIImage(objectText, selectedTemplate, selectedPalette);

                    if (generationResult) {
                        // Update loading message for background removal
                        aiGenerateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Removing Background...';

                        if (window.showToast) {
                            window.showToast('Image generated! Removing background...', 'info');
                        }

                        // Automatically remove background before adding to canvas
                        const finalImageUrl = await removeBackgroundFromGeneration(generationResult.imageUrl, generationResult.generationId);

                        // Check if we're in Restyle mode
                        if (window.isRestyleMode) {
                            // Replace the currently selected image instead of adding new one
                            await replaceSelectedImageWithGenerated(finalImageUrl);

                            // Update admin fields with the new generation data for future regeneration
                            updateAdminFieldsAfterRestyle(generationResult, selectedTemplate, selectedPalette);

                            if (window.showToast) {
                                window.showToast('Image restyled successfully!', 'success');
                            }
                        } else {
                            // Normal mode: Add image to canvas with background removed
                            await addGeneratedImageToCanvas(finalImageUrl);

                            if (window.showToast) {
                                window.showToast('Image generated and added to canvas!', 'success');
                            }
                        }

                        // Close the sidebar after successful generation
                        closeAllSidebars();
                    }

                } catch (error) {
                    console.error('Error generating image:', error);
                    if (window.showToast) {
                        window.showToast('Failed to generate image. Please try again.', 'error');
                    } else {
                        alert('Failed to generate image. Please try again.');
                    }
                } finally {
                    // Hide loading state
                    const aiLoadingContainer = document.getElementById('aiLoadingContainer');
                    if (aiLoadingContainer) {
                        aiLoadingContainer.style.display = 'none';
                    }
                    aiGenerateBtn.disabled = false;
                    aiGenerateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate Image';
                }
            });
        }
    }

    // Save Project functionality - EXACT COPY of Save Template logic
    async function handleSaveProject() {
        console.log('[SaveProject] 🚀 EXACT TEMPLATE COPY - Starting save process...');
        console.log('[SaveProject] 🔍 DEBUG - Current window.artboard:', window.artboard);
        console.log('[SaveProject] 🔍 DEBUG - typeof window.artboard:', typeof window.artboard);
        console.log('[SaveProject] 🔍 DEBUG - window.artboard === null:', window.artboard === null);
        console.log('[SaveProject] 🔍 DEBUG - window.artboard === undefined:', window.artboard === undefined);
        console.log('[SaveProject] 🔍 DEBUG - !window.artboard:', !window.artboard);
        console.log('[SaveProject] 🔍 DEBUG - window.canvasBackgroundColor:', window.canvasBackgroundColor);
        console.log('[SaveProject] 🔍 DEBUG - window.scale:', window.scale);
        console.log('[SaveProject] 🔍 DEBUG - window.offsetX:', window.offsetX);
        console.log('[SaveProject] 🔍 DEBUG - window.offsetY:', window.offsetY);
        console.log('[SaveProject] 🔍 DEBUG - window.canvasObjects length:', window.canvasObjects?.length);

        // DEBUG: Log all current canvas objects
        console.log('[SaveProject] 🔍 DEBUG - Current canvas objects:');
        window.canvasObjects.forEach((obj, index) => {
            console.log(`[SaveProject] 🔍 DEBUG - Object ${index}:`, {
                type: obj.type,
                id: obj.id,
                text: obj.text || obj.imageUrl,
                svgColor: obj.svgColor,
                x: obj.x,
                y: obj.y,
                width: obj.width,
                height: obj.height,
                scale: obj.scale,
                originalWidth: obj.originalWidth,
                originalHeight: obj.originalHeight,
                imageUrl: obj.imageUrl
            });
        });

        // Check if artboard exists (same as template)
        if (!window.artboard) {
            console.log('[SaveProject] ❌ No artboard found - showing error message');
            const msg = 'Cannot save project without an Artboard defined.';
            if (window.showToast) window.showToast(msg, 'warning');
            else alert(msg);
            return;
        }

        console.log('[SaveProject] ✅ Artboard found, proceeding with save...');
        console.log('[SaveProject] 🔍 Artboard details:', window.artboard);
        console.log('[SaveProject] 🔍 Artboard.x:', window.artboard.x);
        console.log('[SaveProject] 🔍 Artboard.y:', window.artboard.y);
        console.log('[SaveProject] 🔍 Artboard.width:', window.artboard.width);
        console.log('[SaveProject] 🔍 Artboard.height:', window.artboard.height);

        // 1. Get Admin Data (same as template)
        const adminData = {
            imageUrl: document.getElementById('adminImageUrl')?.value || '',
            model: document.getElementById('adminModel')?.value || '',
            prompt: document.getElementById('adminPrompt')?.value || '',
            palette: document.getElementById('adminPalette')?.value || ''
        };

        // 2. Generate Preview Image (EXACT COPY from Save Template)
        let previewDataUrl;
        try {
            console.log('[SaveProject] Generating preview image...');
            if (window.showToast) window.showToast('Generating preview...', 'info');

            // Small delay to ensure canvas is fully rendered
            await new Promise(resolve => setTimeout(resolve, 100));

            // Create export canvas for preview (EXACT same as Save Template)
            const exportCanvas = document.createElement('canvas');
            exportCanvas.width = window.artboard.width;
            exportCanvas.height = window.artboard.height;
            const exportCtx = exportCanvas.getContext('2d');

            exportCtx.save();
            // Clip to artboard dimensions
            exportCtx.beginPath();
            exportCtx.rect(0, 0, window.artboard.width, window.artboard.height);
            exportCtx.clip();
            // Translate context so drawing happens relative to artboard's top-left
            exportCtx.translate(-window.artboard.x, -window.artboard.y);

            // Fill with background color
            exportCtx.fillStyle = window.canvasBackgroundColor || '#ffffff';
            exportCtx.fillRect(window.artboard.x, window.artboard.y, window.artboard.width, window.artboard.height);

            // Draw all canvas objects that intersect with the artboard
            if (window.canvasObjects && Array.isArray(window.canvasObjects)) {
                window.canvasObjects.forEach(obj => {
                    // Skip drawing mask shapes that are being used as masks (they should be invisible)
                    if (obj.type === 'image' && obj.isMaskShape && obj.isVisible === false) {
                        console.log('[Preview] 🎭 Skipping drawing of mask shape in project preview:', obj.id);
                        return; // Skip this object
                    }

                    // Additional debug logging for mask shapes
                    if (obj.isMaskShape) {
                        console.log('[Preview] 🎭 MASK SHAPE DEBUG:', {
                            id: obj.id,
                            type: obj.type,
                            isMaskShape: obj.isMaskShape,
                            isVisible: obj.isVisible,
                            shouldSkip: obj.type === 'image' && obj.isMaskShape && obj.isVisible === false
                        });
                    }

                    if (obj.type === 'text') {
                        // Use the same text rendering function as the main canvas
                        if (typeof window.drawTextObject === 'function') {
                            window.drawTextObject(obj, exportCtx);
                        } else {
                            // Fallback text rendering
                            exportCtx.save();
                            exportCtx.globalAlpha = (obj.opacity || 100) / 100;
                            exportCtx.fillStyle = obj.color || '#000000';
                            exportCtx.font = `${obj.fontSize || 24}px ${obj.fontFamily || 'Arial'}`;
                            exportCtx.textAlign = obj.textAlign || 'left';
                            exportCtx.textBaseline = 'top';
                            if (obj.rotation) {
                                exportCtx.translate(obj.x, obj.y);
                                exportCtx.rotate((obj.rotation * Math.PI) / 180);
                                exportCtx.fillText(obj.text || '', 0, 0);
                            } else {
                                exportCtx.fillText(obj.text || '', obj.x, obj.y);
                            }
                            exportCtx.restore();
                        }
                    } else if (obj.type === 'image') {
                        // 🎭 MASKING SUPPORT: Check if this image is masked (same logic as main canvas)
                        if (obj.isMasked && obj.maskShape && obj.maskShape.image) {
                            console.log('[Preview] 🎭 Drawing masked image in project preview:', {
                                imageId: obj.id,
                                maskId: obj.maskShape.id,
                                imagePos: { x: obj.x, y: obj.y },
                                maskPos: { x: obj.maskShape.x, y: obj.maskShape.y }
                            });

                            try {
                                // Use the same masking logic as the main canvas (from design-editor.js line 9278+)
                                exportCtx.save();
                                exportCtx.globalAlpha = (obj.opacity || 100) / 100;

                                // Calculate scaled dimensions
                                const scaledWidth = obj.originalWidth * obj.scale;
                                const scaledHeight = obj.originalHeight * obj.scale;

                                // Create an offscreen canvas for the masking effect
                                const maskCanvas = document.createElement('canvas');
                                const maskScaledWidth = obj.maskShape.originalWidth * obj.maskShape.scale;
                                const maskScaledHeight = obj.maskShape.originalHeight * obj.maskShape.scale;

                                // Make the mask canvas large enough to contain both image and mask
                                const canvasWidth = Math.max(scaledWidth, maskScaledWidth) + 100;
                                const canvasHeight = Math.max(scaledHeight, maskScaledHeight) + 100;

                                maskCanvas.width = canvasWidth;
                                maskCanvas.height = canvasHeight;
                                const maskCtx = maskCanvas.getContext('2d');

                                console.log('[Preview] 🎭 Mask canvas setup:', {
                                    canvasSize: { width: canvasWidth, height: canvasHeight },
                                    centerPos: { x: canvasWidth / 2, y: canvasHeight / 2 },
                                    maskRelative: { x: obj.maskShape.x - obj.x, y: obj.maskShape.y - obj.y }
                                });

                                // Draw the mask shape first (this will be used as the clipping path)
                                maskCtx.save();
                                maskCtx.translate(canvasWidth / 2 + (obj.maskShape.x - obj.x), canvasHeight / 2 + (obj.maskShape.y - obj.y));
                                if (obj.maskShape.rotation) {
                                    maskCtx.rotate((obj.maskShape.rotation * Math.PI) / 180);
                                }
                                maskCtx.drawImage(obj.maskShape.image, -maskScaledWidth / 2, -maskScaledHeight / 2, maskScaledWidth, maskScaledHeight);
                                maskCtx.restore();

                                // Use the mask as a clipping path
                                maskCtx.globalCompositeOperation = 'source-in';

                                // Draw the main image
                                maskCtx.save();
                                maskCtx.translate(canvasWidth / 2, canvasHeight / 2);
                                if (obj.rotation) {
                                    maskCtx.rotate((obj.rotation * Math.PI) / 180);
                                }
                                maskCtx.drawImage(obj.image, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);
                                maskCtx.restore();

                                // Draw the masked result onto the main canvas
                                exportCtx.translate(obj.x, obj.y);
                                if (obj.rotation) {
                                    exportCtx.rotate((obj.rotation * Math.PI) / 180);
                                }
                                exportCtx.drawImage(maskCanvas, -canvasWidth / 2, -canvasHeight / 2);
                                exportCtx.restore();

                                console.log('[Preview] 🎭 Masked image drawn successfully in project preview');
                            } catch (e) {
                                console.error('[Preview] 🎭 Error drawing masked image in project preview:', e);
                                // Fallback to normal drawing
                                exportCtx.save();
                                exportCtx.globalAlpha = (obj.opacity || 100) / 100;
                                const scaledWidth = obj.originalWidth * obj.scale;
                                const scaledHeight = obj.originalHeight * obj.scale;
                                exportCtx.translate(obj.x, obj.y);
                                if (obj.rotation) {
                                    exportCtx.rotate((obj.rotation * Math.PI) / 180);
                                }
                                exportCtx.drawImage(obj.image, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);
                                exportCtx.restore();
                            }
                        } else {
                            // Draw regular image objects (including SVG shapes)
                            exportCtx.save();
                            exportCtx.globalAlpha = (obj.opacity || 100) / 100;

                            console.log('[SaveProject] 🖼️ IMAGE DEBUG - Object:', {
                                type: obj.type,
                                hasImage: !!obj.image,
                                imageUrl: obj.imageUrl,
                                svgColor: obj.svgColor,
                                x: obj.x,
                                y: obj.y,
                                scale: obj.scale,
                                originalWidth: obj.originalWidth,
                                originalHeight: obj.originalHeight
                            });

                            // Check if image is loaded
                            if (obj.image && obj.image.complete && obj.image.naturalWidth > 0) {
                                // Calculate scaled dimensions
                                const scaledWidth = obj.originalWidth * obj.scale;
                                const scaledHeight = obj.originalHeight * obj.scale;

                                // Draw the actual loaded image
                                if (obj.rotation) {
                                    exportCtx.translate(obj.x, obj.y);
                                    exportCtx.rotate((obj.rotation * Math.PI) / 180);
                                    exportCtx.drawImage(obj.image, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);
                                } else {
                                    exportCtx.drawImage(obj.image, obj.x - scaledWidth / 2, obj.y - scaledHeight / 2, scaledWidth, scaledHeight);
                                }
                            } else {
                                // Calculate fallback dimensions
                                const fallbackWidth = obj.originalWidth ? obj.originalWidth * obj.scale : 100;
                                const fallbackHeight = obj.originalHeight ? obj.originalHeight * obj.scale : 100;

                                // Fallback: Draw colored rectangle for SVG shapes or placeholder for images
                                if (obj.imageUrl && obj.imageUrl.includes('.svg') && obj.svgColor) {
                                    // SVG shape with color
                                    exportCtx.fillStyle = obj.svgColor;
                                    exportCtx.fillRect(obj.x - fallbackWidth / 2, obj.y - fallbackHeight / 2, fallbackWidth, fallbackHeight);
                                } else {
                                    // Regular image placeholder
                                    exportCtx.fillStyle = '#e5e7eb';
                                    exportCtx.fillRect(obj.x - fallbackWidth / 2, obj.y - fallbackHeight / 2, fallbackWidth, fallbackHeight);
                                    exportCtx.strokeStyle = '#9ca3af';
                                    exportCtx.strokeRect(obj.x - fallbackWidth / 2, obj.y - fallbackHeight / 2, fallbackWidth, fallbackHeight);

                                    // Add image icon
                                    exportCtx.fillStyle = '#6b7280';
                                    exportCtx.font = `${Math.max(16, fallbackWidth/4)}px Arial`;
                                    exportCtx.textAlign = 'center';
                                    exportCtx.textBaseline = 'middle';
                                    exportCtx.fillText('🖼️', obj.x, obj.y);
                                }
                            }
                            exportCtx.restore();
                        } // End of regular image drawing
                    } else if (obj.type === 'shape' || obj.objectType) {
                        // Use the same shape rendering function as the main canvas
                        if (typeof window.drawShapeObject === 'function') {
                            window.drawShapeObject(obj, exportCtx);
                        } else {
                            // Fallback shape rendering
                            exportCtx.save();
                            exportCtx.globalAlpha = (obj.opacity || 100) / 100;

                            // Set fill color
                            exportCtx.fillStyle = obj.fill || '#ffffff';

                            // Set stroke if present
                            if (obj.stroke && obj.strokeWidth > 0) {
                                exportCtx.strokeStyle = obj.stroke;
                                exportCtx.lineWidth = obj.strokeWidth;
                            }

                            // Draw based on shape type
                            const shapeType = obj.objectType || obj.type;
                            if (shapeType === 'circle') {
                                exportCtx.beginPath();
                                exportCtx.arc(obj.x + obj.width/2, obj.y + obj.height/2, Math.min(obj.width, obj.height)/2, 0, 2 * Math.PI);
                                exportCtx.fill();
                                if (obj.stroke && obj.strokeWidth > 0) exportCtx.stroke();
                            } else {
                                // Rectangle and other shapes
                                exportCtx.fillRect(obj.x, obj.y, obj.width, obj.height);
                                if (obj.stroke && obj.strokeWidth > 0) exportCtx.strokeRect(obj.x, obj.y, obj.width, obj.height);
                            }

                            exportCtx.restore();
                        }
                    }
                });
            }

            exportCtx.restore();

            // Convert to data URL
            previewDataUrl = exportCanvas.toDataURL('image/png');
            console.log('[SaveProject] Preview image generated, length:', previewDataUrl.length);
        } catch (error) {
            console.error('[SaveProject] Error generating preview:', error);
            const msg = `Error generating preview: ${error.message}`;
            if (window.showToast) window.showToast(msg, 'error');
            else alert(msg);
            return;
        }

        // 3. Upload Preview Image (same as template)
        let previewImageUrl;
        try {
            console.log('[SaveProject] Uploading preview image...');
            console.log('[SaveProject] Preview data URL length:', previewDataUrl.length);
            if (window.showToast) window.showToast('Uploading preview...', 'info');

            const blob = await (await fetch(previewDataUrl)).blob();
            console.log('[SaveProject] Blob created, size:', blob.size, 'type:', blob.type);
            const formData = new FormData();
            formData.append('image', blob, 'project_preview.png');
            console.log('[SaveProject] FormData created, sending request to /api/images/upload');

            const response = await fetch('/api/images/upload', {
                method: 'POST',
                body: formData
            });

            console.log('[SaveProject] Upload response status:', response.status);
            console.log('[SaveProject] Upload response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text();
                console.error('[SaveProject] Upload failed with status:', response.status);
                console.error('[SaveProject] Upload failed with response:', errorText);
                console.error('[SaveProject] Upload failed with headers:', Object.fromEntries(response.headers.entries()));
                throw new Error(`Preview upload failed: ${response.statusText} - ${errorText}`);
            }

            const uploadResult = await response.json();
            console.log('[SaveProject] 🔍 Upload response structure:', uploadResult);
            console.log('[SaveProject] 🔍 Available fields:', Object.keys(uploadResult));

            // Try different possible field names for the URL
            previewImageUrl = uploadResult.url || uploadResult.imageUrl || uploadResult.path || uploadResult.src;
            console.log('[SaveProject] Preview uploaded successfully:', previewImageUrl);

        } catch (e) {
            console.error('[SaveProject] Error uploading preview image:', e);
            const msg = `Error uploading preview image: ${e.message}`;
            if (window.showToast) window.showToast(msg, 'error');
            else alert(msg);
            return;
        }

        // 4. Prepare Canvas Objects Data (EXACT same as template)
        console.log('[SaveProject] ===== SAVING LAYER ORDER DEBUG =====');
        console.log('[SaveProject] Current canvasObjects array length:', window.canvasObjects.length);

        // 🎭 CRITICAL FIX: Remove duplicate objects before saving (SAME AS TEMPLATE)
        console.log('[SaveProject] 🎭 DUPLICATE CHECK - Checking for duplicate objects...');
        const uniqueObjects = [];
        const seenIds = new Set();

        window.canvasObjects.forEach((obj, index) => {
            console.log(`[SaveProject] Object ${index}: type=${obj.type}, text="${obj.text || obj.imageUrl || 'N/A'}", id=${obj.id}, isMasked=${obj.isMasked || false}, isMaskShape=${obj.isMaskShape || false}`);

            // Check for duplicates by ID and type
            const objectKey = `${obj.id}-${obj.type}-${obj.imageUrl}`;
            if (!seenIds.has(objectKey)) {
                seenIds.add(objectKey);
                uniqueObjects.push(obj);
                console.log(`[SaveProject] 🎭 ✅ Object ${index} added (unique)`);
            } else {
                console.warn(`[SaveProject] 🎭 ⚠️ Object ${index} is duplicate, skipping:`, {
                    id: obj.id,
                    type: obj.type,
                    imageUrl: obj.imageUrl?.substring(0, 50) + '...'
                });
            }
        });

        console.log(`[SaveProject] 🎭 DUPLICATE CHECK COMPLETE: ${window.canvasObjects.length} -> ${uniqueObjects.length} objects`);

        // Use unique objects for serialization
        const objectsToSerialize = uniqueObjects;

        const serializableObjects = objectsToSerialize.map((obj, index) => {
            const cleanObj = window.cleanObjectForSerialization(obj);
            cleanObj.layerOrder = index;
            cleanObj.zIndex = index;

            // 🎭 Enhanced mask debugging for Save Project
            if (obj.isMasked || obj.isMaskShape) {
                console.log(`[SaveProject] 🎭 MASK OBJECT ${index}:`, {
                    type: obj.type,
                    id: obj.id,
                    isMasked: obj.isMasked,
                    maskShapeId: obj.maskShapeId,
                    isMaskShape: obj.isMaskShape,
                    isVisible: obj.isVisible,
                    imageUrl: obj.imageUrl?.substring(0, 50) + '...'
                });
                console.log(`[SaveProject] 🎭 CLEANED MASK OBJECT ${index}:`, {
                    type: cleanObj.type,
                    id: cleanObj.id,
                    isMasked: cleanObj.isMasked,
                    maskShapeId: cleanObj.maskShapeId,
                    isMaskShape: cleanObj.isMaskShape,
                    isVisible: cleanObj.isVisible,
                    imageUrl: cleanObj.imageUrl?.substring(0, 50) + '...'
                });
            }

            console.log(`[SaveProject] Saving object ${index}: type=${obj.type}, text="${obj.text || obj.imageUrl || 'N/A'}", layerOrder=${index}, zIndex=${index}, isMasked=${obj.isMasked || false}`);
            return cleanObj;
        });

        console.log('[SaveProject] ===== SERIALIZABLE OBJECTS =====');
        serializableObjects.forEach((obj, index) => {
            console.log(`[SaveProject] Serialized ${index}: type=${obj.type}, text="${obj.text || obj.imageUrl || 'N/A'}", layerOrder=${obj.layerOrder}, zIndex=${obj.zIndex}, isMasked=${obj.isMasked || false}`);
        });

        // 🎭 FINAL VERIFICATION: Check masking properties in final data (SAME AS TEMPLATE)
        const finalMaskedImages = serializableObjects.filter(obj => obj.isMasked);
        const finalMaskShapes = serializableObjects.filter(obj => obj.isMaskShape);
        console.log('[SaveProject] 🎭 FINAL VERIFICATION - Masked images in final data:', finalMaskedImages.length);
        console.log('[SaveProject] 🎭 FINAL VERIFICATION - Mask shapes in final data:', finalMaskShapes.length);
        finalMaskedImages.forEach((img, index) => {
            console.log(`[SaveProject] 🎭 FINAL MASKED IMAGE ${index + 1}:`, {
                id: img.id,
                isMasked: img.isMasked,
                maskShapeId: img.maskShapeId,
                imageUrl: img.imageUrl?.substring(0, 50) + '...'
            });
        });
        finalMaskShapes.forEach((shape, index) => {
            console.log(`[SaveProject] 🎭 FINAL MASK SHAPE ${index + 1}:`, {
                id: shape.id,
                isMaskShape: shape.isMaskShape,
                isVisible: shape.isVisible,
                imageUrl: shape.imageUrl?.substring(0, 50) + '...'
            });
        });

        // 5. Prepare Editor State (EXACT same as template)
        const editorState = {
            canvasBackgroundColor: window.canvasBackgroundColor || '#ffffff',
            zoom: {
                scale: window.scale || 1.0,
                offsetX: window.offsetX || 0,
                offsetY: window.offsetY || 0
            },
            selectedObjectIndex: window.selectedObjectIndex || -1,
            nextId: window.nextId || 0,
            editorSettings: {
                lastUpdateTimestamp: Date.now()
            }
        };

        console.log('[SaveProject] ===== EDITOR STATE DEBUG =====');
        console.log('[SaveProject] window.canvasBackgroundColor:', window.canvasBackgroundColor);
        console.log('[SaveProject] window.scale:', window.scale);
        console.log('[SaveProject] window.offsetX:', window.offsetX);
        console.log('[SaveProject] window.offsetY:', window.offsetY);
        console.log('[SaveProject] Final editorState:', editorState);

        // 6. Validate and prepare artboard data
        const validatedArtboard = {
            x: typeof window.artboard.x === 'number' ? window.artboard.x : 0,
            y: typeof window.artboard.y === 'number' ? window.artboard.y : 0,
            width: typeof window.artboard.width === 'number' ? window.artboard.width : 600,
            height: typeof window.artboard.height === 'number' ? window.artboard.height : 600
        };

        console.log('[SaveProject] 🔍 Validated artboard:', validatedArtboard);

        // 7. Prepare Project Data (matching server schema)
        const projectData = {
            title: 'My Project', // Server expects 'title', not 'name'
            description: '', // Add description field
            previewImageUrl,
            artboard: validatedArtboard, // Validated artboard with required properties
            canvasObjects: serializableObjects,
            adminData,
            editorState,
            tags: [], // Add tags field
            status: 'draft' // Add status field
        };

        console.log('[SaveProject] Final project data prepared:', projectData);

        // Dispatch save project event
        const saveProjectEvent = new CustomEvent('saveProject', {
            detail: projectData
        });

        document.dispatchEvent(saveProjectEvent);

        // Fallback: Try to directly access the modal if event doesn't work
        setTimeout(() => {
            const modal = document.querySelector('project-modal');
            if (modal && !modal.projectData) {
                console.log('[SaveProject] Fallback: Opening modal directly with project data');
                modal.show(projectData);
            }
        }, 100);
    }



    // Duplicate Project functionality
    function handleDuplicateProject() {
        try {
            // Check if we have canvas objects to duplicate
            if (!window.canvasObjects || window.canvasObjects.length === 0) {
                alert('No content to duplicate. Please add some text, images, or shapes first.');
                return;
            }

            // Create a copy of current canvas objects
            const duplicatedObjects = JSON.parse(JSON.stringify(window.canvasObjects));

            // Offset duplicated objects slightly
            duplicatedObjects.forEach(obj => {
                if (obj.x !== undefined) obj.x += 20;
                if (obj.y !== undefined) obj.y += 20;
                // Update IDs to avoid conflicts
                if (obj.id !== undefined) obj.id = window.nextId++;
            });

            // Add duplicated objects to canvas
            window.canvasObjects.push(...duplicatedObjects);

            // Update canvas
            if (window.update) {
                window.update();
            }

            alert('Content duplicated successfully!');

        } catch (error) {
            console.error('Error duplicating project:', error);
            alert('Failed to duplicate project. Please try again.');
        }
    }



    // Helper function to generate canvas-wide thumbnail when no artboard exists
    function generateCanvasThumbnail(editorState) {
        try {
            console.log('[generateCanvasThumbnail] Input editorState:', editorState);

            const canvasObjects = editorState.canvasObjects || [];

            if (canvasObjects.length === 0) {
                console.log('[generateCanvasThumbnail] No objects to render, creating placeholder');
                return createPlaceholderImage();
            }

            // Calculate bounding box of all objects
            let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

            canvasObjects.forEach(obj => {
                const objX = obj.x || 0;
                const objY = obj.y || 0;
                const objWidth = obj.width || 100;
                const objHeight = obj.height || 50;

                minX = Math.min(minX, objX);
                minY = Math.min(minY, objY);
                maxX = Math.max(maxX, objX + objWidth);
                maxY = Math.max(maxY, objY + objHeight);
            });

            // Add some padding
            const padding = 50;
            minX -= padding;
            minY -= padding;
            maxX += padding;
            maxY += padding;

            const contentWidth = maxX - minX;
            const contentHeight = maxY - minY;

            console.log('[generateCanvasThumbnail] Content bounds:', { minX, minY, maxX, maxY, contentWidth, contentHeight });

            // Create thumbnail canvas
            const thumbnailCanvas = document.createElement('canvas');
            const thumbnailWidth = 400;
            const thumbnailHeight = Math.round((contentHeight / contentWidth) * thumbnailWidth);

            thumbnailCanvas.width = thumbnailWidth;
            thumbnailCanvas.height = thumbnailHeight;
            const ctx = thumbnailCanvas.getContext('2d');

            // Fill with background color
            ctx.fillStyle = editorState.canvasBackgroundColor || '#ffffff';
            ctx.fillRect(0, 0, thumbnailWidth, thumbnailHeight);

            // Calculate scale factor
            const scale = thumbnailWidth / contentWidth;

            // Render canvas objects
            canvasObjects.forEach(obj => {
                ctx.save();

                // Convert object position to thumbnail coordinates
                const relativeX = (obj.x - minX) * scale;
                const relativeY = (obj.y - minY) * scale;
                const scaledWidth = (obj.width || 100) * scale;
                const scaledHeight = (obj.height || 50) * scale;

                if (obj.type === 'text') {
                    // Render text
                    ctx.fillStyle = obj.color || '#000000';
                    ctx.font = `${Math.max(12, (obj.fontSize || 24) * scale)}px ${obj.fontFamily || 'Arial'}`;
                    ctx.textAlign = obj.textAlign || 'left';
                    ctx.textBaseline = 'top';
                    ctx.fillText(obj.text || 'Text', relativeX, relativeY);
                } else if (obj.type === 'image' && obj.src) {
                    // Render image placeholder
                    ctx.fillStyle = '#e5e7eb';
                    ctx.fillRect(relativeX, relativeY, scaledWidth, scaledHeight);
                    ctx.strokeStyle = '#9ca3af';
                    ctx.strokeRect(relativeX, relativeY, scaledWidth, scaledHeight);

                    // Add image icon
                    ctx.fillStyle = '#6b7280';
                    ctx.font = `${Math.max(16, scaledWidth/4)}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText('🖼️', relativeX + scaledWidth/2, relativeY + scaledHeight/2);
                } else if (obj.type === 'shape') {
                    // Render shape
                    ctx.fillStyle = obj.fill || '#3b82f6';
                    if (obj.shape === 'circle') {
                        ctx.beginPath();
                        ctx.arc(relativeX + scaledWidth/2, relativeY + scaledHeight/2, Math.min(scaledWidth, scaledHeight)/2, 0, 2 * Math.PI);
                        ctx.fill();
                    } else {
                        // Rectangle or other shapes
                        ctx.fillRect(relativeX, relativeY, scaledWidth, scaledHeight);
                    }
                }

                ctx.restore();
            });

            return thumbnailCanvas.toDataURL('image/png');
        } catch (error) {
            console.error('[generateCanvasThumbnail] Error generating canvas thumbnail:', error);
            return createPlaceholderImage();
        }
    }

    // Helper function to generate artboard thumbnail
    function generateArtboardThumbnail(editorState) {
        try {
            console.log('[generateArtboardThumbnail] Input editorState:', editorState);

            // Ensure artboard exists with fallback values
            const artboard = editorState.artboard || {
                x: 0,
                y: 0,
                width: 800,
                height: 600
            };

            console.log('[generateArtboardThumbnail] Using artboard:', artboard);

            const canvasObjects = editorState.canvasObjects || [];

            // Create thumbnail canvas with artboard dimensions
            const thumbnailCanvas = document.createElement('canvas');
            const thumbnailWidth = 400;
            const thumbnailHeight = Math.round((artboard.height / artboard.width) * thumbnailWidth);

            thumbnailCanvas.width = thumbnailWidth;
            thumbnailCanvas.height = thumbnailHeight;
            const ctx = thumbnailCanvas.getContext('2d');

            // Fill with background color
            ctx.fillStyle = editorState.canvasBackgroundColor || '#ffffff';
            ctx.fillRect(0, 0, thumbnailWidth, thumbnailHeight);

            // Calculate scale factor
            const scaleX = thumbnailWidth / artboard.width;
            const scaleY = thumbnailHeight / artboard.height;
            const scale = Math.min(scaleX, scaleY);

            // Render canvas objects that are within the artboard
            canvasObjects.forEach(obj => {
                // Check if object intersects with artboard
                const objX = obj.x || 0;
                const objY = obj.y || 0;
                const objWidth = obj.width || 100;
                const objHeight = obj.height || 50;

                // Skip objects that are completely outside the artboard
                if (objX + objWidth < artboard.x || objX > artboard.x + artboard.width ||
                    objY + objHeight < artboard.y || objY > artboard.y + artboard.height) {
                    return;
                }

                ctx.save();

                // Convert object position to artboard-relative coordinates
                const relativeX = (objX - artboard.x) * scale;
                const relativeY = (objY - artboard.y) * scale;
                const scaledWidth = objWidth * scale;
                const scaledHeight = objHeight * scale;

                if (obj.type === 'text') {
                    // Render text
                    ctx.fillStyle = obj.color || '#000000';
                    ctx.font = `${Math.max(12, (obj.fontSize || 24) * scale)}px ${obj.fontFamily || 'Arial'}`;
                    ctx.textAlign = obj.textAlign || 'left';
                    ctx.textBaseline = 'top';
                    ctx.fillText(obj.text || 'Text', relativeX, relativeY);
                } else if (obj.type === 'image') {
                    // Calculate actual scaled dimensions for image objects
                    const actualWidth = obj.originalWidth ? obj.originalWidth * obj.scale : objWidth;
                    const actualHeight = obj.originalHeight ? obj.originalHeight * obj.scale : objHeight;
                    const actualScaledWidth = actualWidth * scale;
                    const actualScaledHeight = actualHeight * scale;
                    const actualRelativeX = (objX - artboard.x) * scale;
                    const actualRelativeY = (objY - artboard.y) * scale;

                    // Check if image is loaded and draw it
                    if (obj.image && obj.image.complete && obj.image.naturalWidth > 0) {
                        ctx.drawImage(obj.image, actualRelativeX - actualScaledWidth/2, actualRelativeY - actualScaledHeight/2, actualScaledWidth, actualScaledHeight);
                    } else if (obj.imageUrl && obj.imageUrl.includes('.svg') && obj.svgColor) {
                        // SVG shape with color
                        ctx.fillStyle = obj.svgColor;
                        ctx.fillRect(actualRelativeX - actualScaledWidth/2, actualRelativeY - actualScaledHeight/2, actualScaledWidth, actualScaledHeight);
                    } else {
                        // Placeholder for unloaded images
                        ctx.fillStyle = '#e5e7eb';
                        ctx.fillRect(actualRelativeX - actualScaledWidth/2, actualRelativeY - actualScaledHeight/2, actualScaledWidth, actualScaledHeight);
                        ctx.strokeStyle = '#9ca3af';
                        ctx.strokeRect(actualRelativeX - actualScaledWidth/2, actualRelativeY - actualScaledHeight/2, actualScaledWidth, actualScaledHeight);

                        // Add image icon
                        ctx.fillStyle = '#6b7280';
                        ctx.font = `${Math.max(16, actualScaledWidth/4)}px Arial`;
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        ctx.fillText('🖼️', actualRelativeX, actualRelativeY);
                    }
                } else if (obj.type === 'shape') {
                    // Render shape
                    ctx.fillStyle = obj.fill || obj.svgColor || '#3b82f6';
                    if (obj.shape === 'circle') {
                        ctx.beginPath();
                        ctx.arc(relativeX + scaledWidth/2, relativeY + scaledHeight/2, Math.min(scaledWidth, scaledHeight)/2, 0, 2 * Math.PI);
                        ctx.fill();
                    } else {
                        // Rectangle or other shapes
                        ctx.fillRect(relativeX, relativeY, scaledWidth, scaledHeight);
                    }
                }

                ctx.restore();
            });

            return thumbnailCanvas.toDataURL('image/png');
        } catch (error) {
            console.error('[SaveProject] Error generating artboard thumbnail:', error);
            return createPlaceholderImage();
        }
    }

    // Helper function to create placeholder image
    function createPlaceholderImage() {
        try {
            // Create a small placeholder canvas
            const placeholderCanvas = document.createElement('canvas');
            placeholderCanvas.width = 400;
            placeholderCanvas.height = 300;
            const ctx = placeholderCanvas.getContext('2d');

            // Fill with a gradient background
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#4f46e5');
            gradient.addColorStop(1, '#7c3aed');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);

            // Add text
            ctx.fillStyle = '#ffffff';
            ctx.font = '24px Inter, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('Project Preview', 200, 140);
            ctx.font = '16px Inter, sans-serif';
            ctx.fillText('No canvas content', 200, 170);

            return placeholderCanvas.toDataURL('image/png');
        } catch (error) {
            console.error('[SaveProject] Error creating placeholder image:', error);
            // Return a minimal data URL as last resort
            return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
        }
    }

    // --- Project Save Button Management ---
    function updateProjectSaveButtons(hasProject, projectTitle) {
        console.log('[LeftMenu] Updating project save buttons, hasProject:', hasProject, 'title:', projectTitle);

        const saveProjectBtn = document.getElementById('saveProjectBtn');
        const updateProjectBtn = document.getElementById('updateProjectBtn');
        const saveAsProjectBtn = document.getElementById('saveAsProjectBtn');

        if (hasProject) {
            // Hide "Save Project" button
            if (saveProjectBtn) saveProjectBtn.style.display = 'none';

            // Show "Save" and "Save As New" buttons
            if (updateProjectBtn) {
                updateProjectBtn.style.display = 'block';
                updateProjectBtn.textContent = `Save "${projectTitle || 'Project'}"`;
            }
            if (saveAsProjectBtn) {
                saveAsProjectBtn.style.display = 'block';
            }

            console.log('[LeftMenu] Switched to existing project mode');
        } else {
            // Show "Save Project" button
            if (saveProjectBtn) saveProjectBtn.style.display = 'block';

            // Hide "Save" and "Save As New" buttons
            if (updateProjectBtn) updateProjectBtn.style.display = 'none';
            if (saveAsProjectBtn) saveAsProjectBtn.style.display = 'none';

            console.log('[LeftMenu] Switched to new project mode');
        }
    }

    // Listen for project loaded events
    document.addEventListener('projectLoaded', (event) => {
        console.log('[LeftMenu] Received projectLoaded event:', event.detail);
        const { hasProject, projectTitle } = event.detail;
        updateProjectSaveButtons(hasProject, projectTitle);
    });

    // Initialize project save buttons for new project (default state)
    updateProjectSaveButtons(false, null);

    // Expose the original save project function for updates
    window.handleSaveProjectFromLeftMenu = handleSaveProject;

    // Modify handleSaveProject to check for existing project BEFORE any modal logic
    const originalHandleSaveProject = handleSaveProject;
    handleSaveProject = async function() {
        console.log('[LeftMenu] ===== SAVE PROJECT CALLED =====');
        console.log('[LeftMenu] Checking for existing project...');
        console.log('[LeftMenu] window.currentProjectId:', window.currentProjectId);
        console.log('[LeftMenu] window.currentProjectTitle:', window.currentProjectTitle);
        console.log('[LeftMenu] window.currentProjectFolderId:', window.currentProjectFolderId);

        // Check if we have a current project and should update instead
        if (window.currentProjectId && window.handleUpdateProject) {
            console.log('[LeftMenu] ===== EXISTING PROJECT DETECTED =====');
            console.log('[LeftMenu] Going directly to handleUpdateProject - NO MODAL');
            await window.handleUpdateProject();
        } else {
            console.log('[LeftMenu] ===== NEW PROJECT =====');
            console.log('[LeftMenu] No current project, calling original handleSaveProject - WILL SHOW MODAL');
            await originalHandleSaveProject();
        }
    };

    // Make functions available globally if needed
    window.handleSaveProject = handleSaveProject;
    window.handleDuplicateProject = handleDuplicateProject;
    window.cleanObjectForSerialization = cleanObjectForSerialization;
    window.updateProjectSaveButtons = updateProjectSaveButtons;
});

// AI Generator helper functions (outside DOMContentLoaded to be globally accessible)

// Generate AI image
async function generateAIImage(objectText, templateId, palette) {
    console.log('[AIGenerator] 🎨 generateAIImage called with:', {
        objectText,
        templateId,
        palette: {
            id: palette?.id,
            name: palette?.name,
            description: palette?.description,
            fullObject: palette
        }
    });

    const payload = {
        object: objectText,
        templateId: templateId,
        imagePalette: palette  // Use same parameter name as working pages
    };

    console.log('[AIGenerator] 🎨 Sending payload to backend:', payload);

    const response = await fetch('/api/generate/image', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate image');
    }

    const result = await response.json();
    console.log('[AIGenerator] 🎨 Generation result:', result);
    return {
        imageUrl: result.imageUrl,
        generationId: result.generationId
    };
}

// Remove background from generated image
async function removeBackgroundFromGeneration(imageUrl, generationId) {
    console.log('[AIGenerator] 🎨 Starting background removal for:', { imageUrl, generationId });

    try {
        const response = await fetch('/api/images/bgremove', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                imageUrl: imageUrl,
                generationId: generationId
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || errorData.details || 'Background removal failed');
        }

        const result = await response.json();
        console.log('[AIGenerator] 🎨 Background removal successful:', result);

        // Return the new image URL with background removed
        return result.imageUrl;
    } catch (error) {
        console.error('[AIGenerator] 🎨 Background removal failed:', error);
        // If background removal fails, return the original image URL
        console.log('[AIGenerator] 🎨 Falling back to original image URL');

        if (window.showToast) {
            window.showToast('Background removal failed, using original image', 'warning');
        }

        return imageUrl;
    }
}

// Add generated image to canvas
async function addGeneratedImageToCanvas(imageUrl) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        // Don't use crossOrigin for B2 images - let the server handle authorization

        img.onload = () => {
            try {
                // Create new image object for canvas
                const newImageObj = {
                    id: window.nextId ? window.nextId++ : Date.now(),
                    type: 'image',
                    image: img,
                    imageUrl: imageUrl,
                    x: window.canvas ? window.canvas.width / 2 : 1024,
                    y: window.canvas ? window.canvas.height / 2 : 1024,
                    originalWidth: img.width,
                    originalHeight: img.height,
                    scale: 0.5, // Start at 50% scale
                    rotation: 0,
                    opacity: 100,
                    isSelected: false,
                    isFromGeneration: true, // Mark as AI generated
                    generationId: Date.now() // Unique generation ID
                };

                // Add to canvas objects array
                if (window.canvasObjects && Array.isArray(window.canvasObjects)) {
                    window.canvasObjects.push(newImageObj);
                    console.log('[AIGenerator] Image added to canvas objects');

                    // Select the new image
                    if (typeof window.selectObject === 'function') {
                        window.selectObject(window.canvasObjects.length - 1);
                    } else {
                        window.selectedObjectIndex = window.canvasObjects.length - 1;
                        newImageObj.isSelected = true;
                    }

                    // Update canvas
                    if (typeof window.update === 'function') {
                        window.update();
                    }

                    // Sync global references
                    if (typeof window.syncGlobalReferences === 'function') {
                        window.syncGlobalReferences();
                    }
                }

                resolve();
            } catch (error) {
                reject(error);
            }
        };

        img.onerror = () => {
            reject(new Error('Failed to load generated image'));
        };

        img.src = imageUrl;
    });
}

// Template selection function (globally accessible)
async function selectAITemplate(element) {
    // Remove previous selection
    document.querySelectorAll('.ai-template-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Add selection to clicked item
    element.classList.add('selected');
    const templateId = element.dataset.templateId;
    console.log('[AIGenerator] Template selected:', templateId);

    // Fetch template data and set original palette description (same as working pages)
    try {
        const response = await fetch(`/api/templates/${templateId}`);
        if (response.ok) {
            const template = await response.json();
            console.log('[AIGenerator] Template data loaded:', template);

            // Set the original palette description in the ColorPaletteSelector (same as index.html)
            const paletteSelector = document.getElementById('aiColorPaletteSelector');
            if (paletteSelector && template.originalPalette) {
                paletteSelector.setOriginalPaletteDescription(template.originalPalette);
                console.log('[AIGenerator] Set original palette from template:', template.originalPalette);
            }
        }
    } catch (error) {
        console.error('[AIGenerator] Error loading template data:', error);
    }
}

// --- Update Admin Fields After Restyle ---
function updateAdminFieldsAfterRestyle(generationResult, selectedTemplate, selectedPalette) {
    console.log('🎨 Updating admin fields after restyle:', {
        generationResult: generationResult,
        selectedTemplate: selectedTemplate,
        selectedPalette: selectedPalette
    });

    try {
        // Update admin prompt with the new generation prompt
        const adminPrompt = document.getElementById('adminPrompt');
        if (adminPrompt) {
            if (generationResult.prompt) {
                adminPrompt.value = generationResult.prompt;
                console.log('🎨 Updated adminPrompt:', generationResult.prompt);
            } else {
                console.log('🎨 No prompt in generationResult:', generationResult);
            }
        } else {
            console.log('🎨 adminPrompt element not found');
        }

        // Update admin model - get from current AI Generator selection since it's not in generationResult
        const adminModel = document.getElementById('adminModel');
        const currentModelSelect = document.getElementById('aiModelSelect');
        if (adminModel) {
            if (currentModelSelect && currentModelSelect.value) {
                adminModel.value = currentModelSelect.value;
                console.log('🎨 Updated adminModel from current selection:', currentModelSelect.value);
            } else if (generationResult.model) {
                adminModel.value = generationResult.model;
                console.log('🎨 Updated adminModel from generationResult:', generationResult.model);
            } else {
                console.log('🎨 No model available to update adminModel');
            }
        } else {
            console.log('🎨 adminModel element not found');
        }

        // Update admin original palette with the new palette
        const adminOriginalPalette = document.getElementById('adminOriginalPalette');
        if (adminOriginalPalette) {
            if (selectedPalette) {
                // Format palette info for display
                const paletteInfo = selectedPalette.name || selectedPalette.id || 'Custom Palette';
                adminOriginalPalette.value = paletteInfo;
                console.log('🎨 Updated adminOriginalPalette:', paletteInfo);
            } else {
                // If no palette was selected, use "Original Palette" as default
                adminOriginalPalette.value = 'Original Palette';
                console.log('🎨 Updated adminOriginalPalette to default: Original Palette');
            }
        }

        // Note: adminOriginalObject is NOT updated during restyle since the object stays the same

        // Update admin image URL with the new generated image
        const adminImageUrl = document.getElementById('adminImageUrl');
        if (adminImageUrl && generationResult.imageUrl) {
            adminImageUrl.value = generationResult.imageUrl;
            console.log('🎨 Updated adminImageUrl:', generationResult.imageUrl);
        }

        console.log('🎨 Admin fields updated successfully after restyle');

    } catch (error) {
        console.error('🎨 Error updating admin fields after restyle:', error);
    }
}

// Make functions globally available
window.generateAIImage = generateAIImage;
window.removeBackgroundFromGeneration = removeBackgroundFromGeneration;
window.addGeneratedImageToCanvas = addGeneratedImageToCanvas;
window.selectAITemplate = selectAITemplate;
window.updateAdminFieldsAfterRestyle = updateAdminFieldsAfterRestyle;
