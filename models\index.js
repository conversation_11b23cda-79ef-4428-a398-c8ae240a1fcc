import User from './User.js';
import Generation from './Generation.js';
import Collection from './Collection.js';
import Inspiration from './Inspiration.js';
import Settings from './Settings.js'; // Fixed: Settings.js not Setting.js
import Image from './Image.js'; // Import Image model
import Tag from './Tag.js'; // Import Tag model
import TextStyle from './TextStyle.js'; // Import TextStyle model
import Project from './Project.js'; // Import Project model
import ProjectFolder from './ProjectFolder.js'; // Import ProjectFolder model

export {
    User,
    Image, // Export Image model
    Tag, // Export Tag model
    TextStyle, // Export TextStyle model
    Generation,
    Collection,
    Inspiration,
    Settings, // Fixed: Settings not Setting
    Project, // Export Project model
    ProjectFolder // Export ProjectFolder model
};
