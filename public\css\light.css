/* ================= LOADER ================== */

.palleon-loader-inner {
    background: #eee;
}

.palleon-loader {
    border: 5px solid rgba(0, 0, 0, 0.1);
    border-top-color: #6658ea;
}

/* ================= GENERAL STYLES ================== */

* {
    scrollbar-color: #aaa #fff;
}

*::-webkit-scrollbar-track {
    background: #303030;
}

*::-webkit-scrollbar-thumb {
    background-color: #999;
    border: 4px solid #303030;
}

body {
    background: #eee;
    color: #494949;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #111;
}

.text-danger {
    color: #F44336 !important;
}

.text-success {
    color: #009688 !important;
}

.text-warning {
    color: #FFC107 !important;
}

.text-white {
    color: #111 !important;
}

#palleon-icon-panel hr {
    border-bottom: 1px dashed #bcbcbc;
}

.notice {
    color: #494949;
    background: rgba(0, 0, 0, 0.1);
    border-left: 4px solid #6658ea;
}

.notice.notice-danger {
    border-left: 4px solid #F44336;
}

.notice.notice-warning {
    border-left: 4px solid #FFC107;
}

.notice.notice-success {
    border-left: 4px solid #009688;
}

.palleon-img-loader {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: #6658ea;
}


/* ================= PAGE STRUCTURE ================== */

#palleon-toggle-left,
#palleon-toggle-right {
    background-color: #fff;
    border: 1px solid #bcbcbc;
}

#palleon-toggle-right.closed,
#palleon-toggle-left.closed {
    background-color: #fff;
}

#palleon-toggle-left .material-icons,
#palleon-toggle-right .material-icons {
    color: #111;
}

#palleon-toggle-left:hover .material-icons,
#palleon-toggle-right:hover .material-icons {
    color: #6658ea;
}

/* ================= TOP BAR ================== */

#palleon-top-bar {
    background: #eee;
    border-bottom: 1px solid #bcbcbc;
}

.palleon-top-bar-menu .palleon-btn-simple {
    color:#111;
}
  
.palleon-top-bar-menu .palleon-btn-simple:disabled {
    color:#494949
}

/* ================= DROPDOWN ================== */

ul.palleon-dropdown {
    background: #fff;
    border: 1px solid #bcbcbc;
}

ul.palleon-dropdown li {
    border-bottom: 1px solid #bcbcbc;
}

ul.palleon-dropdown li a {
    color: #494949;
}

ul.palleon-dropdown li a:hover {
    color: #111;
}

ul.palleon-dropdown li a .material-icons {
    color: #6658ea;
}

/* ================= CONTENT BAR ================== */

.palleon-content-bar {
    background: #fff;
    border: 1px solid #bcbcbc;
}

.palleon-content-bar .palleon-img-size {
    border-right: 1px solid #bcbcbc;
}

.palleon-content-bar .palleon-counter .palleon-btn,
.palleon-content-bar .palleon-counter .palleon-btn:hover,
.palleon-content-bar .palleon-counter .palleon-btn:focus {
    background: #fff;
    color: #494949;
}

.palleon-content-bar .palleon-counter .palleon-btn:hover {
    color: #111;
}

.palleon-counter .palleon-btn:disabled {
    color: rgba(0, 0, 0, 0.3);
}

.palleon-content-bar .palleon-counter:after {
    background: #eee;
}

#palleon-img-drag {
    border-right: 1px solid #bcbcbc;
    background: #fff;
    color: #494949;
}

#palleon-img-drag:hover,
#palleon-img-drag.active {
    background: #eee;
    color: #111;
}

/* ================= ICON MENU ================== */

#palleon-icon-menu {
    background: #eee;
}

#palleon-icon-menu.closed {
    border-right: 1px solid #bcbcbc;
}

button.palleon-icon-menu-btn {
    color: #494949;
    background: #eee;
}

button.palleon-icon-menu-btn.active,
button.palleon-icon-menu-btn:hover {
    color: #000;
    background: #fff;
}

#palleon-icon-panel {
    border-right: 1px solid #bcbcbc;
    background: #fff;
}

/* ================= CONTENT ================== */

#palleon-content {
    background-color: #f9f9f9;
}

#palleon-canvas-wrap {
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='2' height='2'%3E%3Cpath d='M1,0H0V1H2V2H1' fill='%23FFFFFF'/%3E%3C/svg%3E") left top/contain #ccc;
    background-size: 15px 15px;
}

#palleon-canvas-loader {
    background: #fff;
}

/* ================= LAYERS ================== */

#palleon-right-col {
    border-left: 1px solid #bcbcbc;
    background: #fff;
}

.palleon-layers-title {
    background: #eee;
}

#palleon-layers li {
    color: #494949;
    border-bottom: 1px solid #bcbcbc;
    background: #fff;
}

#palleon-layers li:first-child {
    border-top: 1px solid #bcbcbc;
}

#palleon-layers li:hover {
    color: #111;
}

#palleon-layers li.active {
    color: #111;
    background: #eee;
}

#palleon-layers li .layer-icons {
    background: #eee;
}

#palleon-layers li .layer-name {
    color:#494949;
}

#palleon-layers li .layer-name:focus {
    color:#111;
}

#palleon-layers li .layer-icons .material-icons {
    color: #111;
}

#palleon-layers>li>.material-icons {
    color: #6658ea;
}

#palleon-layers>li>.material-icons.layer-settings {
    background: #eee;
    color: #494949;
}

#palleon-layers>li>.material-icons.layer-settings:hover,
#palleon-layers>li>.material-icons.layer-settings.active {
    color: #111;
}

#palleon-layers li .layer-icons .layer-visible,
#palleon-layers li .layer-icons .layer-unlocked {
    color: #009688;
}

#palleon-layers li .layer-icons .layer-hidden,
#palleon-layers li .layer-icons .layer-locked {
    color: #FFC107;
}

#palleon-no-layer a {
    color: #6658ea;
}

#palleon-layer-delete-wrap {
    border-top: 1px dashed #bcbcbc;
}

/* ================= GRID ================== */

.palleon-frames-grid .grid-item,
.palleon-grid .grid-item {
    background: #eee;
}

.palleon-frame,
.palleon-element {
    border: 1px solid #eee;
    background: #eee;
}

.type-customSVG>a:before {
    color: #4CAF50;
}

.palleon-element.light {
    background: #eee;
}

.palleon-element.dark {
    background: #6658ea;
  border-color: #6658ea;
}

.palleon-frame:hover,
.palleon-element:hover {
    border: 1px solid #6658ea;
}

.palleon-element.dark:hover {
  border-color: #1B1642;
}

.template-favorite,
.palleon-frame .frame-favorite,
.palleon-element .element-favorite {
    background: #6658ea;
}

.palleon-element.dark .element-favorite {
  background: #1B1642;
}


.palleon-frames-grid .palleon-frame {
    border: 3px solid #eee;
}

.palleon-frames-grid .palleon-frame:hover {
    border: 3px solid #111;
}

.grid-icon-item {
    color: #494949;
    border: 1px solid #bcbcbc;
}

.grid-icon-item:hover {
    border: 1px solid #6658ea;
    color: #fff;
}

.grid-icon-item .material-icons {
    color: #6658ea;
}

.grid-icon-label {
    border-top: 1px solid #bcbcbc;
}

/* ================= ELEMENTS ================== */

#palleon-filters.palleon-grid img:hover {
    border: 3px solid #6658ea;
}

#palleon-filters.palleon-grid div label span {
    background: #eee;
    color: #494949;
}

#palleon-filters.palleon-grid input[type=checkbox]:checked+label img,
#palleon-filters.palleon-grid input[type=radio]:checked+label img {
    border: 3px solid #6658ea
}

#palleon-filters.palleon-grid div:hover span,
#palleon-filters.palleon-grid input[type=checkbox]:checked+label>span,
#palleon-filters.palleon-grid input[type=radio]:checked+label>span {
    color: #fff;
    background: #6658ea
}

.palleon-element>.material-icons {
    color: #494949;
}

/* ================= BUTTONS ================== */

.palleon-btn-simple {
    color: #494949;
}

.palleon-btn-simple:hover,
.palleon-btn-simple.active {
    color: #111;
}

.palleon-btn-simple:disabled {
    color: #494949;
}

.palleon-btn-simple.star {
    color: #FF9800 !important;
}

.palleon-btn {
    background: #eee;
    color: #111;
}

.palleon-btn:hover,
.palleon-btn.primary:hover {
    background: #5546e8;
    color:#fff;
}

.palleon-btn.active,
.palleon-btn.active:hover,
.palleon-btn.primary {
    background: #6658ea;
    color: #fff;
}

.palleon-btn.danger {
    background: #F44336;
    color:#fff;
}

.palleon-btn.danger:hover {
    background: #D32F2F;
    color:#fff;
}

.palleon-horizontal-center.active,
.palleon-vertical-center.active {
    background: #eee !important;
}

/* ================= BLOCKS ================== */

.icon-group {
    border: 1px solid #bcbcbc;
    background: #bcbcbc
}

.icon-group .palleon-btn {
    border-right: 1px solid #bcbcbc;
}

.icon-group .palleon-btn:hover,
.icon-group .palleon-btn.active {
    background: #fff;
  color:#111
}

/* ================= TOOLTIP ================== */

.tooltip:after {
    background-color: #111;
    color: #fff;
}

.tooltip:before {
    border-color: #111 transparent;
}

/* ================= PAGINATION ================== */

.palleon-pagination ul li {
    border: 1px solid #bcbcbc;
    color: #494949;
}

.palleon-pagination ul li.active,
.palleon-pagination ul li.active:hover {
    background: #6658ea;
    border-color: #6658ea;
    color: #fff;
}

.palleon-pagination ul li:hover {
    border-color: #6658ea;
}

.palleon-pagination ul li a {
    color: #111;
}

.palleon-accordion .palleon-pagination ul li {
    color: #494949;
    background: #eee;
}

.palleon-accordion .palleon-pagination ul li.active,
.palleon-accordion .palleon-pagination ul li.active:hover {
    color: #fff;
    background: #6658ea;
}

.palleon-accordion .palleon-pagination ul li a,
.palleon-accordion .palleon-pagination ul li>span {
    color: #494949;
}

.palleon-accordion .palleon-pagination ul li a:hover {
    color: #111
}

.palleon-accordion .palleon-pagination ul li.active>span {
    color: #fff
}

/* ================= ACCORDION ================== */

ul.palleon-accordion>li {
    border: 1px solid #bcbcbc;
}

ul.palleon-accordion>li.opened {
    border-color: #6658ea;
}

ul.palleon-accordion>li>a {
    color: #494949;
}

ul.palleon-accordion>li>a:hover,
ul.palleon-accordion>li.opened>a {
    color: #111;
}

ul.palleon-accordion>li.opened>a {
    border-bottom: 1px solid #bcbcbc
}

ul.palleon-accordion>li>a>.data-count {
    background: #6658ea;
    color: #fff;
}

/* ================= RANGESLIDER ================== */

.palleon-slider {
    background: #bcbcbc;
}

.palleon-slider::-webkit-slider-thumb {
    background: #6658ea;
    border: 3px solid #fff;
}

.palleon-slider::-moz-range-thumb {
    background: #6658ea;
    border: 3px solid #fff;
}

/* ================= COUNTER ================== */

.palleon-counter .palleon-btn,
.palleon-counter .palleon-btn:hover,
.palleon-counter .palleon-btn:focus {
    background: #bcbcbc;
}

.palleon-counter .palleon-btn:disabled {
    color: rgba(0, 0, 0, 0.3);
}

/* ================= CONTROLS ================== */

.palleon-control-label span {
    background: #eee;
    color: #111;
}

.palleon-form-field {
    background: #eee;
    border: 1px solid #bcbcbc;
    color: #494949;
}

.palleon-form-field:focus {
    background: #fff;
    border: 1px solid #6658ea;
    color: #111;
}

.palleon-search-wrap .material-icons {
    color: #494949;
}

#palleon-element-search-icon.cancel,
#palleon-icon-search-icon.cancel {
    color: #F44336;
}

.palleon-sub-settings {
    border-top: 1px dashed #bcbcbc;
}

/* ================= COLORPICKER ================== */

.palleon-colorpicker,
.palleon-colorpicker:focus,
.palleon-form-field {
    background: #eee;
    border: 1px solid #bcbcbc;
    color: #494949;
}

.palleon-colorpicker:focus {
    color: #111;
}

.sp-container {
    background: #fff;
    border: 1px solid #bcbcbc;
}

.sp-palette-container {
    border-right: solid 1px #bcbcbc;
}

.sp-container button {
    color: #494949;
}

.sp-container button:hover {
    color: #111;
}

.sp-container button.sp-choose {
    background: #6658ea;
    color: #fff;
}

.sp-container button.sp-choose:hover,
.sp-container button.sp-choose:focus {
    background: #5546e8;
    color: #fff;
}

.sp-clear-display:before {
    color: #111;
}

.sp-colorize-container {
    border-color:#bcbcbc !important;
}

/* ================= SELECT ================== */

.palleon-select {
    color: #494949;
    background: #eee url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23aaaaaa' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right 0.5rem center;
    background-size: 20px 10px;
    border: 1px solid #bcbcbc;
}

.palleon-select:focus {
    border: 1px solid #6658ea;
}

.palleon-select:focus::-ms-value {
    color: #465362;
    background-color: #fff;
}

.select2-container--dark .select2-selection--single {
    background: #eee;
    border: 1px solid #bcbcbc;
}

.select2-container--dark .select2-selection--single .select2-selection__rendered {
    color: #494949;
}

.select2-container--dark .select2-selection--single .select2-selection__arrow b {
    border-color: #bcbcbc transparent transparent transparent;
}

.select2-container--dark .select2-selection--single .select2-selection__placeholder {
    color: #fff;
}

.select2-container--open .select2-dropdown--below,
.select2-container--open .select2-dropdown--above {
    background: #fff;
    border-color: #bcbcbc;
}

.select2-container--dark .select2-results__option--highlighted[aria-selected] {
    background: #6658ea !important;
    color: #ffffff;
}

.select2-container--dark .select2-results__option[aria-selected=true] {
    background: #6658ea;
    color: #fff;
}

.select2-drop {
    border: 2px solid #bcbcbc;
}

.select2-drop.select2-drop-above {
    border-top: 2px solid #bcbcbc;
}

.select2-container--dark .select2-search input {
    border: 1px solid #bcbcbc;
    background: #eee;
    color: #111
}

.select2-container--dark .select2-search input:focus {
    border: 1px solid #6658ea;
}

.select2-container--dark .select2-results__group {
    border-top: 1px solid #bcbcbc;
}

/* ================= TABS ================== */

.palleon-tabs-menu li:hover {
    color: #111;
}

.palleon-tabs-menu li.active {
    background: #6658ea;
    color: #fff;
}

/* ================= TOGGLE ================== */

.palleon-toggle-switch {
    background: #bcbcbc;
}

.palleon-toggle-switch:before {
    background: #fff;
}

.palleon-toggle-checkbox:checked+.palleon-toggle-switch {
    background: #6658ea;
}

/* ================= CHECKBOX ================== */

.palleon-checkmark {
    background-color: #eee;
    border: 1px solid #bcbcbc;
}

.palleon-checkbox input:checked~.palleon-checkmark {
    background-color: #6658ea;
    border-color: #6658ea;
}

.palleon-checkbox .palleon-checkmark:after {
    border: solid #111;
    border-width: 0 3px 3px 0;
}

/* ================= CUSTOM CURSOR ================== */

.tm-pointer-simple.tm-cursor {
    background-color: #fff;
}

/* ================= MODAL ================== */

.palleon-modal {
    background: rgba(224, 224, 224, 0.9);
}

.palleon-modal-inner>.palleon-tabs>.palleon-tabs-menu>li {
    background: transparent;
  color:#494949;
}

.palleon-modal-inner>.palleon-tabs>.palleon-tabs-menu>li:hover, 
.palleon-modal-inner>.palleon-tabs>.palleon-tabs-menu>li.active {
    background: #fff;
  color:#111;
}

.palleon-modal-inner>.palleon-tabs>.palleon-tab,
.palleon-modal-bg {
    background: #fff;
    box-shadow: 0 19px 38px rgba(0, 0, 0, 0.20), 0 15px 12px rgba(0, 0, 0, 0.12);
}

.palleon-modal-close {
    color: #fff;
    background: #F44336;
}

.palleon-modal-close:hover {
    color: #fff;
    background: #D32F2F;
}

.palleon-modal-close .material-icons {
    font-size: 20px;
    line-height: 36px;
}

#palleon-download-as-json,
#palleon-save-as-json {
    border-top: 1px dashed #bcbcbc;
}

.palleon-select-btn-block>div:first-child {
    border-right: 1px dashed #bcbcbc
}

/* ================= TEMPLATE LIBRARY ================== */

.palleon-template-list {
    border: 1px solid #bcbcbc;
}

.palleon-template-list li {
    border-bottom: 1px solid #bcbcbc;
}

.palleon-template-list li.active {
    background:#eee
}

#palleon-history-list li .info .material-icons {
    color: #6658ea;
}

#palleon-history-list li .info span.time {
    background: #fff;
    color:#111;
}

/* ================= MASONRY ================== */

.palleon-masonry-item .favorite {
    background: #fff;
}

.palleon-masonry-item-inner {
    border: 4px solid #eee;
}

.grid-item:hover .palleon-masonry-item-inner,
.palleon-masonry-item-inner:hover {
    border-color: #6658ea;
}

.palleon-masonry-item-desc {
    background: #6658ea;
    color: #fff
}

.palleon-svg-library-delete,
.palleon-library-delete {
    color: #fff;
    background: #F44336;
}

.palleon-svg-library-delete:hover,
.palleon-library-delete:hover {
    background: #D32F2F;
}

a.pexels-url {
    color: #6658ea;
    background: #fff;
}

a.pexels-url:hover {
    color: #544abf;
}

#pexels-credit {
    color:#494949;
}

/* ================= RULER ================== */

.guide.v {
    border-right: 1px solid #6658ea;
}

.guide.h {
    border-bottom: 1px solid #6658ea;
}

.guide .info {
    background-color: #111;
    border: 1px solid #111;
    color: #fff;
}

.ruler {
    background-color:#eee;
}

.ruler .label {
    color: #494949;
}

.ruler.h {
    border-bottom: 1px solid #bcbcbc;
}

.ruler.h span.major {
    border-left: 1px solid #bcbcbc
}

.ruler.h span.milestone {
    border-left: 1px solid #bcbcbc
}

.ruler.v {
    border-right: 1px solid #bcbcbc
}

.ruler.v span.major {
    border-top: 1px solid #bcbcbc;
}

.ruler.v span.milestone {
    border-top: 1px solid #bcbcbc;
}

#palleon-ruler-icon {
  background: #eee;
    border-right:1px solid #bcbcbc;
    border-bottom:1px solid #bcbcbc;
}

#palleon-ruler-icon.closed {
    background: transparent;
}

.menu-btn {
    background-color: #6658ea;
    color: #fff;
}

.rg-menu {
  border-right: 1px solid #bcbcbc;
}

.rg-menu li {
    border-bottom: 1px solid #bcbcbc;
}

.rg-menu a {
    background-color: #eee;
  color: #494949;
}

.rg-menu a:hover,
.rg-menu a.selected {
    color: #000;
}

/* ================= MEDIA QUERIES ================== */

@media only screen and (max-width: 800px) {
    .palleon-modal-inner>.palleon-tabs>.palleon-tabs-menu>li {
        background: #fff;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
}