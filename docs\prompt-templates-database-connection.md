# Prompt Templates Database Integration Documentation

This document explains how the `prompt-templates.html` page connects to the MongoDB database through the backend API, ensuring error-free operations for creating, reading, updating, and deleting prompt templates.

## Database Model Structure

Templates are stored in MongoDB using the following schema (defined in `models/PromptTemplate.js`):

```javascript
const promptTemplateSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    category: {
        type: String,
        required: true,
        trim: true
    },
    template: {
        type: String,
        required: true
    },
    thumbnailUrl: {
        type: String,
        default: ''
    },
    randomOptions: {
        type: Object,
        default: {}
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        default: null
    },
    isPublic: {
        type: Boolean,
        default: true
    }
});
```

## API Endpoints

The server provides these endpoints for template management (defined in `server.js`):

1. **GET /api/templates**
   - Retrieves all templates
   - No authentication required
   - Returns an array of template objects

2. **GET /api/templates/:id**
   - Retrieves a single template by ID
   - No authentication required
   - Returns a single template object

3. **POST /api/templates**
   - Creates a new template
   - No authentication required (but could be added)
   - Expects a JSON body with template properties
   - Returns the created template object

4. **PUT /api/templates/:id**
   - Updates an existing template
   - No authentication required (but could be added)
   - Expects a JSON body with updated template properties
   - Returns the updated template object

5. **DELETE /api/templates/:id**
   - Deletes a template by ID
   - No authentication required (but could be added)
   - Returns a success message

6. **POST /api/templates/delete/:id**
   - Alternative deletion endpoint using POST method
   - Used when DELETE requests might be restricted
   - Returns a success message

## Client-Side Integration (prompt-templates.html)

### Template Loading Process

When the page loads, it fetches all templates from the database:

```javascript
document.addEventListener('DOMContentLoaded', () => {
    loadTemplates();
});

function loadTemplates() {
    fetch('/api/templates')
        .then(response => response.json())
        .then(templates => {
            // Process and display templates
        })
        .catch(error => {
            console.error('Error loading templates:', error);
            displayError('Failed to load templates. Please try again.');
        });
}
```

### Template Creation Process

When a new template is created:

1. Validate all required fields on the client-side
2. Create a template object with user inputs
3. Send a POST request to `/api/templates`
4. Handle the response (success or error)
5. Update the UI accordingly

```javascript
function saveTemplate() {
    // 1. Validate fields
    if (!validateTemplateForm()) return;
    
    // 2. Create template object
    const template = {
        name: document.getElementById('templateName').value,
        category: document.getElementById('templateCategory').value,
        template: document.getElementById('templateContent').value,
        thumbnailUrl: document.getElementById('thumbnailUrlInput').value,
        randomOptions: getRandomOptionsFromUI()
    };
    
    // 3. Send POST request
    fetch('/api/templates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(template)
    })
    .then(response => {
        // 4. Handle response
        if (!response.ok) {
            throw new Error('Failed to save template');
        }
        return response.json();
    })
    .then(savedTemplate => {
        // 5. Update UI
        displaySuccess('Template saved successfully');
        addTemplateToList(savedTemplate);
    })
    .catch(error => {
        console.error('Error saving template:', error);
        displayError('Failed to save template. Please try again.');
    });
}
```

### Template Update Process

For updating existing templates:

1. Load existing template data
2. Populate the form
3. Submit updates via PUT request to `/api/templates/:id`
4. Handle the response

```javascript
function editTemplate(templateId) {
    currentEditingTemplateId = templateId;
    
    // 1. Load existing template
    fetch(`/api/templates/${templateId}`)
        .then(response => response.json())
        .then(template => {
            // 2. Populate form fields
            document.getElementById('templateName').value = template.name;
            document.getElementById('templateCategory').value = template.category;
            document.getElementById('templateContent').value = template.template;
            document.getElementById('thumbnailUrlInput').value = template.thumbnailUrl || '';
            populateRandomOptionsUI(template.randomOptions);
            
            // Show update UI elements
            document.getElementById('updateTemplateBtn').style.display = 'inline-block';
            document.getElementById('cancelEditBtn').style.display = 'inline-block';
            document.getElementById('saveTemplateBtn').style.display = 'none';
        })
        .catch(error => {
            console.error('Error loading template for edit:', error);
            displayError('Failed to load template details');
        });
}

function updateTemplate() {
    // 1. Validate fields
    if (!validateTemplateForm()) return;
    
    // 2. Create updated template object
    const template = {
        name: document.getElementById('templateName').value,
        category: document.getElementById('templateCategory').value,
        template: document.getElementById('templateContent').value,
        thumbnailUrl: document.getElementById('thumbnailUrlInput').value,
        randomOptions: getRandomOptionsFromUI(),
        updatedAt: new Date()
    };
    
    // 3. Send PUT request
    fetch(`/api/templates/${currentEditingTemplateId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(template)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Failed to update template');
        }
        return response.json();
    })
    .then(updatedTemplate => {
        displaySuccess('Template updated successfully');
        updateTemplateInList(updatedTemplate);
        resetForm();
    })
    .catch(error => {
        console.error('Error updating template:', error);
        displayError('Failed to update template. Please try again.');
    });
}
```

### Template Deletion Process

For deleting templates:

```javascript
function deleteTemplate(templateId) {
    if (confirm('Are you sure you want to delete this template?')) {
        // Using POST for delete to ensure compatibility
        fetch(`/api/templates/delete/${templateId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to delete template');
            }
            return response.json();
        })
        .then(() => {
            displaySuccess('Template deleted successfully');
            removeTemplateFromList(templateId);
        })
        .catch(error => {
            console.error('Error deleting template:', error);
            displayError('Failed to delete template. Please try again.');
        });
    }
}
```

## Error Prevention Guidelines

To ensure error-free operations:

1. **Field Validation**
   - Always validate required fields before submission
   - Ensure template content contains placeholders like `[object]`, `[text1]`, etc.
   - Validate URLs for thumbnails

2. **Error Handling**
   - Always include `.catch()` blocks for fetch requests
   - Display user-friendly error messages
   - Log detailed errors to console for debugging

3. **Data Consistency**
   - Use the correct field names matching the database schema:
     - `name` - The template name
     - `category` - The template category
     - `template` - The actual prompt template content
     - `thumbnailUrl` - URL to the template thumbnail image
     - `randomOptions` - Object containing random options for variables

4. **Route Order Awareness**
   - Remember that in Express.js, route order matters
   - More specific routes like `/api/templates/delete/:id` must be defined before general routes like `/api/templates/:id`

5. **Form Reset**
   - Always reset forms after successful operations
   - Clear editing state when canceling edits

## Troubleshooting Common Issues

| Issue | Possible Cause | Solution |
|-------|---------------|----------|
| 404 Not Found | Incorrect API endpoint URL | Double-check endpoint paths and parameters |
| 400 Bad Request | Invalid data sent to server | Validate form data before submission |
| 500 Server Error | Database connection issues | Check MongoDB connection |
| Thumbnail not displaying | Incorrect field name used | Use `thumbnailUrl` field name consistently |
| Template not saving | Missing required fields | Add form validation for all required fields |
| Delete operation failing | Using DELETE method in restricted environment | Use the POST `/api/templates/delete/:id` endpoint |

## Integration with Main Page

The main page (`index.html`) uses templates for sticker generation:

1. Loads templates in the carousel
2. Displays thumbnails using the `thumbnailUrl` field
3. Selects templates via `selectTemplate()` function
4. Passes template data to the sticker generation API

For seamless integration, ensure consistent field names between pages.

## Preserving Variable Processing Functionality

When connecting to a database, it's critical to preserve existing functionality, especially the complex variable processing system that handles:

### Variables in Templates
- `{object}` - User input from the main page
- `{text1}`, `{text2}`, `{text3}` - Text inputs from the main page or random fallbacks
- `{art}` - Art style variables selected from options
- `{elements}` - Design elements randomly selected
- `{fontstyle}` - Font style options
- `{feel}` - Feel/mood descriptors

### How to Maintain This System When Adding Database Connectivity

1. **Store the Entire Template String**
   - Save the complete template string with all variable placeholders intact in the `template` field
   - Example: `"Create a {art} style sticker with {object} and {text1} theme with {elements} and {feel} mood"`

2. **Store Random Options Separately**
   - Keep the `randomOptions` object structure unchanged when saving to the database
   - Include all categories: `art_options`, `elements_options`, `fontstyle_options`, `feel_options`, etc.
   - Ensure the `text1_options`, `text2_options`, and `text3_options` arrays remain intact

3. **Use a Migration Approach**
   - Don't replace existing localStorage code immediately
   - First, implement dual-write (save to both localStorage and database)
   - Then implement dual-read (try database first, fall back to localStorage)
   - Finally, transition fully to database once verified

4. **Variable Processing Functions**
   - Keep the existing variable processing functions unchanged
   - When retrieving templates from the database, pass them through the same processing pipeline
   - Example functions to preserve:
     - `processTemplateVariables()`
     - `getRandomFallbackForVariable()`
     - `selectRandomOptions()`

### Sample Migration Code

```javascript
// STEP 1: Dual-write approach
function saveTemplate(template) {
  // Save to localStorage (existing code)
  saveTemplateToLocalStorage(template);
  
  // Also save to database
  fetch('/api/templates', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(template)
  })
  .then(response => response.json())
  .then(data => console.log('Template saved to database:', data))
  .catch(err => console.error('Error saving to database:', err));
}

// STEP 2: Dual-read approach
async function loadTemplates() {
  try {
    // Try to load from database first
    const response = await fetch('/api/templates');
    if (response.ok) {
      const dbTemplates = await response.json();
      if (dbTemplates && dbTemplates.length > 0) {
        return processTemplates(dbTemplates);
      }
    }
    // Fall back to localStorage if needed
    return loadTemplatesFromLocalStorage();
  } catch (error) {
    console.error('Error loading templates:', error);
    // Fall back to localStorage if there's an error
    return loadTemplatesFromLocalStorage();
  }
}
```

## Critical Fields to Preserve

To avoid breaking functionality when migrating to a database, ensure these fields maintain their structure:

1. `template` (string): The full template string with all variables
2. `randomOptions` (object): With nested objects for each variable type:
   ```javascript
   randomOptions: {
     art_options: ["watercolor", "cartoon", "pixel art", ...],
     feel_options: ["playful", "serious", "vibrant", ...],
     elements_options: ["clean lines", "gradients", "shadows", ...],
     text1_options: ["inspiring", "funny", "motivational", ...],
     text2_options: ["cool", "awesome", "amazing", ...],
     text3_options: ["special", "unique", "custom", ...]
   }
   ```

By following these guidelines, you can add database connectivity without losing the complex variable processing system that makes the template system valuable.

---

Last updated: March 25, 2025
