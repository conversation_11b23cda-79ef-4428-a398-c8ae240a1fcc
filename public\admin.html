<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #111;
            color: #fff;
            margin: 0;
            padding: 0;
        }

        .admin-container {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .section h2 {
            margin-top: 0;
            margin-bottom: 1.5rem;
            color: #fff;
        }

        .form-container {
            max-width: 500px;
        }

        #createUserAlert {
            margin-bottom: 1rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }

        th {
            background: #222;
            font-weight: 600;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            background: #ff1cf7;
            color: white;
            cursor: pointer;
            margin-right: 0.5rem;
        }

        .btn-small:hover {
            background: #d417cc;
        }

        .btn-delete {
            background: #dc3545;
        }

        .btn-delete:hover {
            background: #c82333;
        }

        .btn-block {
            background: #ffc107;
        }

        .btn-block:hover {
            background: #e0a800;
        }

        .btn-unblock {
            background: #28a745;
        }

        .btn-unblock:hover {
            background: #218838;
        }

        .user-blocked {
            background: rgba(220, 53, 69, 0.1);
        }

        .block-reason {
            font-size: 0.9em;
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .credits-unlimited {
            color: #28a745;
            font-weight: bold;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }

        .modal-content {
            background: #1a1a1a;
            margin: 10% auto;
            padding: 2rem;
            width: 90%;
            max-width: 500px;
            border-radius: 8px;
            position: relative;
        }

        .close-modal {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .close-modal:hover {
            color: #fff;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
        }

        .form-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #333;
            border-radius: 4px;
            background: #222;
            color: #fff;
            box-sizing: border-box;
        }

        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            display: none;
        }

        .alert-success {
            background: #1a472a;
            color: #4ade80;
        }

        .alert-error {
            background: #481a1d;
            color: #f87171;
        }

        button[type="submit"] {
            width: 100%;
            padding: 0.75rem;
            background: #ff1cf7;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }

        button[type="submit"]:hover {
            background: #d417cc;
        }

        .update-section {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .update-form {
            display: flex;
            gap: 20px;
            align-items: flex-end;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .form-input {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #333;
            background: #222;
            color: white;
            min-width: 200px;
        }
        .btn-update {
            background: #ff1493;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            height: 35px;
        }
        .btn-update:hover {
            background: #ff1493dd;
        }

        /* Add notification styles */
        .notifications-section {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .notifications-list {
            margin-top: 1rem;
        }
        .notification-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #333;
            font-size: 0.9rem;
        }
        .notification-item:last-child {
            border-bottom: none;
        }
        .notification-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        .status-pending {
            background: #ffd700;
            color: #000;
        }
        .status-processed {
            background: #00ff00;
            color: #000;
        }
        .status-failed {
            background: #ff0000;
            color: #fff;
        }
        .notification-time {
            color: #888;
            font-size: 0.8rem;
        }

        .style-options {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .style-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 8px;
            cursor: pointer;
        }

        .style-option img {
            width: 50px;
            height: 50px;
            margin-bottom: 10px;
        }

        .style-option span {
            font-size: 14px;
            font-weight: bold;
        }

        .background-options {
            margin-bottom: 20px;
        }

        .background-options label {
            margin-right: 20px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <h1>Admin Dashboard</h1>

        <!-- Notifications Section -->
        <div class="notifications-section">
            <h2>Recent IPN Notifications</h2>
            <div id="notificationsContainer" class="notifications-list">
                <!-- Notifications will be loaded here -->
            </div>
        </div>

        <!-- Updates Section -->
        <div class="update-section">
            <h2>Updates</h2>
            <div class="update-form">
                <div class="form-group">
                    <label for="updateEmail">User email</label>
                    <input type="email" id="updateEmail" class="form-input" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="updateProduct">Product ID</label>
                    <input type="text" id="updateProduct" class="form-input" placeholder="wso_kwc43t">
                </div>
                <button onclick="handleProductUpdate()" class="btn-update">Update User</button>
            </div>
        </div>

        <!-- Create Test User Section -->
        <div class="section">
            <h2>Create Test User</h2>
            <div class="form-container">
                <div id="createUserAlert" class="alert"></div>
                <form id="createUserForm" onsubmit="createTestUser(event)">
                    <div class="form-group">
                        <label for="createName">Name:</label>
                        <input type="text" id="createName" required>
                    </div>
                    <div class="form-group">
                        <label for="createEmail">Email:</label>
                        <input type="email" id="createEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="createPassword">Password:</label>
                        <input type="password" id="createPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="createCredits">Initial Credits:</label>
                        <input type="number" id="createCredits" value="100" required min="0">
                    </div>
                    <button type="submit">Create Test User</button>
                </form>
            </div>
        </div>

        <!-- Users Section -->
        <div class="section">
            <h2>Manage Users</h2>
            <div id="usersTable">
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Credits</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>

        <!-- Edit User Modal -->
        <div id="editUserModal" class="modal">
            <div class="modal-content">
                <span class="close-modal" onclick="closeEditModal()">&times;</span>
                <h2>Edit User</h2>
                <div id="editUserAlert" class="alert"></div>
                <form id="editUserForm" onsubmit="updateUser(event)">
                    <input type="hidden" id="editUserId">
                    <div class="form-group">
                        <label for="editName">Name:</label>
                        <input type="text" id="editName" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">Email:</label>
                        <input type="email" id="editEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="editPassword">New Password (leave blank to keep current):</label>
                        <input type="password" id="editPassword">
                    </div>
                    <div class="form-group">
                        <label for="editCredits">Credits:</label>
                        <select id="editCredits" required>
                            <option value="unlimited">Unlimited</option>
                            <option value="custom">Custom Amount</option>
                        </select>
                        <input type="number" id="editCreditsCustom" style="display: none;" min="0">
                    </div>
                    <button type="submit">Update User</button>
                </form>
            </div>
        </div>

        <!-- Style Options -->
        <div class="style-options">
            <div class="style-option" data-style-id="chibi-noir">
                <img src="/images/chibi-noir.png" alt="Chibi Noir Style">
                <span>Chibi Noir</span>
            </div>
            <div class="style-option" data-style-id="chibi-pastel">
                <img src="/images/chibi-pastel.png" alt="Chibi Pastel Style">
                <span>Chibi Pastel</span>
            </div>
            <div class="style-option" data-style-id="child-like">
                <img src="/images/child-like.png" alt="Child Like Style">
                <span>Child Like</span>
            </div>
        </div>

        <!-- Background Options -->
        <div class="background-options">
            <label>
                <input type="radio" name="background" value="light">
                Light
            </label>
            <label>
                <input type="radio" name="background" value="dark" checked>
                Dark
            </label>
        </div>

    </div>

    <script>
        // Function to update admin credits
        async function updateAdminCredits(credits) {
            try {
                const response = await fetch('/api/admin/credits/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ credits })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to update admin credits');
                }

                const data = await response.json();
                console.log('Admin credits updated:', data);
                return data;
            } catch (error) {
                console.error('Error updating admin credits:', error);
                throw error;
            }
        }

        // Global variable to store users
        let users = [];

        // Load users
        async function loadUsers() {
            try {
                const response = await fetch('/api/admin/users', {
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error('Failed to load users');
                }
                
                users = await response.json(); // Store users in global variable
                
                const tbody = document.querySelector('#usersTable tbody');
                tbody.innerHTML = '';

                users.forEach(user => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${user.name || ''}</td>
                        <td>${user.email || ''}</td>
                        <td>${user.credits === 456321 ? 'HIDE' : (user.credits === 123654 ? 'Unlimited' : user.credits)}</td>
                        <td>${user.role || ''}</td>
                        <td>${user.status || 'Active'}</td>
                        <td>
                            <button class="btn-small" onclick='openEditModal(${JSON.stringify(user)})'>Edit</button>
                            <button class="btn-small btn-delete" onclick="deleteUser('${user._id}')">Delete</button>
                        </td>
                    `;
                    tbody.appendChild(tr);
                });
            } catch (error) {
                console.error('Error loading users:', error);
                document.getElementById('usersTable').innerHTML = 
                    '<p class="error">Error loading users. Please try again.</p>';
            }
        }

        // Edit user functions
        function openEditModal(user) {
            const modal = document.getElementById('editUserModal');
            const alert = document.getElementById('editUserAlert');
            const creditsSelect = document.getElementById('editCredits');
            const creditsCustom = document.getElementById('editCreditsCustom');

            // Reset alert
            alert.style.display = 'none';
            
            // Fill form with user data
            document.getElementById('editUserId').value = user._id;
            document.getElementById('editName').value = user.name || '';
            document.getElementById('editEmail').value = user.email || '';
            document.getElementById('editPassword').value = '';

            // Handle credits display
            if (user.credits === 123654) {
                creditsSelect.value = 'unlimited';
                creditsCustom.style.display = 'none';
            } else {
                creditsSelect.value = 'custom';
                creditsCustom.style.display = 'block';
                creditsCustom.value = user.credits || 0;
            }

            // Function to handle credits select change
            function handleCreditsChange() {
                const creditsSelect = document.getElementById('editCredits');
                const creditsCustom = document.getElementById('editCreditsCustom');
                
                if (creditsSelect.value === 'custom') {
                    creditsCustom.style.display = 'block';
                    creditsCustom.required = true;
                } else {
                    creditsCustom.style.display = 'none';
                    creditsCustom.required = false;
                }
            }

            // Add event listener to credits select
            document.getElementById('editCredits').addEventListener('change', handleCreditsChange);

            // Show modal
            modal.style.display = 'block';

            // Add click outside to close
            window.onclick = function(event) {
                if (event.target === modal) {
                    closeEditModal();
                }
            }
        }

        function closeEditModal() {
            const modal = document.getElementById('editUserModal');
            modal.style.display = 'none';
            window.onclick = null;
        }

        async function updateUser(event) {
            event.preventDefault();
            const alert = document.getElementById('editUserAlert');
            const userId = document.getElementById('editUserId').value;
            const name = document.getElementById('editName').value;
            const email = document.getElementById('editEmail').value;
            const password = document.getElementById('editPassword').value;
            const creditsSelect = document.getElementById('editCredits');
            const creditsCustom = document.getElementById('editCreditsCustom');
            let credits;

            if (creditsSelect.value === 'unlimited') {
                credits = 123654;
            } else {
                credits = parseInt(creditsCustom.value);
                if (isNaN(credits) || credits < 0) {
                    alert.textContent = 'Please enter a valid number of credits';
                    alert.style.display = 'block';
                    return;
                }
            }

            try {
                // Check if this is the admin user
                const user = users.find(u => u._id === userId);
                if (user && user.role === 'admin') {
                    // Use special admin credits endpoint
                    await updateAdminCredits(credits);
                } else {
                    // Regular user update
                    const response = await fetch(`/api/admin/users/${userId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include',
                        body: JSON.stringify({
                            name,
                            email,
                            password,
                            credits
                        })
                    });

                    if (!response.ok) {
                        const data = await response.json();
                        throw new Error(data.error || 'Failed to update user');
                    }
                }

                // Reload users list
                await loadUsers();
                
                // Close modal
                closeEditModal();

                alert.textContent = 'User updated successfully';
                alert.style.display = 'block';
                alert.classList.remove('error');
                alert.classList.add('success');
            } catch (error) {
                console.error('Error updating user:', error);
                alert.textContent = error.message;
                alert.style.display = 'block';
                alert.classList.remove('success');
                alert.classList.add('error');
            }
        }

        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || 'Failed to delete user');
                }

                // Reload users table
                loadUsers();

            } catch (error) {
                alert(error.message);
            }
        }

        // Create test user function
        async function createTestUser(event) {
            event.preventDefault();
            const alert = document.getElementById('createUserAlert');
            
            try {
                const userData = {
                    name: document.getElementById('createName').value,
                    email: document.getElementById('createEmail').value,
                    password: document.getElementById('createPassword').value,
                    credits: parseInt(document.getElementById('createCredits').value),
                    registered: true,
                    role: 'user',
                    subscription: {
                        plan: 'free',
                        status: 'active'
                    },
                    creditHistory: [{
                        product: 'TestAccount',
                        credits: parseInt(document.getElementById('createCredits').value),
                        date: new Date(),
                        transactionId: 'ADMIN_CREATED'
                    }],
                    usage: {
                        imagesGenerated: 0,
                        savedPresets: 0
                    }
                };

                const response = await fetch('/api/admin/users/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to create user');
                }

                alert.textContent = 'User created successfully!';
                alert.className = 'alert alert-success';
                alert.style.display = 'block';

                // Reset form
                event.target.reset();
                document.getElementById('createCredits').value = '100';

                // Reload users table
                loadUsers();

                // Hide success message after 3 seconds
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 3000);

            } catch (error) {
                alert.textContent = error.message;
                alert.className = 'alert alert-error';
                alert.style.display = 'block';
            }
        }

        // Add block/unblock function
        async function toggleBlockUser(userId, block) {
            const reason = block ? prompt('Enter reason for blocking user:') : null;
            if (block && !reason) return; // Cancel if no reason provided when blocking

            try {
                const response = await fetch(`/api/admin/users/${userId}/block`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        blocked: block,
                        reason: reason
                    })
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || `Failed to ${block ? 'block' : 'unblock'} user`);
                }

                // Reload users table
                loadUsers();

            } catch (error) {
                alert(error.message);
            }
        }

        async function handleProductUpdate() {
            const email = document.getElementById('updateEmail').value;
            const productId = document.getElementById('updateProduct').value;

            if (!email || !productId) {
                alert('Please fill in both email and product ID');
                return;
            }

            try {
                const response = await fetch('/admin/update-product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, productId })
                });

                const data = await response.json();

                if (response.ok) {
                    alert('User updated successfully!');
                    // Clear the form
                    document.getElementById('updateEmail').value = '';
                    document.getElementById('updateProduct').value = '';
                    // Reload the users list
                    loadUsers();
                } else {
                    alert(data.error || 'Failed to update user');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Failed to update user');
            }
        }

        // Load notifications function
        async function loadNotifications() {
            try {
                const response = await fetch('/api/admin/notifications', {
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const contentType = response.headers.get("content-type");
                if (!contentType || !contentType.includes("application/json")) {
                    console.log("Notifications not implemented yet");
                    return;
                }

                const data = await response.json();
                const container = document.getElementById('notificationsContainer');
                if (!container) return;

                if (data.notifications && data.notifications.length > 0) {
                    container.innerHTML = data.notifications.map(notification => `
                        <div class="notification">
                            <p>${notification.message}</p>
                            <span class="time">${new Date(notification.timestamp).toLocaleString()}</span>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<p>No new notifications</p>';
                }
            } catch (error) {
                console.log("Notifications not implemented yet");
            }
        }

        // Initialize page
        async function initializePage() {
            await loadUsers();
            await loadNotifications();
        }

        // Call initialize when page loads
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
    <script src="/js/generate.js"></script>
</body>
</html>