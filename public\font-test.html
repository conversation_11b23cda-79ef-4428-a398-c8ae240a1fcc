<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Variant Detection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .font-controls {
            margin-bottom: 20px;
        }
        .font-controls select, .font-controls input {
            margin: 5px;
            padding: 5px;
        }
        .preview-text {
            font-size: 24px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            background: #f9f9f9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .font-info {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Font Variant Detection System Test</h1>
        
        <div class="test-section">
            <h2>System Status</h2>
            <div id="systemStatus" class="status">Initializing...</div>
            <div id="fontInfo" class="font-info"></div>
        </div>
        
        <div class="test-section">
            <h2>Font Variant Test</h2>
            <div class="font-controls">
                <label>Font Family: 
                    <select id="fontSelect">
                        <option value="Arial">Arial</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Roboto">Roboto</option>
                        <option value="Raleway">Raleway</option>
                        <option value="Almendra">Almendra</option>
                    </select>
                </label>
                
                <label>
                    <input type="checkbox" id="boldCheck"> Bold
                </label>
                
                <label>
                    <input type="checkbox" id="italicCheck"> Italic
                </label>
            </div>
            
            <div class="preview-text" id="previewText">
                The quick brown fox jumps over the lazy dog. 1234567890
            </div>
            
            <div id="variantStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>Available Fonts</h2>
            <div id="availableFonts" class="font-info"></div>
        </div>
    </div>

    <script src="/js/font-variant-detector.js"></script>
    <script>
        let detector = null;
        
        async function initializeTest() {
            const statusEl = document.getElementById('systemStatus');
            const fontInfoEl = document.getElementById('fontInfo');
            
            try {
                statusEl.textContent = 'Initializing font variant detector...';
                statusEl.className = 'status warning';
                
                detector = window.fontVariantDetector;
                if (!detector) {
                    throw new Error('Font variant detector not available');
                }
                
                await detector.initialize();
                
                statusEl.textContent = 'Font variant detection system initialized successfully!';
                statusEl.className = 'status success';
                
                // Display font information
                const fontCount = detector.availableFonts.size;
                fontInfoEl.textContent = `Detected ${fontCount} font families`;
                
                // Update available fonts display
                updateAvailableFonts();
                
                // Setup event listeners
                setupEventListeners();
                
                // Initial update
                updateFontPreview();
                
            } catch (error) {
                statusEl.textContent = `Error: ${error.message}`;
                statusEl.className = 'status error';
                console.error('Font test initialization failed:', error);
            }
        }
        
        function updateAvailableFonts() {
            const availableFontsEl = document.getElementById('availableFonts');
            
            if (!detector || !detector.availableFonts) {
                availableFontsEl.textContent = 'No font data available';
                return;
            }
            
            let html = '';
            for (const [family, variants] of detector.availableFonts.entries()) {
                const availableVariants = [];
                if (variants.regular) availableVariants.push('Regular');
                if (variants.bold) availableVariants.push('Bold');
                if (variants.italic) availableVariants.push('Italic');
                if (variants.boldItalic) availableVariants.push('Bold Italic');
                
                html += `<div><strong>${family}:</strong> ${availableVariants.join(', ')}</div>`;
            }
            
            availableFontsEl.innerHTML = html || 'No fonts detected';
        }
        
        function setupEventListeners() {
            const fontSelect = document.getElementById('fontSelect');
            const boldCheck = document.getElementById('boldCheck');
            const italicCheck = document.getElementById('italicCheck');
            
            fontSelect.addEventListener('change', updateFontPreview);
            boldCheck.addEventListener('change', updateFontPreview);
            italicCheck.addEventListener('change', updateFontPreview);
        }
        
        function updateFontPreview() {
            const fontSelect = document.getElementById('fontSelect');
            const boldCheck = document.getElementById('boldCheck');
            const italicCheck = document.getElementById('italicCheck');
            const previewText = document.getElementById('previewText');
            const variantStatus = document.getElementById('variantStatus');
            
            const fontFamily = fontSelect.value;
            const bold = boldCheck.checked;
            const italic = italicCheck.checked;
            
            // Update checkbox states based on availability
            if (detector && detector.initialized) {
                const boldAvailable = detector.isVariantAvailable(fontFamily, 'bold') || 
                                    detector.isVariantAvailable(fontFamily, 'boldItalic');
                const italicAvailable = detector.isVariantAvailable(fontFamily, 'italic') || 
                                      detector.isVariantAvailable(fontFamily, 'boldItalic');
                
                boldCheck.disabled = !boldAvailable;
                italicCheck.disabled = !italicAvailable;
                
                // Update status
                let variant = 'regular';
                if (bold && italic) variant = 'boldItalic';
                else if (bold) variant = 'bold';
                else if (italic) variant = 'italic';
                
                const variantAvailable = detector.isVariantAvailable(fontFamily, variant);
                
                if (variantAvailable) {
                    variantStatus.textContent = `✓ Using actual font file for ${fontFamily} ${variant}`;
                    variantStatus.className = 'status success';
                    
                    // Load the font variant
                    detector.loadFontVariant(fontFamily, bold, italic);
                } else {
                    variantStatus.textContent = `⚠ Font variant ${fontFamily} ${variant} not available`;
                    variantStatus.className = 'status warning';
                }
            }
            
            // Update preview text style
            previewText.style.fontFamily = `"${fontFamily}", sans-serif`;
            previewText.style.fontWeight = bold ? 'bold' : 'normal';
            previewText.style.fontStyle = italic ? 'italic' : 'normal';
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeTest);
    </script>
</body>
</html>
