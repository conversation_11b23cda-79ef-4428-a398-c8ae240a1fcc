<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Settings - AI Image Generator</title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        .credits-info {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    text-align: center;
}

.current-credits {
    font-size: 24px;
    color: #fff;
    margin-bottom: 16px;
}

.buy-credits-btn {
    background: #ff1cf7;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.credits-history {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 24px;
}

.credits-history h3 {
    color: #fff;
    margin: 0 0 16px;
    font-size: 18px;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    color: #fff;
}

.history-table th,
.history-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #333;
}

.history-table th {
    color: #666;
    font-weight: normal;
    font-size: 14px;
}

.history-table td {
    color: #fff;
}

.credit-add {
    color: #4CAF50;
}

.credit-use {
    color: #f44336;
}

.credits-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.credits-header h2 {
    color: #fff;
    margin: 0;
}


        .profile-container {
            max-width: 800px;
            margin: 80px auto 0;
            padding: 0 20px;
        }

        .profile-header {
            margin-bottom: 2rem;
        }

        .profile-header h1 {
            font-size: 24px;
            margin: 0;
            color: #fff;
        }

        .profile-header p {
            color: #666;
            margin: 4px 0 0;
        }

        .credits-info {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            text-align: center;
        }

        .current-credits {
            font-size: 24px;
            color: #fff;
            margin-bottom: 16px;
        }

        .buy-credits-btn {
            background: #ff1cf7;
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .credits-history {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 24px;
        }

        .credits-history h3 {
            color: #fff;
            margin: 0 0 16px;
            font-size: 18px;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            color: #fff;
        }

        .history-table th,
        .history-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #333;
        }

        .history-table th {
            color: #666;
            font-weight: normal;
            font-size: 14px;
        }

        .history-table td {
            color: #fff;
        }

        .credit-add {
            color: #4CAF50;
        }

        .credit-use {
            color: #f44336;
        }

        .credits-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .credits-header h2 {
            color: #fff;
            margin: 0;
        }

        .profile-tabs {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #333;
        }

        .profile-tab {
            padding: 8px 0;
            color: #666;
            cursor: pointer;
            position: relative;
            background: none;
            border: none;
            font-size: 14px;
        }

        .profile-tab.active {
            color: #fff;
        }

        .profile-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff1cf7;
        }

        .tab-content {
            display: none;
            background: #1a1a1a;
            border-radius: 8px;
            padding: 24px;
        }

        .tab-content.active {
            display: block;
        }

        .field-group {
            margin-bottom: 24px;
        }

        .field-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .field-value {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .field-text {
            color: #fff;
            font-size: 16px;
        }

        .edit-button {
            padding: 6px 12px;
            background: #333;
            border: none;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
            font-size: 14px;
        }

        .edit-button:hover {
            background: #444;
        }

        .subscription-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            background: #1a472e;
            color: #4caf50;
        }

        .subscription-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
        }

        .credits-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .credits-count {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .credits-number {
            font-size: 32px;
            font-weight: bold;
            color: #fff;
        }

        .credits-warning {
            background: #332b00;
            border: 1px solid #665500;
            padding: 16px;
            border-radius: 4px;
            margin: 24px 0;
            color: #ffb300;
        }

        .credits-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 24px;
        }

        .credits-table th {
            text-align: left;
            padding: 12px;
            border-bottom: 1px solid #333;
            color: #666;
            font-weight: normal;
        }

        .credits-table td {
            padding: 12px;
            border-bottom: 1px solid #333;
            color: #fff;
        }

        .primary-button {
            background: #ff1cf7;
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .secondary-button {
            background: #333;
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .credits-section {
            background: #1E1E1E;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .credits-display {
            font-size: 24px;
            margin: 16px 0;
        }

        #userCredits {
            color: #ff1cf7;
            font-weight: bold;
        }

        .buy-credits-btn {
            background: #ff1cf7;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .buy-credits-btn:hover {
            background: #ff45f9;
        }

        .credits-popup {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .credits-popup-content {
            background: #1E1E1E;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            padding: 24px;
            color: #fff;
        }

        .credits-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .credits-popup-header h2 {
            margin: 0;
            font-size: 24px;
        }

        .credits-slider {
            width: 100%;
            margin: 20px 0;
        }

        .credits-info {
            background: #2A2A2A;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            text-align: center;
        }

        .credits-amount {
            font-size: 48px;
            font-weight: bold;
            color: #ff1cf7;
            margin: 24px 0;
        }

        .credits-expiry {
            background: #2A2A2A;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }

        .credits-details {
            margin-top: 32px;
        }

        .credits-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .credits-table th,
        .credits-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #333;
        }

        .credits-table th {
            color: #999;
            font-weight: normal;
        }

        .credits-history {
            margin-top: 2rem;
        }
        .credits-table {
            width: 100%;
            overflow-x: auto;
        }
        .credits-table table {
            width: 100%;
            border-collapse: collapse;
        }
        .credits-table th, .credits-table td {
            padding: 0.8rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .credits-table th {
            background-color: #1a1a1a;
            color: #fff;
        }
        .credit-add {
            color: #4CAF50;
        }
        .credit-use {
            color: #f44336;
        }

        .credits-selector {
            margin-top: 2rem;
        }

        .slider-container {
            margin: 2rem 0;
            position: relative;
        }

        .slider-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 0.5rem;
            color: #666;
        }

        input[type="range"] {
            width: 100%;
            background: #333;
            height: 4px;
            border-radius: 2px;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: #ff1cf7;
            border-radius: 50%;
            cursor: pointer;
        }

        .selected-credits {
            text-align: center;
            font-size: 24px;
            color: #fff;
            margin: 1rem 0;
        }

        #selectedCredits {
            color: #ff1cf7;
            font-weight: bold;
        }

        .purchase-button {
            display: block;
          
            padding: 1rem;
            background: #ff1cf7;
            color: white;
            text-align: center;
            border-radius: 8px;
            text-decoration: none;
            margin-top: 2rem;
            font-weight: bold;
            transition: background 0.3s;
        }

        .purchase-button:hover {
            background: #d600ce;
        }

        .close-button {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            color: #666;
            font-size: 24px;
            cursor: pointer;
        }

        .close-button:hover {
            color: #fff;
        }
    </style>
</head>
<body>
    <div id="topbar"></div>

    <div class="profile-container">
        <div class="profile-header">
            <h1>Profile Settings</h1>
            <p>Manage your personal information</p>
        </div>

        <div class="profile-tabs">
            <button class="profile-tab" onclick="switchTab('credits')">Credits</button>
            <button class="profile-tab" onclick="switchTab('personal')">Personal Info</button>
            <!-- Subscription tab hidden for future use -->
            <button class="profile-tab" onclick="switchTab('subscription')" style="display: none;">Subscription</button>
        </div>

        <!-- Credits Tab -->
        <div id="credits" class="tab-content active">
            <div class="credits-header">
                <h2>My Credits</h2>
                <a href="#" style="color: #ff1cf7;">About Credits?</a>
            </div>
            <div class="credits-info">
                <div class="current-credits">
                    <span id="totalCredits">Loading...</span> Credits Available
                    <button onclick="showCreditsPopup()" class="buy-credits-btn">Buy more Credits</button>
                </div>
            </div>
            <div class="info-section">
                <h3>Usage Details</h3>
                <div class="usage-stats">
                    <div class="stat-item">
                        <label>Stickers Generated:</label>
                        <span id="stickersGenerated">0</span>
                    </div>
                    <div class="stat-item">
                        <label>Collections:</label>
                        <span id="collectionsCount">0</span>
                    </div>
                </div>
            </div>
            <div class="credits-history">
                <h3>Credits History</h3>
                <table class="history-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody id="creditsHistory">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Personal Info Tab -->
        <div id="personal" class="tab-content">
            <div class="field-group">
                <div class="field-label">Nickname</div>
                <div class="field-value">
                    <span id="nickname" class="field-text"></span>
                    <button class="edit-button" onclick="editField('nickname')">Edit</button>
                </div>
            </div>

            <div class="field-group">
                <div class="field-label">Bio</div>
                <div class="field-value">
                    <span id="bio" class="field-text">Briefly introduce yourself in this space...</span>
                    <button class="edit-button" onclick="editField('bio')">Edit</button>
                </div>
            </div>

            <div class="field-group">
                <div class="field-label">Social Media Links</div>
                <div class="field-value">
                    <div class="field-text">No social media links added</div>
                    <button class="edit-button" onclick="addSocialMedia()">Add</button>
                </div>
            </div>

            <div class="field-group">
                <div class="field-label">Email Address</div>
                <div class="field-value">
                    <span id="email" class="field-text"></span>
                    <button class="edit-button" onclick="modifyEmail()">Modify Email</button>
                </div>
            </div>
        </div>

        <!-- Subscription Tab -->
        <div id="subscription" class="tab-content">
            <div class="subscription-header">
                <h2>Your Standard Plan</h2>
            </div>
            <div class="subscription-content">
                <div class="subscription-info">
                    <div class="info-section">
                        <h3>Plan Features</h3>
                        <ul class="plan-features">
                            <li>1000 credits</li>
                            <li>General commercial terms</li>
                            <li>Optional more credits on top</li>
                            <li>3 concurrent fast jobs</li>
                            <li>Unlimited Collections</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Credits Popup -->
    <div class="credits-popup" id="creditsPopup">
        <div class="credits-popup-content">
            <h2>Buy Credits</h2>
            <div class="credits-selector">
                <div class="slider-container">
                    <input type="range" id="creditsSlider" min="100" max="1000" step="100" value="100">
                    <div class="slider-labels">
                        <span>100</span>
                        <span>1000</span>
                    </div>
                </div>
                <div class="selected-credits">
                    <span id="selectedCredits">100</span> Credits
                </div>
                <a href="#" id="purchaseLink" class="purchase-button">Purchase Credits</a>
            </div>
            <button class="close-button" onclick="hideCreditsPopup()">×</button>
        </div>
    </div>

    <script type="module">
    import { createTopbar } from '/js/components/Topbar.js';
    
    // Initialize topbar and credits popup
    document.addEventListener('DOMContentLoaded', async () => {
        await createTopbar();
    });
</script>

<script>
    // Credits popup functionality
    function showCreditsPopup() {
        window.creditsPopup.show();
    }
</script>
    <script src="/js/credits-popup.js"></script>
    <script>
        let lastKnownCredits = null;
        let isInitialLoad = true;

        function formatCredits(credits) {
            if (credits === null) return 'Loading...';
            return credits === 123654 ? 'Unlimited' : credits;
        }

        function updateAllCreditDisplays(credits, force = false) {
            // Don't update if we're getting a 0 value after having a valid value
            if (!force && !isInitialLoad && credits === 0 && lastKnownCredits !== null && lastKnownCredits > 0) {
                return;
            }

            const formattedCredits = formatCredits(credits);
            lastKnownCredits = credits;
            isInitialLoad = false;
            
            // Update profile credits
            const totalCreditsElement = document.getElementById('totalCredits');
            if (totalCreditsElement) {
                totalCreditsElement.textContent = formattedCredits;
            }
        }

        function syncWithTopbarCredits() {
            const topbarCredits = document.getElementById('topbarCredits');
            if (topbarCredits) {
                const creditsText = topbarCredits.textContent;
                if (creditsText === 'Unlimited') {
                    updateAllCreditDisplays(123654, true);
                } else {
                    const credits = parseInt(creditsText) || 0;
                    updateAllCreditDisplays(credits, true);
                }
            }
        }

        // Initialize page with a single source of truth for user data
        async function initializePage() {
            try {
                // First try to sync with topbar if it's already loaded
                syncWithTopbarCredits();

                const response = await fetch('/api/auth/user', {
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error('Failed to load user data');
                }

                const userData = await response.json();
                
                if (userData.credits !== 456321) {
                    const credits = userData.credits;
                    updateAllCreditDisplays(credits, true);
                    
                    // Show credit elements
                    const creditTab = document.querySelector('button[onclick="switchTab(\'credits\')"]');
                    const creditsContent = document.getElementById('credits');
                    const creditsContainer = document.querySelector('.credits-container');
                    
                    if (creditTab) creditTab.style.display = 'block';
                    if (creditsContent) creditsContent.style.display = 'block';
                    if (creditsContainer) creditsContainer.style.display = 'flex';
                }

                // Update user info
                const nameElement = document.getElementById('userName');
                const emailElement = document.getElementById('userEmail');
                if (nameElement) nameElement.textContent = userData.name || '';
                if (emailElement) emailElement.textContent = userData.email || '';

                // Load credit history if credits are not hidden
                if (userData.credits !== 456321) {
                    loadCreditHistory();
                }

            } catch (error) {
                console.error('Error initializing page:', error);
            }
        }

        // Listen for credit updates from topbar
        window.addEventListener('creditsUpdated', (event) => {
            if (event.detail && typeof event.detail.credits !== 'undefined') {
                updateAllCreditDisplays(event.detail.credits);
            }
        });

        // Watch for changes in the topbar credits
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.target.id === 'topbarCredits') {
                    syncWithTopbarCredits();
                }
            });
        });

        // Start observing once the topbar is loaded
        function observeTopbarCredits() {
            const topbarCredits = document.getElementById('topbarCredits');
            if (topbarCredits) {
                observer.observe(topbarCredits, { 
                    characterData: true, 
                    childList: true,
                    subtree: true 
                });
                // Initial sync
                syncWithTopbarCredits();
            } else {
                // If topbar isn't loaded yet, try again in a moment
                setTimeout(observeTopbarCredits, 100);
            }
        }

        // Initialize the page when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initializePage();
            observeTopbarCredits();
            
            // Switch to appropriate tab if specified in URL
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get('tab');
            if (tab) {
                switchTab(tab);
            }
        });
    </script>
    <script>
        // Load credit products configuration
        let creditProducts;
        fetch('/api/subscription/credits/packages')
            .then(response => response.json())
            .then(products => {
                creditProducts = products;
                updatePurchaseLink(); // Update link once products are loaded
            })
            .catch(error => console.error('Error loading credit products:', error));

        function showCreditsPopup() {
            document.getElementById('creditsPopup').style.display = 'flex';
            updatePurchaseLink();
        }

        function hideCreditsPopup() {
            document.getElementById('creditsPopup').style.display = 'none';
        }

        function updatePurchaseLink() {
            if (!creditProducts) return; // Wait for products to load

            const selectedCredits = parseInt(document.getElementById('creditsSlider').value);
            const product = creditProducts.find(p => p.credits === selectedCredits);
            
            if (product) {
                document.getElementById('purchaseLink').href = product.purchaseUrl;
                document.getElementById('selectedCredits').textContent = selectedCredits;
            }
        }

        // Initialize slider
        document.getElementById('creditsSlider').addEventListener('input', updatePurchaseLink);

        // Close popup when clicking outside
        document.getElementById('creditsPopup').addEventListener('click', function(e) {
            if (e.target === this) {
                hideCreditsPopup();
            }
        });

        // Tab switching functionality
        function switchTab(tabId) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.profile-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(tabId).classList.add('active');
            document.querySelector(`.profile-tab[onclick="switchTab('${tabId}')"]`).classList.add('active');
        }

        // Load user data
        fetch('/api/auth/me')
            .then(response => response.json())
            .then(user => {
                document.getElementById('nickname').textContent = user.name;
                document.getElementById('email').textContent = user.email;
                if (user.bio) {
                    document.getElementById('bio').textContent = user.bio;
                }
            })
            .catch(error => console.error('Error loading user data:', error));

        function editField(field) {
            const element = document.getElementById(field);
            const currentValue = element.textContent;
            const input = document.createElement('input');
            input.value = currentValue;
            input.style.width = '100%';
            input.style.background = '#333';
            input.style.border = 'none';
            input.style.padding = '8px';
            input.style.borderRadius = '4px';
            input.style.color = '#fff';
            
            input.onblur = function() {
                updateField(field, input.value);
            };
            
            element.textContent = '';
            element.appendChild(input);
            input.focus();
        }

        function updateField(field, value) {
            fetch('/api/auth/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ [field]: value }),
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById(field).textContent = value;
            })
            .catch(error => console.error('Error updating field:', error));
        }

        function addSocialMedia() {
            alert('Social media link addition functionality coming soon!');
        }

        function modifyEmail() {
            alert('Email modification functionality coming soon!');
        }

        async function loadUserProfile() {
            try {
                const response = await fetch('/api/auth/me');
                const user = await response.json();
                
                // Update all credit displays
                const creditDisplays = ['totalCredits'];
                creditDisplays.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = user.credits || 0;
                    }
                });

                // Update usage statistics
                if (user.statistics) {
                    document.getElementById('stickersGenerated').textContent = user.statistics.generationsCount || 0;
                    document.getElementById('collectionsCount').textContent = user.statistics.collectionsCount || 0;
                }
                
                // Update other profile information
                document.getElementById('nickname').textContent = user.name || '';
                document.getElementById('email').textContent = user.email || '';
                
                // Load credit history
                await loadCreditHistory();
            } catch (error) {
                console.error('Error loading profile:', error);
            }
        }

        // Update all credit displays when credits change
        function updateAllCreditDisplays(credits) {
            const creditDisplays = ['totalCredits'];
            creditDisplays.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = credits;
                }
            });
        }

        // Listen for credit updates
        window.addEventListener('creditsUpdated', (event) => {
            if (event.detail && typeof event.detail.credits === 'number') {
                updateAllCreditDisplays(event.detail.credits);
            }
        });

        async function loadCreditHistory() {
            try {
                const response = await fetch('/api/credits/history');
                const history = await response.json();
                
                const tbody = document.getElementById('creditsHistory');
                tbody.innerHTML = ''; // Clear existing history
                
                if (Array.isArray(history)) {
                    history.forEach(item => {
                        const row = document.createElement('tr');
                        const timestamp = new Date(item.timestamp);
                        const formattedAmount = item.amount === 123654 ? 'Unlimited' : 
                                                  (item.amount > 0 ? '+' : '') + item.amount;
                        row.innerHTML = `
                            <td>${timestamp.toLocaleString()}</td>
                            <td>${item.type}</td>
                            <td class="${item.amount > 0 ? 'credit-add' : 'credit-use'}">${formattedAmount}</td>
                            <td>${item.details}</td>
                        `;
                        tbody.appendChild(row);
                    });
                }
            } catch (error) {
                console.error('Error loading credit history:', error);
                const historyTable = document.getElementById('creditsHistory');
                historyTable.innerHTML = '<tr><td colspan="4">Error loading credit history</td></tr>';
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            loadUserProfile();
            
            // Check URL parameters for active tab
            const urlParams = new URLSearchParams(window.location.search);
            const activeTab = urlParams.get('tab') || 'credits';
            switchTab(activeTab);
            
            // Load credit history if on credits tab
            if (activeTab === 'credits') {
                loadCreditHistory();
            }
        });
    </script>
</body>
</html>
