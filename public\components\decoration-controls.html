<div class="tool-section">
    <h3>Decoration Effects</h3>
    <div class="effect-toggles">
        <div class="toggle-container">
            <span>Horizontal Lines</span>
            <label class="switch">
                <input type="checkbox" id="horizontalLinesToggle" class="decoration-checkbox" data-decoration-type="horizontalLines">
                <span class="slider round"></span>
            </label>
        </div>
        <div class="toggle-container">
            <span>Color Cut</span>
            <label class="switch">
                <input type="checkbox" id="colorCutToggle" class="decoration-checkbox" data-decoration-type="colorCut">
                <span class="slider round"></span>
            </label>
        </div>
        <div class="toggle-container">
            <span>Oblique Lines</span>
            <label class="switch">
                <input type="checkbox" id="obliqueLinesToggle" class="decoration-checkbox" data-decoration-type="obliqueLines">
                <span class="slider round"></span>
            </label>
        </div>
        <div class="toggle-container">
            <span>Fading Color Cut</span>
            <label class="switch">
                <input type="checkbox" id="fadingColorCutToggle" class="decoration-checkbox" data-decoration-type="fadingColorCut">
                <span class="slider round"></span>
            </label>
        </div>
    </div>
</div>

<!-- Decoration Effects Controls Panels -->
<div id="horizontalLinesControlsContent" class="decoration-control-panel" style="display: none;"></div>
<div id="colorCutControlsContent" class="decoration-control-panel" style="display: none;"></div>
<div id="obliqueLinesControlsContent" class="decoration-control-panel" style="display: none;"></div>
<div id="fadingColorCutControlsContent" class="decoration-control-panel" style="display: none;"></div>
