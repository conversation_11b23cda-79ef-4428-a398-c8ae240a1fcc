<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>AI Design Generator</title>
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f7;
      display: flex;
      height: 100vh;
      overflow: hidden;
    }

    .sidebar {
      width: 300px;
      background: #fff;
      padding: 24px;
      box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }

    .section {
      margin-bottom: 24px;
    }

    .section h3 {
      margin: 0 0 10px;
      font-size: 16px;
      color: #333;
    }

    input[type="text"], select, button {
      width: 100%;
      padding: 10px;
      border-radius: 8px;
      border: 1px solid #ccc;
      margin-top: 6px;
      font-size: 14px;
    }

    .toggle-group {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    .toggle-group input {
      margin-right: 6px;
    }

    .template-grid {
      display: flex;
      gap: 10px;
      overflow-x: auto;
    }

    .template-grid .template {
      width: 60px;
      height: 60px;
      border: 2px solid transparent;
      border-radius: 8px;
      background-color: #eee;
      cursor: pointer;
    }

    .template.selected {
      border-color: #0078f2;
    }

    .generate-btn {
      background-color: #0078f2;
      color: white;
      border: none;
      margin-top: auto;
      font-weight: bold;
    }

    .main-panel {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      padding: 40px;
      background-color: #fafafa;
    }

    .preview-container {
      flex-grow: 1;
      max-width: 800px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border-radius: 20px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }

    .prompt-bar {
      width: 100%;
      max-width: 800px;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 20px;
    }

    .prompt-bar label {
      font-size: 16px;
      white-space: nowrap;
    }

    .prompt-bar input {
      flex: 1;
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="section">
      <h3>Include Text</h3>
      <input type="checkbox" id="includeText" checked />
    </div>
    <div class="section">
      <h3>Text for the Design</h3>
      <input type="text" placeholder="Let's Go!" />
    </div>
    <div class="section">
      <h3>Choose a Prompt Template</h3>
      <div class="template-grid">
        <div class="template selected"></div>
        <div class="template"></div>
        <div class="template"></div>
        <div class="template"></div>
      </div>
    </div>
    <div class="section">
      <h3>Choose Model</h3>
      <select>
        <option>flux-yarn-art</option>
      </select>
    </div>
    <div class="section">
      <h3>Choose Theme</h3>
      <select>
        <option>Select a theme...</option>
      </select>
    </div>
    <div class="section">
      <h3>Background</h3>
      <div class="toggle-group">
        <label><input type="radio" name="bg" /> Light</label>
        <label><input type="radio" name="bg" /> Dark</label>
        <label><input type="radio" name="bg" /> Transparent</label>
      </div>
    </div>
    <button class="generate-btn">Generate Image</button>
  </div>
  <div class="main-panel">
    <div class="preview-container">
      <p style="color: #aaa">[ Image Preview Placeholder ]</p>
    </div>
    <div class="prompt-bar">
      <label for="prompt">What would you like to create?</label>
      <input type="text" id="prompt" placeholder="e.g. Cow" />
    </div>
  </div>
</body>
</html>
