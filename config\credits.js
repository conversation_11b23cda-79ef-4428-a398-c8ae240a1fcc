export const creditProducts = [
    {
        credits: 100,
        productId: 'wso_h0pdbg',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/vdcb4x/h0pdbg'
    },
    {
        credits: 200,
        productId: 'wso_g9brry',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/yhq5m5/g9brry'
    },
    {
        credits: 300,
        productId: 'wso_z073wm',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/bt8zt6/z073wm'
    },
    {
        credits: 400,
        productId: 'wso_k6bx56',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/xww2bb/k6bx56'
    },
    {
        credits: 500,
        productId: 'wso_mnn103',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/lzyt7t/mnn103'
    },
    {
        credits: 600,
        productId: 'wso_p6t554',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/p6ww4z/p6t554'
    },
    {
        credits: 700,
        productId: 'wso_pc7kvg',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/wrsc7x/pc7kvg'
    },
    {
        credits: 800,
        productId: 'wso_lzd4gh',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/p5x9b0/lzd4gh'
    },
    {
        credits: 900,
        productId: 'wso_yhcpx7',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/lm6xrs/yhcpx7'
    },
    {
        credits: 1000,
        productId: 'wso_n7w5lr',
        purchaseUrl: 'https://warriorplus.com/o2/buy/jvrcjg/kbnt1j/n7w5lr'
    }
];
